"""
工业智能体平台 - 维护服务
实现预测性维护、设备监控、维护计划等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import numpy as np
import pandas as pd
from dataclasses import dataclass
import uuid
from enum import Enum
import httpx

# 导入算法模块
import sys
sys.path.append('../llm-service')
from algorithms import PredictiveMaintenanceModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
timescale_pool = None
redis_client = None
kafka_producer = None
maintenance_model = PredictiveMaintenanceModel()

class MaintenanceType(str, Enum):
    PREVENTIVE = "preventive"
    PREDICTIVE = "predictive"
    CORRECTIVE = "corrective"
    EMERGENCY = "emergency"

class MaintenanceStatus(str, Enum):
    PLANNED = "planned"
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"

class AlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, timescale_pool, redis_client, kafka_producer

    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )

    # TimescaleDB连接
    timescale_pool = await asyncpg.create_pool(
        host=os.getenv("TIMESCALE_HOST", "localhost"),
        port=int(os.getenv("TIMESCALE_PORT", "5433")),
        user=os.getenv("TIMESCALE_USER", "admin"),
        password=os.getenv("TIMESCALE_PASSWORD", "admin123"),
        database=os.getenv("TIMESCALE_DB", "timeseries_data"),
        min_size=5,
        max_size=20
    )

    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )

    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()

    # 初始化数据库表
    await initialize_database()

    # 启动传感器数据消费者
    asyncio.create_task(start_sensor_data_consumer())

    # 启动预测维护任务
    asyncio.create_task(start_predictive_maintenance_task())

    logger.info("Maintenance service initialized")

    yield

    # 清理资源
    if db_pool:
        await db_pool.close()
    if timescale_pool:
        await timescale_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()

    logger.info("Maintenance service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="维护服务",
    description="预测性维护、设备监控、维护计划",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class MaintenanceTaskCreate(BaseModel):
    equipment_id: str
    task_type: MaintenanceType
    title: str
    description: str
    priority: int = 5  # 1-10, 1最高
    estimated_duration_hours: float
    required_skills: List[str] = []
    required_parts: List[Dict[str, Any]] = []
    scheduled_date: Optional[datetime] = None
    assigned_technician: Optional[str] = None

class MaintenanceTaskUpdate(BaseModel):
    status: Optional[MaintenanceStatus] = None
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    actual_duration_hours: Optional[float] = None
    completion_notes: Optional[str] = None
    parts_used: Optional[List[Dict[str, Any]]] = None
    next_maintenance_date: Optional[datetime] = None

class EquipmentHealthRequest(BaseModel):
    equipment_id: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

class MaintenanceAlert(BaseModel):
    equipment_id: str
    alert_type: str
    severity: AlertSeverity
    message: str
    recommended_action: str
    predicted_failure_time: Optional[datetime] = None
    confidence: float

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 维护任务表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_tasks (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                equipment_id VARCHAR(50) NOT NULL REFERENCES equipment(id),
                task_type VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                priority INTEGER DEFAULT 5,
                status VARCHAR(20) DEFAULT 'planned',
                estimated_duration_hours DECIMAL(5,2),
                actual_duration_hours DECIMAL(5,2),
                required_skills TEXT[],
                required_parts JSONB,
                parts_used JSONB,
                scheduled_date TIMESTAMP WITH TIME ZONE,
                actual_start_time TIMESTAMP WITH TIME ZONE,
                actual_end_time TIMESTAMP WITH TIME ZONE,
                assigned_technician VARCHAR(50),
                completion_notes TEXT,
                next_maintenance_date TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 维护历史表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_history (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                equipment_id VARCHAR(50) NOT NULL REFERENCES equipment(id),
                task_id UUID REFERENCES maintenance_tasks(id),
                maintenance_type VARCHAR(20) NOT NULL,
                start_time TIMESTAMP WITH TIME ZONE,
                end_time TIMESTAMP WITH TIME ZONE,
                duration_hours DECIMAL(5,2),
                technician VARCHAR(50),
                work_performed TEXT,
                parts_replaced JSONB,
                cost DECIMAL(10,2),
                downtime_minutes INTEGER,
                effectiveness_score DECIMAL(3,2),
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 设备健康评分表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS equipment_health_scores (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                equipment_id VARCHAR(50) NOT NULL REFERENCES equipment(id),
                assessment_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                overall_health_score DECIMAL(3,2),
                vibration_score DECIMAL(3,2),
                temperature_score DECIMAL(3,2),
                lubrication_score DECIMAL(3,2),
                electrical_score DECIMAL(3,2),
                mechanical_score DECIMAL(3,2),
                predicted_failure_probability DECIMAL(3,2),
                remaining_useful_life_days INTEGER,
                risk_level VARCHAR(20),
                recommendations TEXT[],
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 维护告警表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_alerts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                equipment_id VARCHAR(50) NOT NULL REFERENCES equipment(id),
                alert_type VARCHAR(50) NOT NULL,
                severity VARCHAR(20) NOT NULL,
                message TEXT NOT NULL,
                recommended_action TEXT,
                predicted_failure_time TIMESTAMP WITH TIME ZONE,
                confidence DECIMAL(3,2),
                status VARCHAR(20) DEFAULT 'active',
                acknowledged_by VARCHAR(50),
                acknowledged_at TIMESTAMP WITH TIME ZONE,
                resolved_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 备件库存表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS spare_parts_inventory (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                part_number VARCHAR(50) UNIQUE NOT NULL,
                part_name VARCHAR(200) NOT NULL,
                description TEXT,
                category VARCHAR(50),
                unit_cost DECIMAL(10,2),
                current_stock INTEGER DEFAULT 0,
                minimum_stock INTEGER DEFAULT 0,
                maximum_stock INTEGER DEFAULT 0,
                reorder_point INTEGER DEFAULT 0,
                lead_time_days INTEGER DEFAULT 7,
                supplier_id UUID REFERENCES suppliers(id),
                location VARCHAR(100),
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

async def start_sensor_data_consumer():
    """启动传感器数据消费者"""
    consumer = AIOKafkaConsumer(
        "sensor_data",
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        group_id="maintenance_service",
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))
    )

    await consumer.start()

    try:
        async for message in consumer:
            sensor_data = message.value
            await process_sensor_data(sensor_data)
    except Exception as e:
        logger.error(f"Error in sensor data consumer: {e}")
    finally:
        await consumer.stop()

async def process_sensor_data(sensor_data: Dict[str, Any]):
    """处理传感器数据"""
    try:
        equipment_id = sensor_data.get("equipment_id")
        if not equipment_id:
            return

        # 检查异常值
        temperature = sensor_data.get("temperature")
        vibration = sensor_data.get("vibration")

        alerts = []

        # 温度异常检测
        if temperature and temperature > 80:  # 假设80度为警戒线
            severity = AlertSeverity.HIGH if temperature > 90 else AlertSeverity.MEDIUM
            alerts.append(MaintenanceAlert(
                equipment_id=equipment_id,
                alert_type="temperature_high",
                severity=severity,
                message=f"设备温度异常：{temperature}°C",
                recommended_action="检查冷却系统，必要时停机检修",
                confidence=0.9
            ))

        # 振动异常检测
        if vibration and vibration > 10:  # 假设10为振动警戒线
            severity = AlertSeverity.HIGH if vibration > 15 else AlertSeverity.MEDIUM
            alerts.append(MaintenanceAlert(
                equipment_id=equipment_id,
                alert_type="vibration_high",
                severity=severity,
                message=f"设备振动异常：{vibration}mm/s",
                recommended_action="检查轴承和联轴器，进行动平衡校正",
                confidence=0.85
            ))

        # 保存告警
        for alert in alerts:
            await MaintenanceManager.create_alert(alert)

        # 更新设备健康评分
        await MaintenanceManager.update_equipment_health(equipment_id, sensor_data)

    except Exception as e:
        logger.error(f"Error processing sensor data: {e}")

async def start_predictive_maintenance_task():
    """启动预测维护任务"""
    while True:
        try:
            await asyncio.sleep(3600)  # 每小时执行一次
            await MaintenanceManager.run_predictive_analysis()
        except Exception as e:
            logger.error(f"Error in predictive maintenance task: {e}")
            await asyncio.sleep(300)  # 出错后5分钟重试

class MaintenanceManager:
    """维护管理器"""

    @staticmethod
    async def create_maintenance_task(task_data: MaintenanceTaskCreate) -> str:
        """创建维护任务"""
        try:
            task_id = str(uuid.uuid4())

            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO maintenance_tasks
                    (id, equipment_id, task_type, title, description, priority,
                     estimated_duration_hours, required_skills, required_parts,
                     scheduled_date, assigned_technician, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    """,
                    task_id,
                    task_data.equipment_id,
                    task_data.task_type.value,
                    task_data.title,
                    task_data.description,
                    task_data.priority,
                    task_data.estimated_duration_hours,
                    task_data.required_skills,
                    json.dumps(task_data.required_parts),
                    task_data.scheduled_date,
                    task_data.assigned_technician,
                    datetime.utcnow()
                )

            # 发送事件
            await kafka_producer.send("maintenance_events", {
                "type": "task_created",
                "task_id": task_id,
                "equipment_id": task_data.equipment_id,
                "task_type": task_data.task_type.value,
                "priority": task_data.priority,
                "timestamp": datetime.utcnow().isoformat()
            })

            logger.info(f"Created maintenance task: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"Error creating maintenance task: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def update_maintenance_task(task_id: str, update_data: MaintenanceTaskUpdate) -> bool:
        """更新维护任务"""
        try:
            async with db_pool.acquire() as conn:
                # 构建更新字段
                update_fields = []
                params = []
                param_count = 1

                for field, value in update_data.dict(exclude_unset=True).items():
                    if value is not None:
                        update_fields.append(f"{field} = ${param_count}")
                        params.append(value.value if isinstance(value, Enum) else value)
                        param_count += 1

                if update_fields:
                    update_fields.append(f"updated_at = ${param_count}")
                    params.append(datetime.utcnow())
                    params.append(task_id)

                    query = f"UPDATE maintenance_tasks SET {', '.join(update_fields)} WHERE id = ${param_count + 1}"
                    result = await conn.execute(query, *params)

                    # 如果任务完成，记录到历史表
                    if update_data.status == MaintenanceStatus.COMPLETED:
                        await MaintenanceManager._record_maintenance_history(task_id)

                    # 发送事件
                    await kafka_producer.send("maintenance_events", {
                        "type": "task_updated",
                        "task_id": task_id,
                        "status": update_data.status.value if update_data.status else None,
                        "timestamp": datetime.utcnow().isoformat()
                    })

                    return True

                return False

        except Exception as e:
            logger.error(f"Error updating maintenance task: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _record_maintenance_history(task_id: str):
        """记录维护历史"""
        async with db_pool.acquire() as conn:
            # 获取任务信息
            task = await conn.fetchrow(
                "SELECT * FROM maintenance_tasks WHERE id = $1", task_id
            )

            if task and task['actual_start_time'] and task['actual_end_time']:
                await conn.execute(
                    """
                    INSERT INTO maintenance_history
                    (equipment_id, task_id, maintenance_type, start_time, end_time,
                     duration_hours, technician, work_performed, parts_replaced,
                     downtime_minutes, notes)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    """,
                    task['equipment_id'],
                    task_id,
                    task['task_type'],
                    task['actual_start_time'],
                    task['actual_end_time'],
                    task['actual_duration_hours'],
                    task['assigned_technician'],
                    task['description'],
                    task['parts_used'],
                    int((task['actual_end_time'] - task['actual_start_time']).total_seconds() / 60),
                    task['completion_notes']
                )

    @staticmethod
    async def create_alert(alert: MaintenanceAlert) -> str:
        """创建维护告警"""
        try:
            alert_id = str(uuid.uuid4())

            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO maintenance_alerts
                    (id, equipment_id, alert_type, severity, message,
                     recommended_action, predicted_failure_time, confidence, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                    alert_id,
                    alert.equipment_id,
                    alert.alert_type,
                    alert.severity.value,
                    alert.message,
                    alert.recommended_action,
                    alert.predicted_failure_time,
                    alert.confidence,
                    datetime.utcnow()
                )

            # 发送事件
            await kafka_producer.send("maintenance_events", {
                "type": "alert_created",
                "alert_id": alert_id,
                "equipment_id": alert.equipment_id,
                "severity": alert.severity.value,
                "message": alert.message,
                "timestamp": datetime.utcnow().isoformat()
            })

            # 如果是高危告警，自动创建维护任务
            if alert.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]:
                await MaintenanceManager._auto_create_maintenance_task(alert)

            logger.info(f"Created maintenance alert: {alert_id}")
            return alert_id

        except Exception as e:
            logger.error(f"Error creating maintenance alert: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _auto_create_maintenance_task(alert: MaintenanceAlert):
        """根据告警自动创建维护任务"""
        try:
            task_data = MaintenanceTaskCreate(
                equipment_id=alert.equipment_id,
                task_type=MaintenanceType.PREDICTIVE,
                title=f"紧急维护 - {alert.alert_type}",
                description=f"告警信息：{alert.message}\n建议措施：{alert.recommended_action}",
                priority=1 if alert.severity == AlertSeverity.CRITICAL else 2,
                estimated_duration_hours=2.0,
                scheduled_date=datetime.utcnow() + timedelta(hours=1)
            )

            await MaintenanceManager.create_maintenance_task(task_data)

        except Exception as e:
            logger.error(f"Error auto-creating maintenance task: {e}")

    @staticmethod
    async def update_equipment_health(equipment_id: str, sensor_data: Dict[str, Any]):
        """更新设备健康评分"""
        try:
            # 计算各项健康指标
            temperature = sensor_data.get("temperature", 25)
            vibration = sensor_data.get("vibration", 0)
            power = sensor_data.get("power_consumption", 100)

            # 温度评分 (0-1)
            temp_score = max(0, min(1, (100 - temperature) / 75))

            # 振动评分 (0-1)
            vib_score = max(0, min(1, (20 - vibration) / 20))

            # 电气评分 (基于功耗变化)
            electrical_score = 0.8  # 简化计算

            # 机械评分
            mechanical_score = (temp_score + vib_score) / 2

            # 润滑评分 (模拟)
            lubrication_score = 0.85

            # 综合健康评分
            overall_score = (temp_score * 0.3 + vib_score * 0.3 +
                           electrical_score * 0.2 + mechanical_score * 0.2)

            # 预测故障概率
            failure_prob = 1 - overall_score

            # 剩余使用寿命 (天)
            rul_days = int(overall_score * 365)

            # 风险等级
            if overall_score > 0.8:
                risk_level = "low"
            elif overall_score > 0.6:
                risk_level = "medium"
            elif overall_score > 0.4:
                risk_level = "high"
            else:
                risk_level = "critical"

            # 生成建议
            recommendations = MaintenanceManager._generate_health_recommendations(
                overall_score, temp_score, vib_score
            )

            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO equipment_health_scores
                    (equipment_id, overall_health_score, vibration_score, temperature_score,
                     lubrication_score, electrical_score, mechanical_score,
                     predicted_failure_probability, remaining_useful_life_days,
                     risk_level, recommendations)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    """,
                    equipment_id,
                    overall_score,
                    vib_score,
                    temp_score,
                    lubrication_score,
                    electrical_score,
                    mechanical_score,
                    failure_prob,
                    rul_days,
                    risk_level,
                    recommendations
                )

            # 缓存最新健康评分
            await redis_client.setex(
                f"equipment_health:{equipment_id}",
                3600,  # 1小时过期
                json.dumps({
                    "overall_score": overall_score,
                    "risk_level": risk_level,
                    "rul_days": rul_days,
                    "last_updated": datetime.utcnow().isoformat()
                })
            )

        except Exception as e:
            logger.error(f"Error updating equipment health: {e}")

    @staticmethod
    def _generate_health_recommendations(overall_score: float, temp_score: float, vib_score: float) -> List[str]:
        """生成健康建议"""
        recommendations = []

        if overall_score < 0.4:
            recommendations.append("设备健康状况严重，建议立即停机检修")
        elif overall_score < 0.6:
            recommendations.append("设备健康状况较差，建议安排维护检查")
        elif overall_score < 0.8:
            recommendations.append("设备健康状况一般，建议加强监控")

        if temp_score < 0.6:
            recommendations.append("温度偏高，检查冷却系统")

        if vib_score < 0.6:
            recommendations.append("振动异常，检查轴承和联轴器")

        return recommendations

    @staticmethod
    async def run_predictive_analysis():
        """运行预测性维护分析"""
        try:
            # 获取所有活跃设备
            async with db_pool.acquire() as conn:
                equipment_list = await conn.fetch(
                    "SELECT id, equipment_name FROM equipment WHERE status = 'active'"
                )

            for equipment in equipment_list:
                equipment_id = equipment['id']

                # 获取最近的传感器数据
                sensor_data = await MaintenanceManager._get_recent_sensor_data(equipment_id)

                if len(sensor_data) > 10:  # 需要足够的数据进行分析
                    # 运行预测模型
                    try:
                        prediction = maintenance_model.predict_failure(sensor_data)

                        # 根据预测结果创建告警
                        if prediction["risk_level"] == "high":
                            alert = MaintenanceAlert(
                                equipment_id=equipment_id,
                                alert_type="predictive_failure",
                                severity=AlertSeverity.HIGH,
                                message=f"预测设备可能在{prediction['remaining_useful_life']:.1f}小时内发生故障",
                                recommended_action="建议安排预防性维护",
                                predicted_failure_time=datetime.utcnow() + timedelta(hours=prediction['remaining_useful_life']),
                                confidence=0.8
                            )
                            await MaintenanceManager.create_alert(alert)

                    except Exception as e:
                        logger.warning(f"Predictive analysis failed for equipment {equipment_id}: {e}")

            logger.info("Completed predictive maintenance analysis")

        except Exception as e:
            logger.error(f"Error in predictive analysis: {e}")

    @staticmethod
    async def _get_recent_sensor_data(equipment_id: str, hours: int = 24) -> pd.DataFrame:
        """获取最近的传感器数据"""
        try:
            async with timescale_pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT time, value, unit, sensor_id
                    FROM sensor_data
                    WHERE source_id = $1 AND time >= NOW() - INTERVAL '%s hours'
                    ORDER BY time DESC
                    """,
                    equipment_id, hours
                )

                if rows:
                    data = []
                    for row in rows:
                        data.append({
                            'timestamp': row['time'],
                            'equipment_id': equipment_id,
                            'sensor_id': row['sensor_id'],
                            'value': row['value'],
                            'unit': row['unit']
                        })

                    df = pd.DataFrame(data)

                    # 透视表，将不同传感器的数据作为列
                    pivot_df = df.pivot_table(
                        index=['timestamp', 'equipment_id'],
                        columns='sensor_id',
                        values='value',
                        aggfunc='mean'
                    ).reset_index()

                    # 重命名列
                    pivot_df.columns.name = None
                    column_mapping = {
                        'temperature_sensor': 'temperature',
                        'vibration_sensor': 'vibration',
                        'power_sensor': 'power_consumption'
                    }
                    pivot_df = pivot_df.rename(columns=column_mapping)

                    return pivot_df
                else:
                    return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting sensor data for {equipment_id}: {e}")
            return pd.DataFrame()

    @staticmethod
    async def get_equipment_health_status(equipment_id: str) -> Dict[str, Any]:
        """获取设备健康状态"""
        try:
            # 先从缓存获取
            cached_health = await redis_client.get(f"equipment_health:{equipment_id}")
            if cached_health:
                return json.loads(cached_health)

            # 从数据库获取最新健康评分
            async with db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    """
                    SELECT * FROM equipment_health_scores
                    WHERE equipment_id = $1
                    ORDER BY assessment_date DESC
                    LIMIT 1
                    """,
                    equipment_id
                )

                if row:
                    health_data = dict(row)

                    # 缓存结果
                    await redis_client.setex(
                        f"equipment_health:{equipment_id}",
                        1800,  # 30分钟过期
                        json.dumps(health_data, default=str)
                    )

                    return health_data
                else:
                    return {"message": "No health data available"}

        except Exception as e:
            logger.error(f"Error getting equipment health status: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def get_maintenance_schedule(start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取维护计划"""
        try:
            async with db_pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT mt.*, e.equipment_name
                    FROM maintenance_tasks mt
                    JOIN equipment e ON mt.equipment_id = e.id
                    WHERE mt.scheduled_date BETWEEN $1 AND $2
                    AND mt.status IN ('planned', 'scheduled', 'in_progress')
                    ORDER BY mt.scheduled_date, mt.priority
                    """,
                    start_date,
                    end_date
                )

                return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"Error getting maintenance schedule: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "maintenance-service"}

@app.post("/tasks")
async def create_task(task_data: MaintenanceTaskCreate):
    """创建维护任务"""
    task_id = await MaintenanceManager.create_maintenance_task(task_data)
    return {"message": "Maintenance task created", "task_id": task_id}

@app.get("/tasks")
async def list_tasks(
    equipment_id: Optional[str] = None,
    status: Optional[MaintenanceStatus] = None,
    task_type: Optional[MaintenanceType] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取维护任务列表"""
    async with db_pool.acquire() as conn:
        query = """
            SELECT mt.*, e.equipment_name
            FROM maintenance_tasks mt
            JOIN equipment e ON mt.equipment_id = e.id
            WHERE 1=1
        """
        params = []
        param_count = 1

        if equipment_id:
            query += f" AND mt.equipment_id = ${param_count}"
            params.append(equipment_id)
            param_count += 1

        if status:
            query += f" AND mt.status = ${param_count}"
            params.append(status.value)
            param_count += 1

        if task_type:
            query += f" AND mt.task_type = ${param_count}"
            params.append(task_type.value)
            param_count += 1

        query += f" ORDER BY mt.created_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])

        rows = await conn.fetch(query, *params)
        return [dict(row) for row in rows]

@app.put("/tasks/{task_id}")
async def update_task(task_id: str, update_data: MaintenanceTaskUpdate):
    """更新维护任务"""
    success = await MaintenanceManager.update_maintenance_task(task_id, update_data)
    if success:
        return {"message": "Maintenance task updated"}
    else:
        raise HTTPException(status_code=404, detail="Task not found")

@app.get("/equipment/{equipment_id}/health")
async def get_equipment_health(equipment_id: str):
    """获取设备健康状态"""
    health_status = await MaintenanceManager.get_equipment_health_status(equipment_id)
    return health_status

@app.get("/alerts")
async def list_alerts(
    equipment_id: Optional[str] = None,
    severity: Optional[AlertSeverity] = None,
    status: str = "active",
    limit: int = 50,
    offset: int = 0
):
    """获取维护告警列表"""
    async with db_pool.acquire() as conn:
        query = """
            SELECT ma.*, e.equipment_name
            FROM maintenance_alerts ma
            JOIN equipment e ON ma.equipment_id = e.id
            WHERE ma.status = $1
        """
        params = [status]
        param_count = 2

        if equipment_id:
            query += f" AND ma.equipment_id = ${param_count}"
            params.append(equipment_id)
            param_count += 1

        if severity:
            query += f" AND ma.severity = ${param_count}"
            params.append(severity.value)
            param_count += 1

        query += f" ORDER BY ma.created_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])

        rows = await conn.fetch(query, *params)
        return [dict(row) for row in rows]

@app.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str, acknowledged_by: str):
    """确认告警"""
    async with db_pool.acquire() as conn:
        await conn.execute(
            """
            UPDATE maintenance_alerts
            SET status = 'acknowledged', acknowledged_by = $1, acknowledged_at = $2
            WHERE id = $3
            """,
            acknowledged_by,
            datetime.utcnow(),
            alert_id
        )

    return {"message": "Alert acknowledged"}

@app.get("/schedule")
async def get_schedule(
    start_date: datetime,
    end_date: datetime
):
    """获取维护计划"""
    schedule = await MaintenanceManager.get_maintenance_schedule(start_date, end_date)
    return {"schedule": schedule}

@app.post("/predictive/analyze")
async def run_predictive_analysis():
    """手动运行预测性维护分析"""
    await MaintenanceManager.run_predictive_analysis()
    return {"message": "Predictive analysis completed"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)