"""
工业智能体平台 - 安全服务
实现安全审计、威胁检测、合规检查等功能
"""

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import hashlib
import hmac
import secrets
import ipaddress
from collections import defaultdict
import uuid
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
security_analyzer = None

class ThreatLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AuditEventType(str, Enum):
    LOGIN = "login"
    LOGOUT = "logout"
    ACCESS_DENIED = "access_denied"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    SYSTEM_CHANGE = "system_change"
    SECURITY_VIOLATION = "security_violation"

class ComplianceStandard(str, Enum):
    ISO27001 = "iso27001"
    GDPR = "gdpr"
    SOX = "sox"
    HIPAA = "hipaa"
    CUSTOM = "custom"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer, security_analyzer
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化安全分析器
    security_analyzer = SecurityAnalyzer()
    
    # 初始化数据库表
    await initialize_database()
    
    # 启动安全监控任务
    asyncio.create_task(start_security_monitoring())
    asyncio.create_task(start_threat_detection())
    
    logger.info("Security service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Security service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="安全服务",
    description="安全审计、威胁检测、合规检查",
    version="1.0.0",
    lifespan=lifespan
)

security = HTTPBearer()

# Pydantic模型
class AuditEvent(BaseModel):
    event_type: AuditEventType
    user_id: Optional[str] = None
    resource: str
    action: str
    result: str  # success, failure, denied
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    details: Dict[str, Any] = {}

class SecurityPolicy(BaseModel):
    name: str
    description: str
    policy_type: str  # access_control, data_protection, network_security
    rules: List[Dict[str, Any]]
    is_active: bool = True
    compliance_standards: List[ComplianceStandard] = []

class ThreatIndicator(BaseModel):
    indicator_type: str  # ip, domain, hash, pattern
    value: str
    threat_level: ThreatLevel
    description: str
    source: str
    expires_at: Optional[datetime] = None

class ComplianceCheck(BaseModel):
    standard: ComplianceStandard
    check_name: str
    description: str
    query: str  # SQL查询或检查逻辑
    expected_result: str
    severity: str  # low, medium, high, critical

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 审计日志表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS audit_logs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                event_type VARCHAR(50) NOT NULL,
                user_id UUID REFERENCES users(id),
                resource VARCHAR(200) NOT NULL,
                action VARCHAR(100) NOT NULL,
                result VARCHAR(20) NOT NULL,
                ip_address INET,
                user_agent TEXT,
                details JSONB,
                timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 安全策略表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS security_policies (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                policy_name VARCHAR(200) NOT NULL,
                description TEXT,
                policy_type VARCHAR(50) NOT NULL,
                rules JSONB NOT NULL,
                is_active BOOLEAN DEFAULT true,
                compliance_standards TEXT[],
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 威胁指标表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS threat_indicators (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                indicator_type VARCHAR(50) NOT NULL,
                indicator_value VARCHAR(500) NOT NULL,
                threat_level VARCHAR(20) NOT NULL,
                description TEXT,
                source VARCHAR(100),
                expires_at TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 安全事件表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS security_incidents (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                incident_type VARCHAR(50) NOT NULL,
                severity VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                affected_systems TEXT[],
                indicators JSONB,
                status VARCHAR(20) DEFAULT 'open', -- open, investigating, resolved, closed
                assigned_to UUID REFERENCES users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP WITH TIME ZONE
            )
        """)
        
        # 合规检查表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS compliance_checks (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                standard VARCHAR(20) NOT NULL,
                check_name VARCHAR(200) NOT NULL,
                description TEXT,
                check_query TEXT NOT NULL,
                expected_result TEXT,
                severity VARCHAR(20) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 合规检查结果表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS compliance_results (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                check_id UUID NOT NULL REFERENCES compliance_checks(id),
                check_date DATE DEFAULT CURRENT_DATE,
                status VARCHAR(20) NOT NULL, -- pass, fail, warning
                actual_result TEXT,
                deviation_details TEXT,
                remediation_required BOOLEAN DEFAULT false,
                remediation_notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

class SecurityAnalyzer:
    """安全分析器"""
    
    def __init__(self):
        self.failed_login_threshold = 5
        self.suspicious_activity_window = 3600  # 1小时
        self.ip_whitelist = set()
        self.ip_blacklist = set()
    
    async def analyze_login_attempt(self, user_id: str, ip_address: str, success: bool):
        """分析登录尝试"""
        try:
            # 记录登录尝试
            key = f"login_attempts:{user_id}:{ip_address}"
            
            if success:
                # 成功登录，清除失败计数
                await redis_client.delete(key)
                
                # 检查异常登录时间
                await self._check_unusual_login_time(user_id)
                
                # 检查异常登录地点
                await self._check_unusual_login_location(user_id, ip_address)
                
            else:
                # 失败登录，增加计数
                current_count = await redis_client.incr(key)
                await redis_client.expire(key, self.suspicious_activity_window)
                
                if current_count >= self.failed_login_threshold:
                    await self._create_security_incident(
                        "brute_force_attack",
                        ThreatLevel.HIGH,
                        f"用户 {user_id} 从 {ip_address} 多次登录失败",
                        {"user_id": user_id, "ip_address": ip_address, "attempts": current_count}
                    )
                    
                    # 临时封禁IP
                    await self._block_ip_temporarily(ip_address, 3600)  # 1小时
            
        except Exception as e:
            logger.error(f"Error analyzing login attempt: {e}")
    
    async def _check_unusual_login_time(self, user_id: str):
        """检查异常登录时间"""
        try:
            current_hour = datetime.now().hour
            
            # 检查是否在非工作时间登录（晚上10点到早上6点）
            if current_hour >= 22 or current_hour <= 6:
                await self._create_security_incident(
                    "unusual_login_time",
                    ThreatLevel.MEDIUM,
                    f"用户 {user_id} 在非工作时间登录",
                    {"user_id": user_id, "login_hour": current_hour}
                )
                
        except Exception as e:
            logger.error(f"Error checking unusual login time: {e}")
    
    async def _check_unusual_login_location(self, user_id: str, ip_address: str):
        """检查异常登录地点"""
        try:
            # 获取用户最近的登录IP
            last_ip_key = f"last_login_ip:{user_id}"
            last_ip = await redis_client.get(last_ip_key)
            
            if last_ip and last_ip.decode() != ip_address:
                # IP地址发生变化，检查是否为内网IP
                try:
                    ip_obj = ipaddress.ip_address(ip_address)
                    if not ip_obj.is_private:
                        await self._create_security_incident(
                            "unusual_login_location",
                            ThreatLevel.MEDIUM,
                            f"用户 {user_id} 从新的外网IP登录",
                            {"user_id": user_id, "new_ip": ip_address, "previous_ip": last_ip.decode()}
                        )
                except ValueError:
                    pass
            
            # 更新最后登录IP
            await redis_client.setex(last_ip_key, 86400 * 7, ip_address)  # 保存7天
            
        except Exception as e:
            logger.error(f"Error checking unusual login location: {e}")
    
    async def _create_security_incident(self, incident_type: str, severity: ThreatLevel, 
                                      title: str, indicators: Dict[str, Any]):
        """创建安全事件"""
        try:
            incident_id = str(uuid.uuid4())
            
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO security_incidents 
                    (id, incident_type, severity, title, indicators)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    incident_id,
                    incident_type,
                    severity.value,
                    title,
                    json.dumps(indicators)
                )
            
            # 发送告警
            await kafka_producer.send("security_events", {
                "type": "security_incident",
                "incident_id": incident_id,
                "incident_type": incident_type,
                "severity": severity.value,
                "title": title,
                "indicators": indicators,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.warning(f"Security incident created: {title}")
            
        except Exception as e:
            logger.error(f"Error creating security incident: {e}")
    
    async def _block_ip_temporarily(self, ip_address: str, duration: int):
        """临时封禁IP"""
        try:
            await redis_client.setex(f"blocked_ip:{ip_address}", duration, "1")
            logger.info(f"Temporarily blocked IP: {ip_address} for {duration} seconds")
            
        except Exception as e:
            logger.error(f"Error blocking IP: {e}")
    
    async def check_ip_reputation(self, ip_address: str) -> Dict[str, Any]:
        """检查IP信誉"""
        try:
            # 检查是否在黑名单
            if ip_address in self.ip_blacklist:
                return {"reputation": "malicious", "reason": "blacklisted"}
            
            # 检查是否被临时封禁
            blocked = await redis_client.get(f"blocked_ip:{ip_address}")
            if blocked:
                return {"reputation": "blocked", "reason": "temporarily_blocked"}
            
            # 检查威胁指标
            async with db_pool.acquire() as conn:
                threat = await conn.fetchrow(
                    """
                    SELECT * FROM threat_indicators 
                    WHERE indicator_type = 'ip' AND indicator_value = $1 
                    AND is_active = true AND (expires_at IS NULL OR expires_at > NOW())
                    """,
                    ip_address
                )
                
                if threat:
                    return {
                        "reputation": "suspicious",
                        "threat_level": threat['threat_level'],
                        "reason": threat['description']
                    }
            
            return {"reputation": "clean", "reason": "no_threats_found"}
            
        except Exception as e:
            logger.error(f"Error checking IP reputation: {e}")
            return {"reputation": "unknown", "reason": "check_failed"}

class ComplianceManager:
    """合规管理器"""
    
    @staticmethod
    async def run_compliance_checks(standard: ComplianceStandard = None):
        """运行合规检查"""
        try:
            async with db_pool.acquire() as conn:
                # 获取检查项
                if standard:
                    checks = await conn.fetch(
                        "SELECT * FROM compliance_checks WHERE standard = $1 AND is_active = true",
                        standard.value
                    )
                else:
                    checks = await conn.fetch(
                        "SELECT * FROM compliance_checks WHERE is_active = true"
                    )
                
                results = []
                
                for check in checks:
                    result = await ComplianceManager._execute_check(check)
                    results.append(result)
                    
                    # 保存检查结果
                    await ComplianceManager._save_check_result(check['id'], result)
                
                return results
                
        except Exception as e:
            logger.error(f"Error running compliance checks: {e}")
            return []
    
    @staticmethod
    async def _execute_check(check: dict) -> Dict[str, Any]:
        """执行单个合规检查"""
        try:
            check_query = check['check_query']
            expected_result = check['expected_result']
            
            async with db_pool.acquire() as conn:
                # 执行检查查询
                result = await conn.fetchval(check_query)
                
                # 比较结果
                if str(result) == expected_result:
                    status = "pass"
                    deviation = None
                else:
                    status = "fail"
                    deviation = f"Expected: {expected_result}, Actual: {result}"
                
                return {
                    "check_id": check['id'],
                    "check_name": check['check_name'],
                    "status": status,
                    "actual_result": str(result),
                    "deviation": deviation,
                    "severity": check['severity']
                }
                
        except Exception as e:
            logger.error(f"Error executing compliance check {check['check_name']}: {e}")
            return {
                "check_id": check['id'],
                "check_name": check['check_name'],
                "status": "error",
                "actual_result": None,
                "deviation": str(e),
                "severity": check['severity']
            }
    
    @staticmethod
    async def _save_check_result(check_id: str, result: Dict[str, Any]):
        """保存检查结果"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO compliance_results 
                    (check_id, status, actual_result, deviation_details, remediation_required)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    check_id,
                    result['status'],
                    result['actual_result'],
                    result['deviation'],
                    result['status'] == 'fail'
                )
                
        except Exception as e:
            logger.error(f"Error saving compliance check result: {e}")

# 中间件
@app.middleware("http")
async def security_middleware(request: Request, call_next):
    """安全中间件"""
    start_time = datetime.utcnow()
    
    # 获取客户端IP
    client_ip = request.client.host
    
    # 检查IP信誉
    ip_reputation = await security_analyzer.check_ip_reputation(client_ip)
    
    if ip_reputation["reputation"] in ["malicious", "blocked"]:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # 记录请求
    await AuditManager.log_request(request, client_ip)
    
    response = await call_next(request)
    
    # 记录响应时间
    end_time = datetime.utcnow()
    response_time = (end_time - start_time).total_seconds()
    
    response.headers["X-Response-Time"] = str(response_time)
    
    return response

class AuditManager:
    """审计管理器"""
    
    @staticmethod
    async def log_audit_event(event: AuditEvent):
        """记录审计事件"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO audit_logs 
                    (event_type, user_id, resource, action, result, ip_address, user_agent, details)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """,
                    event.event_type.value,
                    event.user_id,
                    event.resource,
                    event.action,
                    event.result,
                    event.ip_address,
                    event.user_agent,
                    json.dumps(event.details)
                )
                
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")
    
    @staticmethod
    async def log_request(request: Request, client_ip: str):
        """记录HTTP请求"""
        try:
            # 提取用户信息（如果有认证）
            user_id = None
            auth_header = request.headers.get("Authorization")
            if auth_header:
                # 这里可以解析JWT token获取用户ID
                pass
            
            await AuditManager.log_audit_event(AuditEvent(
                event_type=AuditEventType.DATA_ACCESS,
                user_id=user_id,
                resource=str(request.url.path),
                action=request.method,
                result="success",  # 这里简化处理
                ip_address=client_ip,
                user_agent=request.headers.get("User-Agent"),
                details={
                    "query_params": dict(request.query_params),
                    "headers": dict(request.headers)
                }
            ))
            
        except Exception as e:
            logger.error(f"Error logging request: {e}")

# 后台任务
async def start_security_monitoring():
    """启动安全监控"""
    while True:
        try:
            # 运行合规检查
            await ComplianceManager.run_compliance_checks()
            await asyncio.sleep(3600)  # 每小时运行一次
        except Exception as e:
            logger.error(f"Error in security monitoring: {e}")
            await asyncio.sleep(3600)

async def start_threat_detection():
    """启动威胁检测"""
    consumer = AIOKafkaConsumer(
        "security_events",
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        group_id="security_service",
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))
    )
    
    await consumer.start()
    
    try:
        async for message in consumer:
            event_data = message.value
            await process_security_event(event_data)
    except Exception as e:
        logger.error(f"Error in threat detection: {e}")
    finally:
        await consumer.stop()

async def process_security_event(event_data: Dict[str, Any]):
    """处理安全事件"""
    try:
        event_type = event_data.get("type")
        
        if event_type == "login_attempt":
            await security_analyzer.analyze_login_attempt(
                event_data.get("user_id"),
                event_data.get("ip_address"),
                event_data.get("success", False)
            )
        
    except Exception as e:
        logger.error(f"Error processing security event: {e}")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "security-service"}

@app.post("/audit/events")
async def log_audit_event(event: AuditEvent):
    """记录审计事件"""
    await AuditManager.log_audit_event(event)
    return {"message": "Audit event logged"}

@app.get("/audit/events")
async def get_audit_events(
    event_type: Optional[AuditEventType] = None,
    user_id: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0
):
    """获取审计事件"""
    async with db_pool.acquire() as conn:
        query = "SELECT * FROM audit_logs WHERE 1=1"
        params = []
        param_count = 1
        
        if event_type:
            query += f" AND event_type = ${param_count}"
            params.append(event_type.value)
            param_count += 1
        
        if user_id:
            query += f" AND user_id = ${param_count}"
            params.append(user_id)
            param_count += 1
        
        if start_date:
            query += f" AND timestamp >= ${param_count}"
            params.append(start_date)
            param_count += 1
        
        if end_date:
            query += f" AND timestamp <= ${param_count}"
            params.append(end_date)
            param_count += 1
        
        query += f" ORDER BY timestamp DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])
        
        events = await conn.fetch(query, *params)
        return [dict(event) for event in events]

@app.post("/compliance/checks/run")
async def run_compliance_checks(standard: Optional[ComplianceStandard] = None):
    """运行合规检查"""
    results = await ComplianceManager.run_compliance_checks(standard)
    return {"message": "Compliance checks completed", "results": results}

@app.get("/compliance/results")
async def get_compliance_results(
    standard: Optional[ComplianceStandard] = None,
    status: Optional[str] = None,
    limit: int = 50
):
    """获取合规检查结果"""
    async with db_pool.acquire() as conn:
        query = """
            SELECT cr.*, cc.standard, cc.check_name, cc.severity
            FROM compliance_results cr
            JOIN compliance_checks cc ON cr.check_id = cc.id
            WHERE 1=1
        """
        params = []
        param_count = 1
        
        if standard:
            query += f" AND cc.standard = ${param_count}"
            params.append(standard.value)
            param_count += 1
        
        if status:
            query += f" AND cr.status = ${param_count}"
            params.append(status)
            param_count += 1
        
        query += f" ORDER BY cr.created_at DESC LIMIT ${param_count}"
        params.append(limit)
        
        results = await conn.fetch(query, *params)
        return [dict(result) for result in results]

@app.post("/threats/indicators")
async def add_threat_indicator(indicator: ThreatIndicator):
    """添加威胁指标"""
    indicator_id = str(uuid.uuid4())
    
    async with db_pool.acquire() as conn:
        await conn.execute(
            """
            INSERT INTO threat_indicators 
            (id, indicator_type, indicator_value, threat_level, description, source, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
            indicator_id,
            indicator.indicator_type,
            indicator.value,
            indicator.threat_level.value,
            indicator.description,
            indicator.source,
            indicator.expires_at
        )
    
    return {"message": "Threat indicator added", "indicator_id": indicator_id}

@app.get("/security/incidents")
async def get_security_incidents(
    incident_type: Optional[str] = None,
    severity: Optional[ThreatLevel] = None,
    status: Optional[str] = None,
    limit: int = 50
):
    """获取安全事件"""
    async with db_pool.acquire() as conn:
        query = "SELECT * FROM security_incidents WHERE 1=1"
        params = []
        param_count = 1
        
        if incident_type:
            query += f" AND incident_type = ${param_count}"
            params.append(incident_type)
            param_count += 1
        
        if severity:
            query += f" AND severity = ${param_count}"
            params.append(severity.value)
            param_count += 1
        
        if status:
            query += f" AND status = ${param_count}"
            params.append(status)
            param_count += 1
        
        query += f" ORDER BY created_at DESC LIMIT ${param_count}"
        params.append(limit)
        
        incidents = await conn.fetch(query, *params)
        return [dict(incident) for incident in incidents]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8011)
