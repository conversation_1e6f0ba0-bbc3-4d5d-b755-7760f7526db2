"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-motion";
exports.ids = ["vendor-chunks/rc-motion"];
exports.modules = {

/***/ "(ssr)/../node_modules/rc-motion/es/CSSMotion.js":
/*!*************************************************!*\
  !*** ../node_modules/rc-motion/es/CSSMotion.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotion: () => (/* binding */ genCSSMotion)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/../node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/../node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./context */ \"(ssr)/../node_modules/rc-motion/es/context.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/../node_modules/rc-motion/es/DomWrapper.js\");\n/* harmony import */ var _hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useStatus */ \"(ssr)/../node_modules/rc-motion/es/hooks/useStatus.js\");\n/* harmony import */ var _hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStepQueue */ \"(ssr)/../node_modules/rc-motion/es/hooks/useStepQueue.js\");\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./interface */ \"(ssr)/../node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/../node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nfunction genCSSMotion(config) {\n  var transitionSupport = config;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_context__WEBPACK_IMPORTED_MODULE_8__.Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = (0,_hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(supportMotion, visible, getDomElement, props),\n      _useStatus2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useCallback(function (node) {\n      nodeRef.current = node;\n      (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.fillRef)(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_12__.STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if ((0,_hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__.isActive)(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = (0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(motionChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(motionChildren)) {\n      var originNodeRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotion(_util_motion__WEBPACK_IMPORTED_MODULE_13__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/CSSMotion.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/CSSMotionList.js":
/*!*****************************************************!*\
  !*** ../node_modules/rc-motion/es/CSSMotionList.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotionList: () => (/* binding */ genCSSMotionList)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/../node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _util_diff__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/diff */ \"(ssr)/../node_modules/rc-motion/es/util/diff.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/../node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\n\n\n\n\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nfunction genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _CSSMotion__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(CSSMotionList, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, entity), {}, {\n              status: _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props, _excluded);\n        var Component = component || react__WEBPACK_IMPORTED_MODULE_9__.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref2, _excluded2);\n          var visible = status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_ADD || status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_KEEP;\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(CSSMotion, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.parseKeys)(keys);\n        var mixedKeyEntities = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.diffKeys)(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED && entity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(react__WEBPACK_IMPORTED_MODULE_9__.Component);\n  (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotionList(_util_motion__WEBPACK_IMPORTED_MODULE_12__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9DU1NNb3Rpb25MaXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDZ0M7QUFDckI7QUFDRztBQUNOO0FBQ29CO0FBQzFCO0FBQ007QUFDTTtBQUN4RTtBQUNBO0FBQ0E7QUFDK0I7QUFDVztBQUNnRTtBQUN4RDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLHNGQUFzRixtREFBZTtBQUNyRztBQUNBLElBQUksK0VBQVM7QUFDYixpQkFBaUIsa0ZBQVk7QUFDN0I7QUFDQTtBQUNBLE1BQU0scUZBQWU7QUFDckIsMEVBQTBFLGFBQWE7QUFDdkY7QUFDQTtBQUNBO0FBQ0EsTUFBTSxxRkFBZSxDQUFDLDRGQUFzQjtBQUM1QztBQUNBLE9BQU87QUFDUDtBQUNBLE1BQU0scUZBQWUsQ0FBQyw0RkFBc0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG9GQUFhLENBQUMsb0ZBQWEsR0FBRyxhQUFhO0FBQzlELHNCQUFzQix1REFBYztBQUNwQyxhQUFhO0FBQ2IsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsdURBQWM7QUFDNUMsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBLElBQUksa0ZBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDhGQUF3QjtBQUM5QyxxQ0FBcUMsMkNBQWM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0QkFBNEIsZ0RBQW1CO0FBQy9DO0FBQ0EseUJBQXlCLDhGQUF3QjtBQUNqRCxtQ0FBbUMsbURBQVUsZUFBZSxvREFBVztBQUN2RSw4QkFBOEIsZ0RBQW1CLFlBQVksOEVBQVEsR0FBRztBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsNEJBQTRCLG9GQUFhLENBQUMsb0ZBQWEsR0FBRyxZQUFZO0FBQ3RFO0FBQ0EsYUFBYTtBQUNiLFdBQVc7QUFDWCxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isc0RBQVM7QUFDeEMsK0JBQStCLHFEQUFRO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhOztBQUViO0FBQ0Esb0RBQW9ELHVEQUFjLHNCQUFzQixzREFBYTtBQUNyRztBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUcsQ0FBQyw0Q0FBZTtBQUNuQixFQUFFLHFGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxpRUFBZSxpQkFBaUIsNERBQWlCLENBQUMiLCJzb3VyY2VzIjpbIko6XFxhdWdtZW50XFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxDU1NNb3Rpb25MaXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX2NsYXNzQ2FsbENoZWNrIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jbGFzc0NhbGxDaGVja1wiO1xuaW1wb3J0IF9jcmVhdGVDbGFzcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlQ2xhc3NcIjtcbmltcG9ydCBfYXNzZXJ0VGhpc0luaXRpYWxpemVkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3NlcnRUaGlzSW5pdGlhbGl6ZWRcIjtcbmltcG9ydCBfaW5oZXJpdHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2luaGVyaXRzXCI7XG5pbXBvcnQgX2NyZWF0ZVN1cGVyIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVTdXBlclwiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJjb21wb25lbnRcIiwgXCJjaGlsZHJlblwiLCBcIm9uVmlzaWJsZUNoYW5nZWRcIiwgXCJvbkFsbFJlbW92ZWRcIl0sXG4gIF9leGNsdWRlZDIgPSBbXCJzdGF0dXNcIl07XG4vKiBlc2xpbnQgcmVhY3QvcHJvcC10eXBlczogMCAqL1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IE9yaWdpbkNTU01vdGlvbiBmcm9tIFwiLi9DU1NNb3Rpb25cIjtcbmltcG9ydCB7IGRpZmZLZXlzLCBwYXJzZUtleXMsIFNUQVRVU19BREQsIFNUQVRVU19LRUVQLCBTVEFUVVNfUkVNT1ZFLCBTVEFUVVNfUkVNT1ZFRCB9IGZyb20gXCIuL3V0aWwvZGlmZlwiO1xuaW1wb3J0IHsgc3VwcG9ydFRyYW5zaXRpb24gfSBmcm9tIFwiLi91dGlsL21vdGlvblwiO1xudmFyIE1PVElPTl9QUk9QX05BTUVTID0gWydldmVudFByb3BzJywgJ3Zpc2libGUnLCAnY2hpbGRyZW4nLCAnbW90aW9uTmFtZScsICdtb3Rpb25BcHBlYXInLCAnbW90aW9uRW50ZXInLCAnbW90aW9uTGVhdmUnLCAnbW90aW9uTGVhdmVJbW1lZGlhdGVseScsICdtb3Rpb25EZWFkbGluZScsICdyZW1vdmVPbkxlYXZlJywgJ2xlYXZlZENsYXNzTmFtZScsICdvbkFwcGVhclByZXBhcmUnLCAnb25BcHBlYXJTdGFydCcsICdvbkFwcGVhckFjdGl2ZScsICdvbkFwcGVhckVuZCcsICdvbkVudGVyU3RhcnQnLCAnb25FbnRlckFjdGl2ZScsICdvbkVudGVyRW5kJywgJ29uTGVhdmVTdGFydCcsICdvbkxlYXZlQWN0aXZlJywgJ29uTGVhdmVFbmQnXTtcbi8qKlxuICogR2VuZXJhdGUgYSBDU1NNb3Rpb25MaXN0IGNvbXBvbmVudCB3aXRoIGNvbmZpZ1xuICogQHBhcmFtIHRyYW5zaXRpb25TdXBwb3J0IE5vIG5lZWQgc2luY2UgQ1NTTW90aW9uTGlzdCBubyBsb25nZXIgZGVwZW5kcyBvbiB0cmFuc2l0aW9uIHN1cHBvcnRcbiAqIEBwYXJhbSBDU1NNb3Rpb24gQ1NTTW90aW9uIGNvbXBvbmVudFxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuQ1NTTW90aW9uTGlzdCh0cmFuc2l0aW9uU3VwcG9ydCkge1xuICB2YXIgQ1NTTW90aW9uID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiBPcmlnaW5DU1NNb3Rpb247XG4gIHZhciBDU1NNb3Rpb25MaXN0ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfUmVhY3QkQ29tcG9uZW50KSB7XG4gICAgX2luaGVyaXRzKENTU01vdGlvbkxpc3QsIF9SZWFjdCRDb21wb25lbnQpO1xuICAgIHZhciBfc3VwZXIgPSBfY3JlYXRlU3VwZXIoQ1NTTW90aW9uTGlzdCk7XG4gICAgZnVuY3Rpb24gQ1NTTW90aW9uTGlzdCgpIHtcbiAgICAgIHZhciBfdGhpcztcbiAgICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBDU1NNb3Rpb25MaXN0KTtcbiAgICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgICAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICAgICAgfVxuICAgICAgX3RoaXMgPSBfc3VwZXIuY2FsbC5hcHBseShfc3VwZXIsIFt0aGlzXS5jb25jYXQoYXJncykpO1xuICAgICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcInN0YXRlXCIsIHtcbiAgICAgICAga2V5RW50aXRpZXM6IFtdXG4gICAgICB9KTtcbiAgICAgIC8vIFpvbWJpZUo6IFJldHVybiB0aGUgY291bnQgb2YgcmVzdCBrZXlzLiBJdCdzIHNhZmUgdG8gcmVmYWN0b3IgaWYgbmVlZCBtb3JlIGluZm8uXG4gICAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwicmVtb3ZlS2V5XCIsIGZ1bmN0aW9uIChyZW1vdmVLZXkpIHtcbiAgICAgICAgX3RoaXMuc2V0U3RhdGUoZnVuY3Rpb24gKHByZXZTdGF0ZSkge1xuICAgICAgICAgIHZhciBuZXh0S2V5RW50aXRpZXMgPSBwcmV2U3RhdGUua2V5RW50aXRpZXMubWFwKGZ1bmN0aW9uIChlbnRpdHkpIHtcbiAgICAgICAgICAgIGlmIChlbnRpdHkua2V5ICE9PSByZW1vdmVLZXkpIHJldHVybiBlbnRpdHk7XG4gICAgICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBlbnRpdHkpLCB7fSwge1xuICAgICAgICAgICAgICBzdGF0dXM6IFNUQVRVU19SRU1PVkVEXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAga2V5RW50aXRpZXM6IG5leHRLZXlFbnRpdGllc1xuICAgICAgICAgIH07XG4gICAgICAgIH0sIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICB2YXIga2V5RW50aXRpZXMgPSBfdGhpcy5zdGF0ZS5rZXlFbnRpdGllcztcbiAgICAgICAgICB2YXIgcmVzdEtleXNDb3VudCA9IGtleUVudGl0aWVzLmZpbHRlcihmdW5jdGlvbiAoX3JlZikge1xuICAgICAgICAgICAgdmFyIHN0YXR1cyA9IF9yZWYuc3RhdHVzO1xuICAgICAgICAgICAgcmV0dXJuIHN0YXR1cyAhPT0gU1RBVFVTX1JFTU9WRUQ7XG4gICAgICAgICAgfSkubGVuZ3RoO1xuICAgICAgICAgIGlmIChyZXN0S2V5c0NvdW50ID09PSAwICYmIF90aGlzLnByb3BzLm9uQWxsUmVtb3ZlZCkge1xuICAgICAgICAgICAgX3RoaXMucHJvcHMub25BbGxSZW1vdmVkKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIF90aGlzO1xuICAgIH1cbiAgICBfY3JlYXRlQ2xhc3MoQ1NTTW90aW9uTGlzdCwgW3tcbiAgICAgIGtleTogXCJyZW5kZXJcIixcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiByZW5kZXIoKSB7XG4gICAgICAgIHZhciBfdGhpczIgPSB0aGlzO1xuICAgICAgICB2YXIga2V5RW50aXRpZXMgPSB0aGlzLnN0YXRlLmtleUVudGl0aWVzO1xuICAgICAgICB2YXIgX3RoaXMkcHJvcHMgPSB0aGlzLnByb3BzLFxuICAgICAgICAgIGNvbXBvbmVudCA9IF90aGlzJHByb3BzLmNvbXBvbmVudCxcbiAgICAgICAgICBjaGlsZHJlbiA9IF90aGlzJHByb3BzLmNoaWxkcmVuLFxuICAgICAgICAgIF9vblZpc2libGVDaGFuZ2VkID0gX3RoaXMkcHJvcHMub25WaXNpYmxlQ2hhbmdlZCxcbiAgICAgICAgICBvbkFsbFJlbW92ZWQgPSBfdGhpcyRwcm9wcy5vbkFsbFJlbW92ZWQsXG4gICAgICAgICAgcmVzdFByb3BzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKF90aGlzJHByb3BzLCBfZXhjbHVkZWQpO1xuICAgICAgICB2YXIgQ29tcG9uZW50ID0gY29tcG9uZW50IHx8IFJlYWN0LkZyYWdtZW50O1xuICAgICAgICB2YXIgbW90aW9uUHJvcHMgPSB7fTtcbiAgICAgICAgTU9USU9OX1BST1BfTkFNRVMuZm9yRWFjaChmdW5jdGlvbiAocHJvcCkge1xuICAgICAgICAgIG1vdGlvblByb3BzW3Byb3BdID0gcmVzdFByb3BzW3Byb3BdO1xuICAgICAgICAgIGRlbGV0ZSByZXN0UHJvcHNbcHJvcF07XG4gICAgICAgIH0pO1xuICAgICAgICBkZWxldGUgcmVzdFByb3BzLmtleXM7XG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDb21wb25lbnQsIHJlc3RQcm9wcywga2V5RW50aXRpZXMubWFwKGZ1bmN0aW9uIChfcmVmMiwgaW5kZXgpIHtcbiAgICAgICAgICB2YXIgc3RhdHVzID0gX3JlZjIuc3RhdHVzLFxuICAgICAgICAgICAgZXZlbnRQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmMiwgX2V4Y2x1ZGVkMik7XG4gICAgICAgICAgdmFyIHZpc2libGUgPSBzdGF0dXMgPT09IFNUQVRVU19BREQgfHwgc3RhdHVzID09PSBTVEFUVVNfS0VFUDtcbiAgICAgICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ1NTTW90aW9uLCBfZXh0ZW5kcyh7fSwgbW90aW9uUHJvcHMsIHtcbiAgICAgICAgICAgIGtleTogZXZlbnRQcm9wcy5rZXksXG4gICAgICAgICAgICB2aXNpYmxlOiB2aXNpYmxlLFxuICAgICAgICAgICAgZXZlbnRQcm9wczogZXZlbnRQcm9wcyxcbiAgICAgICAgICAgIG9uVmlzaWJsZUNoYW5nZWQ6IGZ1bmN0aW9uIG9uVmlzaWJsZUNoYW5nZWQoY2hhbmdlZFZpc2libGUpIHtcbiAgICAgICAgICAgICAgX29uVmlzaWJsZUNoYW5nZWQgPT09IG51bGwgfHwgX29uVmlzaWJsZUNoYW5nZWQgPT09IHZvaWQgMCB8fCBfb25WaXNpYmxlQ2hhbmdlZChjaGFuZ2VkVmlzaWJsZSwge1xuICAgICAgICAgICAgICAgIGtleTogZXZlbnRQcm9wcy5rZXlcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIGlmICghY2hhbmdlZFZpc2libGUpIHtcbiAgICAgICAgICAgICAgICBfdGhpczIucmVtb3ZlS2V5KGV2ZW50UHJvcHMua2V5KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pLCBmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICAgICAgICAgICAgcmV0dXJuIGNoaWxkcmVuKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgICAgICAgICAgICBpbmRleDogaW5kZXhcbiAgICAgICAgICAgIH0pLCByZWYpO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfV0sIFt7XG4gICAgICBrZXk6IFwiZ2V0RGVyaXZlZFN0YXRlRnJvbVByb3BzXCIsXG4gICAgICB2YWx1ZTogZnVuY3Rpb24gZ2V0RGVyaXZlZFN0YXRlRnJvbVByb3BzKF9yZWYzLCBfcmVmNCkge1xuICAgICAgICB2YXIga2V5cyA9IF9yZWYzLmtleXM7XG4gICAgICAgIHZhciBrZXlFbnRpdGllcyA9IF9yZWY0LmtleUVudGl0aWVzO1xuICAgICAgICB2YXIgcGFyc2VkS2V5T2JqZWN0cyA9IHBhcnNlS2V5cyhrZXlzKTtcbiAgICAgICAgdmFyIG1peGVkS2V5RW50aXRpZXMgPSBkaWZmS2V5cyhrZXlFbnRpdGllcywgcGFyc2VkS2V5T2JqZWN0cyk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAga2V5RW50aXRpZXM6IG1peGVkS2V5RW50aXRpZXMuZmlsdGVyKGZ1bmN0aW9uIChlbnRpdHkpIHtcbiAgICAgICAgICAgIHZhciBwcmV2RW50aXR5ID0ga2V5RW50aXRpZXMuZmluZChmdW5jdGlvbiAoX3JlZjUpIHtcbiAgICAgICAgICAgICAgdmFyIGtleSA9IF9yZWY1LmtleTtcbiAgICAgICAgICAgICAgcmV0dXJuIGVudGl0eS5rZXkgPT09IGtleTtcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBSZW1vdmUgaWYgYWxyZWFkeSBtYXJrIGFzIHJlbW92ZWRcbiAgICAgICAgICAgIGlmIChwcmV2RW50aXR5ICYmIHByZXZFbnRpdHkuc3RhdHVzID09PSBTVEFUVVNfUkVNT1ZFRCAmJiBlbnRpdHkuc3RhdHVzID09PSBTVEFUVVNfUkVNT1ZFKSB7XG4gICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgIH0pXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfV0pO1xuICAgIHJldHVybiBDU1NNb3Rpb25MaXN0O1xuICB9KFJlYWN0LkNvbXBvbmVudCk7XG4gIF9kZWZpbmVQcm9wZXJ0eShDU1NNb3Rpb25MaXN0LCBcImRlZmF1bHRQcm9wc1wiLCB7XG4gICAgY29tcG9uZW50OiAnZGl2J1xuICB9KTtcbiAgcmV0dXJuIENTU01vdGlvbkxpc3Q7XG59XG5leHBvcnQgZGVmYXVsdCBnZW5DU1NNb3Rpb25MaXN0KHN1cHBvcnRUcmFuc2l0aW9uKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/CSSMotionList.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/DomWrapper.js":
/*!**************************************************!*\
  !*** ../node_modules/rc-motion/es/DomWrapper.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n  function DomWrapper() {\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n    return _super.apply(this, arguments);\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9Eb21XcmFwcGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBd0U7QUFDTjtBQUNOO0FBQ007QUFDbkM7QUFDL0I7QUFDQSxFQUFFLCtFQUFTO0FBQ1gsZUFBZSxrRkFBWTtBQUMzQjtBQUNBLElBQUkscUZBQWU7QUFDbkI7QUFDQTtBQUNBLEVBQUUsa0ZBQVk7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMsQ0FBQyw0Q0FBZTtBQUNqQixpRUFBZSxVQUFVIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcRG9tV3JhcHBlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2NsYXNzQ2FsbENoZWNrIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jbGFzc0NhbGxDaGVja1wiO1xuaW1wb3J0IF9jcmVhdGVDbGFzcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlQ2xhc3NcIjtcbmltcG9ydCBfaW5oZXJpdHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2luaGVyaXRzXCI7XG5pbXBvcnQgX2NyZWF0ZVN1cGVyIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVTdXBlclwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xudmFyIERvbVdyYXBwZXIgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9SZWFjdCRDb21wb25lbnQpIHtcbiAgX2luaGVyaXRzKERvbVdyYXBwZXIsIF9SZWFjdCRDb21wb25lbnQpO1xuICB2YXIgX3N1cGVyID0gX2NyZWF0ZVN1cGVyKERvbVdyYXBwZXIpO1xuICBmdW5jdGlvbiBEb21XcmFwcGVyKCkge1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBEb21XcmFwcGVyKTtcbiAgICByZXR1cm4gX3N1cGVyLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gIH1cbiAgX2NyZWF0ZUNsYXNzKERvbVdyYXBwZXIsIFt7XG4gICAga2V5OiBcInJlbmRlclwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZW5kZXIoKSB7XG4gICAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlbjtcbiAgICB9XG4gIH1dKTtcbiAgcmV0dXJuIERvbVdyYXBwZXI7XG59KFJlYWN0LkNvbXBvbmVudCk7XG5leHBvcnQgZGVmYXVsdCBEb21XcmFwcGVyOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/context.js":
/*!***********************************************!*\
  !*** ../node_modules/rc-motion/es/context.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   \"default\": () => (/* binding */ MotionProvider)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _excluded = [\"children\"];\n\nvar Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nfunction MotionProvider(_ref) {\n  var children = _ref.children,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Context.Provider, {\n    value: props\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBGO0FBQzFGO0FBQytCO0FBQ3hCLDJCQUEyQixnREFBbUIsR0FBRztBQUN6QztBQUNmO0FBQ0EsWUFBWSw4RkFBd0I7QUFDcEMsc0JBQXNCLGdEQUFtQjtBQUN6QztBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiSjpcXGF1Z21lbnRcXGluZHVzdHJ5LWFpLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXGNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJjaGlsZHJlblwiXTtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHt9KTtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1vdGlvblByb3ZpZGVyKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBwcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmLCBfZXhjbHVkZWQpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiBwcm9wc1xuICB9LCBjaGlsZHJlbik7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/context.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/hooks/useDomMotionEvents.js":
/*!****************************************************************!*\
  !*** ../node_modules/rc-motion/es/hooks/useDomMotionEvents.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/motion */ \"(ssr)/../node_modules/rc-motion/es/util/motion.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (onInternalMotionEnd) {\n  var cacheElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/hooks/useDomMotionEvents.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js":
/*!***********************************************************************!*\
  !*** ../node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/../node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__[\"default\"])() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7QUFDRTs7QUFFbkQ7QUFDQSxnQ0FBZ0Msb0VBQVMsS0FBSyxrREFBZSxHQUFHLDRDQUFTO0FBQ3pFLGlFQUFlLHlCQUF5QiIsInNvdXJjZXMiOlsiSjpcXGF1Z21lbnRcXGluZHVzdHJ5LWFpLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXGhvb2tzXFx1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjYW5Vc2VEb20gZnJvbSBcInJjLXV0aWwvZXMvRG9tL2NhblVzZURvbVwiO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8vIEl0J3Mgc2FmZSB0byB1c2UgYHVzZUxheW91dEVmZmVjdGAgYnV0IHRoZSB3YXJuaW5nIGlzIGFubm95aW5nXG52YXIgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGNhblVzZURvbSgpID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0O1xuZXhwb3J0IGRlZmF1bHQgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/hooks/useNextFrame.js":
/*!**********************************************************!*\
  !*** ../node_modules/rc-motion/es/hooks/useNextFrame.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/../node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function () {\n  var nextFrameRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  function cancelNextFrame() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"].cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VOZXh0RnJhbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNGO0FBQy9CLGlFQUFnQjtBQUNoQixxQkFBcUIseUNBQVk7QUFDakM7QUFDQSxJQUFJLHNEQUFHO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQUc7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxDQUFDIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaG9va3NcXHVzZU5leHRGcmFtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcmFmIGZyb20gXCJyYy11dGlsL2VzL3JhZlwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uICgpIHtcbiAgdmFyIG5leHRGcmFtZVJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgZnVuY3Rpb24gY2FuY2VsTmV4dEZyYW1lKCkge1xuICAgIHJhZi5jYW5jZWwobmV4dEZyYW1lUmVmLmN1cnJlbnQpO1xuICB9XG4gIGZ1bmN0aW9uIG5leHRGcmFtZShjYWxsYmFjaykge1xuICAgIHZhciBkZWxheSA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogMjtcbiAgICBjYW5jZWxOZXh0RnJhbWUoKTtcbiAgICB2YXIgbmV4dEZyYW1lSWQgPSByYWYoZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKGRlbGF5IDw9IDEpIHtcbiAgICAgICAgY2FsbGJhY2soe1xuICAgICAgICAgIGlzQ2FuY2VsZWQ6IGZ1bmN0aW9uIGlzQ2FuY2VsZWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV4dEZyYW1lSWQgIT09IG5leHRGcmFtZVJlZi5jdXJyZW50O1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXh0RnJhbWUoY2FsbGJhY2ssIGRlbGF5IC0gMSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgbmV4dEZyYW1lUmVmLmN1cnJlbnQgPSBuZXh0RnJhbWVJZDtcbiAgfVxuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBjYW5jZWxOZXh0RnJhbWUoKTtcbiAgICB9O1xuICB9LCBbXSk7XG4gIHJldHVybiBbbmV4dEZyYW1lLCBjYW5jZWxOZXh0RnJhbWVdO1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/hooks/useNextFrame.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/hooks/useStatus.js":
/*!*******************************************************!*\
  !*** ../node_modules/rc-motion/es/hooks/useStatus.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useStatus)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util */ \"(ssr)/../node_modules/rc-util/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/../node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useSyncState */ \"(ssr)/../node_modules/rc-util/es/hooks/useSyncState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interface */ \"(ssr)/../node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useDomMotionEvents */ \"(ssr)/../node_modules/rc-motion/es/hooks/useDomMotionEvents.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/../node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useStepQueue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useStepQueue */ \"(ssr)/../node_modules/rc-motion/es/hooks/useStepQueue.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = (0,rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n  var deadlineRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = (0,rc_util__WEBPACK_IMPORTED_MODULE_3__.useEvent)(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = (0,_useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(onInternalMotionEnd),\n    _useDomMotionEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onAppearPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onAppearStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onAppearActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onEnterPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onEnterStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onEnterActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onLeavePrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onLeaveStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE) {\n        var onPrepare = eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE];\n        if (!onPrepare) {\n          return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE && currentStatus !== _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.DoStep;\n    }),\n    _useStepQueue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__.isActive)(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // Update with new status\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (\n    // Cancel appear\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && !motionLeave) {\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE] && step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START) {\n    mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VTdGF0dXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFxRTtBQUNHO0FBQ0Y7QUFDbkM7QUFDYztBQUNRO0FBQzFCO0FBQ1c7QUFDa0c7QUFDdEY7QUFDYztBQUNNO0FBQzNEO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IscUVBQVE7QUFDMUIsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxzQkFBc0IseUVBQVksQ0FBQyxtREFBVztBQUM5QyxxQkFBcUIsb0ZBQWM7QUFDbkM7QUFDQTtBQUNBLG1CQUFtQixxRUFBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDZDQUFNO0FBQ3pCLG9CQUFvQiw2Q0FBTTs7QUFFMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0IsNkNBQU07O0FBRXhCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxtREFBVztBQUN6QjtBQUNBO0FBQ0EsNEJBQTRCLGlEQUFRO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtREFBVztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHFEQUFhO0FBQ2hDO0FBQ0EsTUFBTSxvQkFBb0Isb0RBQVk7QUFDdEM7QUFDQSxNQUFNLG9CQUFvQixvREFBWTtBQUN0QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILDRCQUE0QiwrREFBa0I7QUFDOUMsMkJBQTJCLG9GQUFjO0FBQ3pDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcscURBQWE7QUFDeEIsZUFBZSxxRkFBZSxDQUFDLHFGQUFlLENBQUMscUZBQWUsR0FBRyxFQUFFLG9EQUFZLG9CQUFvQixrREFBVSxrQkFBa0IsbURBQVc7QUFDMUksV0FBVyxvREFBWTtBQUN2QixlQUFlLHFGQUFlLENBQUMscUZBQWUsQ0FBQyxxRkFBZSxHQUFHLEVBQUUsb0RBQVksbUJBQW1CLGtEQUFVLGlCQUFpQixtREFBVztBQUN4SSxXQUFXLG9EQUFZO0FBQ3ZCLGVBQWUscUZBQWUsQ0FBQyxxRkFBZSxDQUFDLHFGQUFlLEdBQUcsRUFBRSxvREFBWSxtQkFBbUIsa0RBQVUsaUJBQWlCLG1EQUFXO0FBQ3hJO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBDQUFhO0FBQ25DO0FBQ0EsR0FBRztBQUNILHNCQUFzQiwwREFBWTtBQUNsQztBQUNBLHNCQUFzQixvREFBWTtBQUNsQyxzQ0FBc0Msb0RBQVk7QUFDbEQ7QUFDQSxpQkFBaUIsb0RBQVE7QUFDekI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsbURBQVcsc0JBQXNCLG1EQUFXO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFdBQVc7QUFDWDtBQUNBO0FBQ0EsbUJBQW1CLHFEQUFhO0FBQ2hDO0FBQ0E7QUFDQSxhQUFhLGtEQUFNO0FBQ25CLEtBQUs7QUFDTCxxQkFBcUIsb0ZBQWM7QUFDbkM7QUFDQTtBQUNBLGVBQWUsd0RBQVE7QUFDdkI7O0FBRUE7QUFDQSxtQkFBbUIsNkNBQU07O0FBRXpCO0FBQ0EsRUFBRSxzRUFBeUI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsbUJBQW1CLHFEQUFhO0FBQ2hDOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIsb0RBQVk7QUFDL0I7O0FBRUE7QUFDQTtBQUNBLG1CQUFtQixvREFBWTtBQUMvQjtBQUNBOztBQUVBO0FBQ0EsMERBQTBELG9EQUFZO0FBQ3RFO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxnQkFBZ0IsbURBQVc7QUFDM0I7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0Esc0JBQXNCLHFEQUFhO0FBQ25DO0FBQ0Esc0JBQXNCLG9EQUFZO0FBQ2xDO0FBQ0Esc0JBQXNCLG9EQUFZO0FBQ2xDLGdCQUFnQixtREFBVztBQUMzQjtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsNEJBQTRCLHlDQUFZO0FBQ3hDLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCxtREFBVztBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxvQkFBb0Isb0RBQVksY0FBYyxrREFBVTtBQUN4RCxrQkFBa0Isb0ZBQWE7QUFDL0I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaG9va3NcXHVzZVN0YXR1cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IHsgdXNlRXZlbnQgfSBmcm9tICdyYy11dGlsJztcbmltcG9ydCB1c2VTdGF0ZSBmcm9tIFwicmMtdXRpbC9lcy9ob29rcy91c2VTdGF0ZVwiO1xuaW1wb3J0IHVzZVN5bmNTdGF0ZSBmcm9tIFwicmMtdXRpbC9lcy9ob29rcy91c2VTeW5jU3RhdGVcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU1RBVFVTX0FQUEVBUiwgU1RBVFVTX0VOVEVSLCBTVEFUVVNfTEVBVkUsIFNUQVRVU19OT05FLCBTVEVQX0FDVElWRSwgU1RFUF9QUkVQQVJFLCBTVEVQX1BSRVBBUkVELCBTVEVQX1NUQVJUIH0gZnJvbSBcIi4uL2ludGVyZmFjZVwiO1xuaW1wb3J0IHVzZURvbU1vdGlvbkV2ZW50cyBmcm9tIFwiLi91c2VEb21Nb3Rpb25FdmVudHNcIjtcbmltcG9ydCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IGZyb20gXCIuL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3RcIjtcbmltcG9ydCB1c2VTdGVwUXVldWUsIHsgRG9TdGVwLCBpc0FjdGl2ZSwgU2tpcFN0ZXAgfSBmcm9tIFwiLi91c2VTdGVwUXVldWVcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVN0YXR1cyhzdXBwb3J0TW90aW9uLCB2aXNpYmxlLCBnZXRFbGVtZW50LCBfcmVmKSB7XG4gIHZhciBfcmVmJG1vdGlvbkVudGVyID0gX3JlZi5tb3Rpb25FbnRlcixcbiAgICBtb3Rpb25FbnRlciA9IF9yZWYkbW90aW9uRW50ZXIgPT09IHZvaWQgMCA/IHRydWUgOiBfcmVmJG1vdGlvbkVudGVyLFxuICAgIF9yZWYkbW90aW9uQXBwZWFyID0gX3JlZi5tb3Rpb25BcHBlYXIsXG4gICAgbW90aW9uQXBwZWFyID0gX3JlZiRtb3Rpb25BcHBlYXIgPT09IHZvaWQgMCA/IHRydWUgOiBfcmVmJG1vdGlvbkFwcGVhcixcbiAgICBfcmVmJG1vdGlvbkxlYXZlID0gX3JlZi5tb3Rpb25MZWF2ZSxcbiAgICBtb3Rpb25MZWF2ZSA9IF9yZWYkbW90aW9uTGVhdmUgPT09IHZvaWQgMCA/IHRydWUgOiBfcmVmJG1vdGlvbkxlYXZlLFxuICAgIG1vdGlvbkRlYWRsaW5lID0gX3JlZi5tb3Rpb25EZWFkbGluZSxcbiAgICBtb3Rpb25MZWF2ZUltbWVkaWF0ZWx5ID0gX3JlZi5tb3Rpb25MZWF2ZUltbWVkaWF0ZWx5LFxuICAgIG9uQXBwZWFyUHJlcGFyZSA9IF9yZWYub25BcHBlYXJQcmVwYXJlLFxuICAgIG9uRW50ZXJQcmVwYXJlID0gX3JlZi5vbkVudGVyUHJlcGFyZSxcbiAgICBvbkxlYXZlUHJlcGFyZSA9IF9yZWYub25MZWF2ZVByZXBhcmUsXG4gICAgb25BcHBlYXJTdGFydCA9IF9yZWYub25BcHBlYXJTdGFydCxcbiAgICBvbkVudGVyU3RhcnQgPSBfcmVmLm9uRW50ZXJTdGFydCxcbiAgICBvbkxlYXZlU3RhcnQgPSBfcmVmLm9uTGVhdmVTdGFydCxcbiAgICBvbkFwcGVhckFjdGl2ZSA9IF9yZWYub25BcHBlYXJBY3RpdmUsXG4gICAgb25FbnRlckFjdGl2ZSA9IF9yZWYub25FbnRlckFjdGl2ZSxcbiAgICBvbkxlYXZlQWN0aXZlID0gX3JlZi5vbkxlYXZlQWN0aXZlLFxuICAgIG9uQXBwZWFyRW5kID0gX3JlZi5vbkFwcGVhckVuZCxcbiAgICBvbkVudGVyRW5kID0gX3JlZi5vbkVudGVyRW5kLFxuICAgIG9uTGVhdmVFbmQgPSBfcmVmLm9uTGVhdmVFbmQsXG4gICAgb25WaXNpYmxlQ2hhbmdlZCA9IF9yZWYub25WaXNpYmxlQ2hhbmdlZDtcbiAgLy8gVXNlZCBmb3Igb3V0ZXIgcmVuZGVyIHVzYWdlIHRvIGF2b2lkIGB2aXNpYmxlOiBmYWxzZSAmIHN0YXR1czogbm9uZWAgdG8gcmVuZGVyIG5vdGhpbmdcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKCksXG4gICAgX3VzZVN0YXRlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZSwgMiksXG4gICAgYXN5bmNWaXNpYmxlID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRBc3luY1Zpc2libGUgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN5bmNTdGF0ZSA9IHVzZVN5bmNTdGF0ZShTVEFUVVNfTk9ORSksXG4gICAgX3VzZVN5bmNTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3luY1N0YXRlLCAyKSxcbiAgICBnZXRTdGF0dXMgPSBfdXNlU3luY1N0YXRlMlswXSxcbiAgICBzZXRTdGF0dXMgPSBfdXNlU3luY1N0YXRlMlsxXTtcbiAgdmFyIF91c2VTdGF0ZTMgPSB1c2VTdGF0ZShudWxsKSxcbiAgICBfdXNlU3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlMywgMiksXG4gICAgc3R5bGUgPSBfdXNlU3RhdGU0WzBdLFxuICAgIHNldFN0eWxlID0gX3VzZVN0YXRlNFsxXTtcbiAgdmFyIGN1cnJlbnRTdGF0dXMgPSBnZXRTdGF0dXMoKTtcbiAgdmFyIG1vdW50ZWRSZWYgPSB1c2VSZWYoZmFsc2UpO1xuICB2YXIgZGVhZGxpbmVSZWYgPSB1c2VSZWYobnVsbCk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09IERvbSBOb2RlID09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBmdW5jdGlvbiBnZXREb21FbGVtZW50KCkge1xuICAgIHJldHVybiBnZXRFbGVtZW50KCk7XG4gIH1cblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PSBNb3Rpb24gRW5kID09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBhY3RpdmVSZWYgPSB1c2VSZWYoZmFsc2UpO1xuXG4gIC8qKlxuICAgKiBDbGVhbiB1cCBzdGF0dXMgJiBzdHlsZVxuICAgKi9cbiAgZnVuY3Rpb24gdXBkYXRlTW90aW9uRW5kU3RhdHVzKCkge1xuICAgIHNldFN0YXR1cyhTVEFUVVNfTk9ORSk7XG4gICAgc2V0U3R5bGUobnVsbCwgdHJ1ZSk7XG4gIH1cbiAgdmFyIG9uSW50ZXJuYWxNb3Rpb25FbmQgPSB1c2VFdmVudChmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICB2YXIgc3RhdHVzID0gZ2V0U3RhdHVzKCk7XG4gICAgLy8gRG8gbm90aGluZyBzaW5jZSBub3QgaW4gYW55IHRyYW5zaXRpb24gc3RhdHVzLlxuICAgIC8vIFRoaXMgbWF5IGhhcHBlbiB3aGVuIGBtb3Rpb25EZWFkbGluZWAgdHJpZ2dlci5cbiAgICBpZiAoc3RhdHVzID09PSBTVEFUVVNfTk9ORSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB2YXIgZWxlbWVudCA9IGdldERvbUVsZW1lbnQoKTtcbiAgICBpZiAoZXZlbnQgJiYgIWV2ZW50LmRlYWRsaW5lICYmIGV2ZW50LnRhcmdldCAhPT0gZWxlbWVudCkge1xuICAgICAgLy8gZXZlbnQgZXhpc3RzXG4gICAgICAvLyBub3QgaW5pdGlhdGVkIGJ5IGRlYWRsaW5lXG4gICAgICAvLyB0cmFuc2l0aW9uRW5kIG5vdCBmaXJlZCBieSBpbm5lciBlbGVtZW50c1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB2YXIgY3VycmVudEFjdGl2ZSA9IGFjdGl2ZVJlZi5jdXJyZW50O1xuICAgIHZhciBjYW5FbmQ7XG4gICAgaWYgKHN0YXR1cyA9PT0gU1RBVFVTX0FQUEVBUiAmJiBjdXJyZW50QWN0aXZlKSB7XG4gICAgICBjYW5FbmQgPSBvbkFwcGVhckVuZCA9PT0gbnVsbCB8fCBvbkFwcGVhckVuZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogb25BcHBlYXJFbmQoZWxlbWVudCwgZXZlbnQpO1xuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSBTVEFUVVNfRU5URVIgJiYgY3VycmVudEFjdGl2ZSkge1xuICAgICAgY2FuRW5kID0gb25FbnRlckVuZCA9PT0gbnVsbCB8fCBvbkVudGVyRW5kID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvbkVudGVyRW5kKGVsZW1lbnQsIGV2ZW50KTtcbiAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gU1RBVFVTX0xFQVZFICYmIGN1cnJlbnRBY3RpdmUpIHtcbiAgICAgIGNhbkVuZCA9IG9uTGVhdmVFbmQgPT09IG51bGwgfHwgb25MZWF2ZUVuZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogb25MZWF2ZUVuZChlbGVtZW50LCBldmVudCk7XG4gICAgfVxuXG4gICAgLy8gT25seSB1cGRhdGUgc3RhdHVzIHdoZW4gYGNhbkVuZGAgYW5kIG5vdCBkZXN0cm95ZWRcbiAgICBpZiAoY3VycmVudEFjdGl2ZSAmJiBjYW5FbmQgIT09IGZhbHNlKSB7XG4gICAgICB1cGRhdGVNb3Rpb25FbmRTdGF0dXMoKTtcbiAgICB9XG4gIH0pO1xuICB2YXIgX3VzZURvbU1vdGlvbkV2ZW50cyA9IHVzZURvbU1vdGlvbkV2ZW50cyhvbkludGVybmFsTW90aW9uRW5kKSxcbiAgICBfdXNlRG9tTW90aW9uRXZlbnRzMiA9IF9zbGljZWRUb0FycmF5KF91c2VEb21Nb3Rpb25FdmVudHMsIDEpLFxuICAgIHBhdGNoTW90aW9uRXZlbnRzID0gX3VzZURvbU1vdGlvbkV2ZW50czJbMF07XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gU3RlcCA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgZ2V0RXZlbnRIYW5kbGVycyA9IGZ1bmN0aW9uIGdldEV2ZW50SGFuZGxlcnModGFyZ2V0U3RhdHVzKSB7XG4gICAgc3dpdGNoICh0YXJnZXRTdGF0dXMpIHtcbiAgICAgIGNhc2UgU1RBVFVTX0FQUEVBUjpcbiAgICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KHt9LCBTVEVQX1BSRVBBUkUsIG9uQXBwZWFyUHJlcGFyZSksIFNURVBfU1RBUlQsIG9uQXBwZWFyU3RhcnQpLCBTVEVQX0FDVElWRSwgb25BcHBlYXJBY3RpdmUpO1xuICAgICAgY2FzZSBTVEFUVVNfRU5URVI6XG4gICAgICAgIHJldHVybiBfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgU1RFUF9QUkVQQVJFLCBvbkVudGVyUHJlcGFyZSksIFNURVBfU1RBUlQsIG9uRW50ZXJTdGFydCksIFNURVBfQUNUSVZFLCBvbkVudGVyQWN0aXZlKTtcbiAgICAgIGNhc2UgU1RBVFVTX0xFQVZFOlxuICAgICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoe30sIFNURVBfUFJFUEFSRSwgb25MZWF2ZVByZXBhcmUpLCBTVEVQX1NUQVJULCBvbkxlYXZlU3RhcnQpLCBTVEVQX0FDVElWRSwgb25MZWF2ZUFjdGl2ZSk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4ge307XG4gICAgfVxuICB9O1xuICB2YXIgZXZlbnRIYW5kbGVycyA9IFJlYWN0LnVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBnZXRFdmVudEhhbmRsZXJzKGN1cnJlbnRTdGF0dXMpO1xuICB9LCBbY3VycmVudFN0YXR1c10pO1xuICB2YXIgX3VzZVN0ZXBRdWV1ZSA9IHVzZVN0ZXBRdWV1ZShjdXJyZW50U3RhdHVzLCAhc3VwcG9ydE1vdGlvbiwgZnVuY3Rpb24gKG5ld1N0ZXApIHtcbiAgICAgIC8vIE9ubHkgcHJlcGFyZSBzdGVwIGNhbiBiZSBza2lwXG4gICAgICBpZiAobmV3U3RlcCA9PT0gU1RFUF9QUkVQQVJFKSB7XG4gICAgICAgIHZhciBvblByZXBhcmUgPSBldmVudEhhbmRsZXJzW1NURVBfUFJFUEFSRV07XG4gICAgICAgIGlmICghb25QcmVwYXJlKSB7XG4gICAgICAgICAgcmV0dXJuIFNraXBTdGVwO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvblByZXBhcmUoZ2V0RG9tRWxlbWVudCgpKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmVzdCBzdGVwIGlzIHN5bmMgdXBkYXRlXG4gICAgICBpZiAoc3RlcCBpbiBldmVudEhhbmRsZXJzKSB7XG4gICAgICAgIHZhciBfZXZlbnRIYW5kbGVycyRzdGVwO1xuICAgICAgICBzZXRTdHlsZSgoKF9ldmVudEhhbmRsZXJzJHN0ZXAgPSBldmVudEhhbmRsZXJzW3N0ZXBdKSA9PT0gbnVsbCB8fCBfZXZlbnRIYW5kbGVycyRzdGVwID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXZlbnRIYW5kbGVycyRzdGVwLmNhbGwoZXZlbnRIYW5kbGVycywgZ2V0RG9tRWxlbWVudCgpLCBudWxsKSkgfHwgbnVsbCk7XG4gICAgICB9XG4gICAgICBpZiAoc3RlcCA9PT0gU1RFUF9BQ1RJVkUgJiYgY3VycmVudFN0YXR1cyAhPT0gU1RBVFVTX05PTkUpIHtcbiAgICAgICAgLy8gUGF0Y2ggZXZlbnRzIHdoZW4gbW90aW9uIG5lZWRlZFxuICAgICAgICBwYXRjaE1vdGlvbkV2ZW50cyhnZXREb21FbGVtZW50KCkpO1xuICAgICAgICBpZiAobW90aW9uRGVhZGxpbmUgPiAwKSB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KGRlYWRsaW5lUmVmLmN1cnJlbnQpO1xuICAgICAgICAgIGRlYWRsaW5lUmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIG9uSW50ZXJuYWxNb3Rpb25FbmQoe1xuICAgICAgICAgICAgICBkZWFkbGluZTogdHJ1ZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSwgbW90aW9uRGVhZGxpbmUpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoc3RlcCA9PT0gU1RFUF9QUkVQQVJFRCkge1xuICAgICAgICB1cGRhdGVNb3Rpb25FbmRTdGF0dXMoKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBEb1N0ZXA7XG4gICAgfSksXG4gICAgX3VzZVN0ZXBRdWV1ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RlcFF1ZXVlLCAyKSxcbiAgICBzdGFydFN0ZXAgPSBfdXNlU3RlcFF1ZXVlMlswXSxcbiAgICBzdGVwID0gX3VzZVN0ZXBRdWV1ZTJbMV07XG4gIHZhciBhY3RpdmUgPSBpc0FjdGl2ZShzdGVwKTtcbiAgYWN0aXZlUmVmLmN1cnJlbnQgPSBhY3RpdmU7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PSBTdGF0dXMgPT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgdmlzaWJsZVJlZiA9IHVzZVJlZihudWxsKTtcblxuICAvLyBVcGRhdGUgd2l0aCBuZXcgc3RhdHVzXG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIC8vIFdoZW4gdXNlIFN1c3BlbnNlLCB0aGUgYHZpc2libGVgIHdpbGwgcmVwZWF0IHRyaWdnZXIsXG4gICAgLy8gQnV0IG5vdCByZWFsIGNoYW5nZSBvZiB0aGUgYHZpc2libGVgLCB3ZSBuZWVkIHRvIHNraXAgaXQuXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FudC1kZXNpZ24vYW50LWRlc2lnbi9pc3N1ZXMvNDQzNzlcbiAgICBpZiAobW91bnRlZFJlZi5jdXJyZW50ICYmIHZpc2libGVSZWYuY3VycmVudCA9PT0gdmlzaWJsZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXRBc3luY1Zpc2libGUodmlzaWJsZSk7XG4gICAgdmFyIGlzTW91bnRlZCA9IG1vdW50ZWRSZWYuY3VycmVudDtcbiAgICBtb3VudGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xuXG4gICAgLy8gaWYgKCFzdXBwb3J0TW90aW9uKSB7XG4gICAgLy8gICByZXR1cm47XG4gICAgLy8gfVxuXG4gICAgdmFyIG5leHRTdGF0dXM7XG5cbiAgICAvLyBBcHBlYXJcbiAgICBpZiAoIWlzTW91bnRlZCAmJiB2aXNpYmxlICYmIG1vdGlvbkFwcGVhcikge1xuICAgICAgbmV4dFN0YXR1cyA9IFNUQVRVU19BUFBFQVI7XG4gICAgfVxuXG4gICAgLy8gRW50ZXJcbiAgICBpZiAoaXNNb3VudGVkICYmIHZpc2libGUgJiYgbW90aW9uRW50ZXIpIHtcbiAgICAgIG5leHRTdGF0dXMgPSBTVEFUVVNfRU5URVI7XG4gICAgfVxuXG4gICAgLy8gTGVhdmVcbiAgICBpZiAoaXNNb3VudGVkICYmICF2aXNpYmxlICYmIG1vdGlvbkxlYXZlIHx8ICFpc01vdW50ZWQgJiYgbW90aW9uTGVhdmVJbW1lZGlhdGVseSAmJiAhdmlzaWJsZSAmJiBtb3Rpb25MZWF2ZSkge1xuICAgICAgbmV4dFN0YXR1cyA9IFNUQVRVU19MRUFWRTtcbiAgICB9XG4gICAgdmFyIG5leHRFdmVudEhhbmRsZXJzID0gZ2V0RXZlbnRIYW5kbGVycyhuZXh0U3RhdHVzKTtcblxuICAgIC8vIFVwZGF0ZSB0byBuZXh0IHN0YXR1c1xuICAgIGlmIChuZXh0U3RhdHVzICYmIChzdXBwb3J0TW90aW9uIHx8IG5leHRFdmVudEhhbmRsZXJzW1NURVBfUFJFUEFSRV0pKSB7XG4gICAgICBzZXRTdGF0dXMobmV4dFN0YXR1cyk7XG4gICAgICBzdGFydFN0ZXAoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gU2V0IGJhY2sgaW4gY2FzZSBubyBtb3Rpb24gYnV0IHByZXYgc3RhdHVzIGhhcyBwcmVwYXJlIHN0ZXBcbiAgICAgIHNldFN0YXR1cyhTVEFUVVNfTk9ORSk7XG4gICAgfVxuICAgIHZpc2libGVSZWYuY3VycmVudCA9IHZpc2libGU7XG4gIH0sIFt2aXNpYmxlXSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PSBFZmZlY3QgPT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBSZXNldCB3aGVuIG1vdGlvbiBjaGFuZ2VkXG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKFxuICAgIC8vIENhbmNlbCBhcHBlYXJcbiAgICBjdXJyZW50U3RhdHVzID09PSBTVEFUVVNfQVBQRUFSICYmICFtb3Rpb25BcHBlYXIgfHxcbiAgICAvLyBDYW5jZWwgZW50ZXJcbiAgICBjdXJyZW50U3RhdHVzID09PSBTVEFUVVNfRU5URVIgJiYgIW1vdGlvbkVudGVyIHx8XG4gICAgLy8gQ2FuY2VsIGxlYXZlXG4gICAgY3VycmVudFN0YXR1cyA9PT0gU1RBVFVTX0xFQVZFICYmICFtb3Rpb25MZWF2ZSkge1xuICAgICAgc2V0U3RhdHVzKFNUQVRVU19OT05FKTtcbiAgICB9XG4gIH0sIFttb3Rpb25BcHBlYXIsIG1vdGlvbkVudGVyLCBtb3Rpb25MZWF2ZV0pO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBtb3VudGVkUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgIGNsZWFyVGltZW91dChkZWFkbGluZVJlZi5jdXJyZW50KTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8gVHJpZ2dlciBgb25WaXNpYmxlQ2hhbmdlZGBcbiAgdmFyIGZpcnN0TW91bnRDaGFuZ2VSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIC8vIFt2aXNpYmxlICYgbW90aW9uIG5vdCBlbmRdID0+IFshdmlzaWJsZSAmIG1vdGlvbiBlbmRdIHN0aWxsIG5lZWQgdHJpZ2dlciBvblZpc2libGVDaGFuZ2VkXG4gICAgaWYgKGFzeW5jVmlzaWJsZSkge1xuICAgICAgZmlyc3RNb3VudENoYW5nZVJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICB9XG4gICAgaWYgKGFzeW5jVmlzaWJsZSAhPT0gdW5kZWZpbmVkICYmIGN1cnJlbnRTdGF0dXMgPT09IFNUQVRVU19OT05FKSB7XG4gICAgICAvLyBTa2lwIGZpcnN0IHJlbmRlciBpcyBpbnZpc2libGUgc2luY2UgaXQncyBub3RoaW5nIGNoYW5nZWRcbiAgICAgIGlmIChmaXJzdE1vdW50Q2hhbmdlUmVmLmN1cnJlbnQgfHwgYXN5bmNWaXNpYmxlKSB7XG4gICAgICAgIG9uVmlzaWJsZUNoYW5nZWQgPT09IG51bGwgfHwgb25WaXNpYmxlQ2hhbmdlZCA9PT0gdm9pZCAwIHx8IG9uVmlzaWJsZUNoYW5nZWQoYXN5bmNWaXNpYmxlKTtcbiAgICAgIH1cbiAgICAgIGZpcnN0TW91bnRDaGFuZ2VSZWYuY3VycmVudCA9IHRydWU7XG4gICAgfVxuICB9LCBbYXN5bmNWaXNpYmxlLCBjdXJyZW50U3RhdHVzXSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PSBTdHlsZXMgPT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgbWVyZ2VkU3R5bGUgPSBzdHlsZTtcbiAgaWYgKGV2ZW50SGFuZGxlcnNbU1RFUF9QUkVQQVJFXSAmJiBzdGVwID09PSBTVEVQX1NUQVJUKSB7XG4gICAgbWVyZ2VkU3R5bGUgPSBfb2JqZWN0U3ByZWFkKHtcbiAgICAgIHRyYW5zaXRpb246ICdub25lJ1xuICAgIH0sIG1lcmdlZFN0eWxlKTtcbiAgfVxuICByZXR1cm4gW2N1cnJlbnRTdGF0dXMsIHN0ZXAsIG1lcmdlZFN0eWxlLCBhc3luY1Zpc2libGUgIT09IG51bGwgJiYgYXN5bmNWaXNpYmxlICE9PSB2b2lkIDAgPyBhc3luY1Zpc2libGUgOiB2aXNpYmxlXTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/hooks/useStatus.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/hooks/useStepQueue.js":
/*!**********************************************************!*\
  !*** ../node_modules/rc-motion/es/hooks/useStepQueue.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoStep: () => (/* binding */ DoStep),\n/* harmony export */   SkipStep: () => (/* binding */ SkipStep),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isActive: () => (/* binding */ isActive)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/../node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interface */ \"(ssr)/../node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/../node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useNextFrame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useNextFrame */ \"(ssr)/../node_modules/rc-motion/es/hooks/useNextFrame.js\");\n\n\n\n\n\n\nvar FULL_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_START, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARED];\n\n/** Skip current step */\nvar SkipStep = false;\n/** Current step should be update in */\nvar DoStep = true;\nfunction isActive(step) {\n  return step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE || step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (status, prepareOnly, callback) {\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = (0,_useNextFrame__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n    _useNextFrame2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    if (step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE && step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VTdGVwUXVldWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBc0U7QUFDckI7QUFDbEI7QUFDZ0Y7QUFDM0M7QUFDMUI7QUFDMUMsdUJBQXVCLG9EQUFZLEVBQUUsa0RBQVUsRUFBRSxtREFBVyxFQUFFLHNEQUFjO0FBQzVFLHlCQUF5QixvREFBWSxFQUFFLHFEQUFhOztBQUVwRDtBQUNPO0FBQ1A7QUFDTztBQUNBO0FBQ1Asa0JBQWtCLG1EQUFXLGFBQWEsc0RBQWM7QUFDeEQ7QUFDQSxpRUFBZ0I7QUFDaEIsa0JBQWtCLHFFQUFRLENBQUMsaURBQVM7QUFDcEMsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxzQkFBc0IseURBQVk7QUFDbEMscUJBQXFCLG9GQUFjO0FBQ25DO0FBQ0E7QUFDQTtBQUNBLFlBQVksb0RBQVk7QUFDeEI7QUFDQTtBQUNBLEVBQUUsc0VBQXlCO0FBQzNCLGlCQUFpQixpREFBUyxhQUFhLHNEQUFjO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxDQUFDIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaG9va3NcXHVzZVN0ZXBRdWV1ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCB1c2VTdGF0ZSBmcm9tIFwicmMtdXRpbC9lcy9ob29rcy91c2VTdGF0ZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU1RFUF9BQ1RJVkFURUQsIFNURVBfQUNUSVZFLCBTVEVQX05PTkUsIFNURVBfUFJFUEFSRSwgU1RFUF9QUkVQQVJFRCwgU1RFUF9TVEFSVCB9IGZyb20gXCIuLi9pbnRlcmZhY2VcIjtcbmltcG9ydCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IGZyb20gXCIuL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3RcIjtcbmltcG9ydCB1c2VOZXh0RnJhbWUgZnJvbSBcIi4vdXNlTmV4dEZyYW1lXCI7XG52YXIgRlVMTF9TVEVQX1FVRVVFID0gW1NURVBfUFJFUEFSRSwgU1RFUF9TVEFSVCwgU1RFUF9BQ1RJVkUsIFNURVBfQUNUSVZBVEVEXTtcbnZhciBTSU1QTEVfU1RFUF9RVUVVRSA9IFtTVEVQX1BSRVBBUkUsIFNURVBfUFJFUEFSRURdO1xuXG4vKiogU2tpcCBjdXJyZW50IHN0ZXAgKi9cbmV4cG9ydCB2YXIgU2tpcFN0ZXAgPSBmYWxzZTtcbi8qKiBDdXJyZW50IHN0ZXAgc2hvdWxkIGJlIHVwZGF0ZSBpbiAqL1xuZXhwb3J0IHZhciBEb1N0ZXAgPSB0cnVlO1xuZXhwb3J0IGZ1bmN0aW9uIGlzQWN0aXZlKHN0ZXApIHtcbiAgcmV0dXJuIHN0ZXAgPT09IFNURVBfQUNUSVZFIHx8IHN0ZXAgPT09IFNURVBfQUNUSVZBVEVEO1xufVxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIChzdGF0dXMsIHByZXBhcmVPbmx5LCBjYWxsYmFjaykge1xuICB2YXIgX3VzZVN0YXRlID0gdXNlU3RhdGUoU1RFUF9OT05FKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBzdGVwID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRTdGVwID0gX3VzZVN0YXRlMlsxXTtcbiAgdmFyIF91c2VOZXh0RnJhbWUgPSB1c2VOZXh0RnJhbWUoKSxcbiAgICBfdXNlTmV4dEZyYW1lMiA9IF9zbGljZWRUb0FycmF5KF91c2VOZXh0RnJhbWUsIDIpLFxuICAgIG5leHRGcmFtZSA9IF91c2VOZXh0RnJhbWUyWzBdLFxuICAgIGNhbmNlbE5leHRGcmFtZSA9IF91c2VOZXh0RnJhbWUyWzFdO1xuICBmdW5jdGlvbiBzdGFydFF1ZXVlKCkge1xuICAgIHNldFN0ZXAoU1RFUF9QUkVQQVJFLCB0cnVlKTtcbiAgfVxuICB2YXIgU1RFUF9RVUVVRSA9IHByZXBhcmVPbmx5ID8gU0lNUExFX1NURVBfUVVFVUUgOiBGVUxMX1NURVBfUVVFVUU7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmIChzdGVwICE9PSBTVEVQX05PTkUgJiYgc3RlcCAhPT0gU1RFUF9BQ1RJVkFURUQpIHtcbiAgICAgIHZhciBpbmRleCA9IFNURVBfUVVFVUUuaW5kZXhPZihzdGVwKTtcbiAgICAgIHZhciBuZXh0U3RlcCA9IFNURVBfUVVFVUVbaW5kZXggKyAxXTtcbiAgICAgIHZhciByZXN1bHQgPSBjYWxsYmFjayhzdGVwKTtcbiAgICAgIGlmIChyZXN1bHQgPT09IFNraXBTdGVwKSB7XG4gICAgICAgIC8vIFNraXAgd2hlbiBubyBuZWVkZWRcbiAgICAgICAgc2V0U3RlcChuZXh0U3RlcCwgdHJ1ZSk7XG4gICAgICB9IGVsc2UgaWYgKG5leHRTdGVwKSB7XG4gICAgICAgIC8vIERvIGFzIGZyYW1lIGZvciBzdGVwIHVwZGF0ZVxuICAgICAgICBuZXh0RnJhbWUoZnVuY3Rpb24gKGluZm8pIHtcbiAgICAgICAgICBmdW5jdGlvbiBkb05leHQoKSB7XG4gICAgICAgICAgICAvLyBTa2lwIHNpbmNlIGN1cnJlbnQgcXVldWUgaXMgb29kXG4gICAgICAgICAgICBpZiAoaW5mby5pc0NhbmNlbGVkKCkpIHJldHVybjtcbiAgICAgICAgICAgIHNldFN0ZXAobmV4dFN0ZXAsIHRydWUpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAocmVzdWx0ID09PSB0cnVlKSB7XG4gICAgICAgICAgICBkb05leHQoKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gT25seSBwcm9taXNlIHNob3VsZCBiZSBhc3luY1xuICAgICAgICAgICAgUHJvbWlzZS5yZXNvbHZlKHJlc3VsdCkudGhlbihkb05leHQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdHVzLCBzdGVwXSk7XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIGNhbmNlbE5leHRGcmFtZSgpO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgcmV0dXJuIFtzdGFydFF1ZXVlLCBzdGVwXTtcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/hooks/useStepQueue.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/index.js":
/*!*********************************************!*\
  !*** ../node_modules/rc-motion/es/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSMotionList: () => (/* reexport safe */ _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Provider: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/../node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CSSMotionList */ \"(ssr)/../node_modules/rc-motion/es/CSSMotionList.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context */ \"(ssr)/../node_modules/rc-motion/es/context.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_CSSMotion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0M7QUFDUTtBQUNJO0FBQ3ZCO0FBQ3pCLGlFQUFlLGtEQUFTIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENTU01vdGlvbiBmcm9tIFwiLi9DU1NNb3Rpb25cIjtcbmltcG9ydCBDU1NNb3Rpb25MaXN0IGZyb20gXCIuL0NTU01vdGlvbkxpc3RcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUHJvdmlkZXIgfSBmcm9tIFwiLi9jb250ZXh0XCI7XG5leHBvcnQgeyBDU1NNb3Rpb25MaXN0IH07XG5leHBvcnQgZGVmYXVsdCBDU1NNb3Rpb247Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/interface.js":
/*!*************************************************!*\
  !*** ../node_modules/rc-motion/es/interface.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_APPEAR: () => (/* binding */ STATUS_APPEAR),\n/* harmony export */   STATUS_ENTER: () => (/* binding */ STATUS_ENTER),\n/* harmony export */   STATUS_LEAVE: () => (/* binding */ STATUS_LEAVE),\n/* harmony export */   STATUS_NONE: () => (/* binding */ STATUS_NONE),\n/* harmony export */   STEP_ACTIVATED: () => (/* binding */ STEP_ACTIVATED),\n/* harmony export */   STEP_ACTIVE: () => (/* binding */ STEP_ACTIVE),\n/* harmony export */   STEP_NONE: () => (/* binding */ STEP_NONE),\n/* harmony export */   STEP_PREPARE: () => (/* binding */ STEP_PREPARE),\n/* harmony export */   STEP_PREPARED: () => (/* binding */ STEP_PREPARED),\n/* harmony export */   STEP_START: () => (/* binding */ STEP_START)\n/* harmony export */ });\nvar STATUS_NONE = 'none';\nvar STATUS_APPEAR = 'appear';\nvar STATUS_ENTER = 'enter';\nvar STATUS_LEAVE = 'leave';\nvar STEP_NONE = 'none';\nvar STEP_PREPARE = 'prepare';\nvar STEP_START = 'start';\nvar STEP_ACTIVE = 'active';\nvar STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nvar STEP_PREPARED = 'prepared';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9pbnRlcmZhY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIko6XFxhdWdtZW50XFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxpbnRlcmZhY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBTVEFUVVNfTk9ORSA9ICdub25lJztcbmV4cG9ydCB2YXIgU1RBVFVTX0FQUEVBUiA9ICdhcHBlYXInO1xuZXhwb3J0IHZhciBTVEFUVVNfRU5URVIgPSAnZW50ZXInO1xuZXhwb3J0IHZhciBTVEFUVVNfTEVBVkUgPSAnbGVhdmUnO1xuZXhwb3J0IHZhciBTVEVQX05PTkUgPSAnbm9uZSc7XG5leHBvcnQgdmFyIFNURVBfUFJFUEFSRSA9ICdwcmVwYXJlJztcbmV4cG9ydCB2YXIgU1RFUF9TVEFSVCA9ICdzdGFydCc7XG5leHBvcnQgdmFyIFNURVBfQUNUSVZFID0gJ2FjdGl2ZSc7XG5leHBvcnQgdmFyIFNURVBfQUNUSVZBVEVEID0gJ2VuZCc7XG4vKipcbiAqIFVzZWQgZm9yIGRpc2FibGVkIG1vdGlvbiBjYXNlLlxuICogUHJlcGFyZSBzdGFnZSB3aWxsIHN0aWxsIHdvcmsgYnV0IHN0YXJ0ICYgYWN0aXZlIHdpbGwgYmUgc2tpcHBlZC5cbiAqL1xuZXhwb3J0IHZhciBTVEVQX1BSRVBBUkVEID0gJ3ByZXBhcmVkJzsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/interface.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/util/diff.js":
/*!*************************************************!*\
  !*** ../node_modules/rc-motion/es/util/diff.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_ADD: () => (/* binding */ STATUS_ADD),\n/* harmony export */   STATUS_KEEP: () => (/* binding */ STATUS_KEEP),\n/* harmony export */   STATUS_REMOVE: () => (/* binding */ STATUS_REMOVE),\n/* harmony export */   STATUS_REMOVED: () => (/* binding */ STATUS_REMOVED),\n/* harmony export */   diffKeys: () => (/* binding */ diffKeys),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   wrapKeyToObject: () => (/* binding */ wrapKeyToObject)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\n\nvar STATUS_ADD = 'add';\nvar STATUS_KEEP = 'keep';\nvar STATUS_REMOVE = 'remove';\nvar STATUS_REMOVED = 'removed';\nfunction wrapKeyToObject(key) {\n  var keyObj;\n  if (key && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nfunction parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nfunction diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/util/diff.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-motion/es/util/motion.js":
/*!***************************************************!*\
  !*** ../node_modules/rc-motion/es/util/motion.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationEndName: () => (/* binding */ animationEndName),\n/* harmony export */   getTransitionName: () => (/* binding */ getTransitionName),\n/* harmony export */   getVendorPrefixedEventName: () => (/* binding */ getVendorPrefixedEventName),\n/* harmony export */   getVendorPrefixes: () => (/* binding */ getVendorPrefixes),\n/* harmony export */   supportTransition: () => (/* binding */ supportTransition),\n/* harmony export */   transitionEndName: () => (/* binding */ transitionEndName)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/../node_modules/rc-util/es/Dom/canUseDom.js\");\n\n\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nfunction getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif ((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nfunction getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nvar supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nvar animationEndName = internalAnimationEndName || 'animationend';\nvar transitionEndName = internalTransitionEndName || 'transitionend';\nfunction getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-motion/es/util/motion.js\n");

/***/ })

};
;