"""
工业智能体平台 - 知识管理服务
负责行业知识库、产品数字孪生、知识图谱的管理
"""

from fastapi import FastAPI, HTTPException, Depends, UploadFile, File, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from motor.motor_asyncio import AsyncIOMotorClient
import json
import logging
from datetime import datetime
import os
from contextlib import asynccontextmanager
import httpx
import numpy as np
from sentence_transformers import SentenceTransformer
import weaviate
from elasticsearch import AsyncElasticsearch
import PyPDF2
import docx
from pathlib import Path
import hashlib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
mongo_client = None
mongo_db = None
weaviate_client = None
elasticsearch_client = None
embedding_model = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, mongo_client, mongo_db, weaviate_client, elasticsearch_client, embedding_model
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # MongoDB连接
    mongo_client = AsyncIOMotorClient(
        f"mongodb://{os.getenv('MONGO_USER', 'admin')}:{os.getenv('MONGO_PASSWORD', 'admin123')}@{os.getenv('MONGO_HOST', 'localhost')}:{os.getenv('MONGO_PORT', '27017')}"
    )
    mongo_db = mongo_client[os.getenv("MONGO_DB", "industry_documents")]
    
    # Weaviate连接
    weaviate_client = weaviate.Client(
        url=f"http://{os.getenv('WEAVIATE_HOST', 'localhost')}:{os.getenv('WEAVIATE_PORT', '8080')}"
    )
    
    # Elasticsearch连接
    elasticsearch_client = AsyncElasticsearch([
        f"http://{os.getenv('ELASTICSEARCH_HOST', 'localhost')}:{os.getenv('ELASTICSEARCH_PORT', '9200')}"
    ])
    
    # 加载嵌入模型
    embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # 初始化知识库结构
    await initialize_knowledge_base()
    
    logger.info("Knowledge service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if mongo_client:
        mongo_client.close()
    if elasticsearch_client:
        await elasticsearch_client.close()
    
    logger.info("Knowledge service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="知识管理服务",
    description="行业知识库、产品数字孪生、知识图谱管理",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class KnowledgeArticle(BaseModel):
    title: str
    content: str
    category: str
    tags: List[str] = []
    source: Optional[str] = None
    author: Optional[str] = None
    confidence: float = 1.0
    metadata: Dict[str, Any] = {}

class ProductModel(BaseModel):
    product_code: str
    product_name: str
    category: str
    specifications: Dict[str, Any]
    cad_model_url: Optional[str] = None
    process_flow: List[Dict[str, Any]] = []
    materials: List[str] = []
    quality_standards: List[str] = []

class ProcessKnowledge(BaseModel):
    process_name: str
    process_type: str
    description: str
    parameters: Dict[str, Any]
    best_practices: List[str] = []
    common_issues: List[str] = []
    troubleshooting: Dict[str, str] = {}

class QualityStandard(BaseModel):
    standard_code: str
    standard_name: str
    category: str
    requirements: Dict[str, Any]
    test_methods: List[str] = []
    acceptance_criteria: Dict[str, Any] = {}

class SearchQuery(BaseModel):
    query: str
    category: Optional[str] = None
    tags: List[str] = []
    limit: int = 10
    similarity_threshold: float = 0.7

async def initialize_knowledge_base():
    """初始化知识库结构"""
    
    # 创建Weaviate schema
    try:
        # 知识文章类
        article_class = {
            "class": "KnowledgeArticle",
            "description": "Industrial knowledge articles",
            "properties": [
                {"name": "title", "dataType": ["text"]},
                {"name": "content", "dataType": ["text"]},
                {"name": "category", "dataType": ["string"]},
                {"name": "tags", "dataType": ["string[]"]},
                {"name": "source", "dataType": ["string"]},
                {"name": "author", "dataType": ["string"]},
                {"name": "confidence", "dataType": ["number"]},
                {"name": "created_at", "dataType": ["date"]},
            ]
        }
        
        # 产品模型类
        product_class = {
            "class": "ProductModel",
            "description": "Product digital twin models",
            "properties": [
                {"name": "product_code", "dataType": ["string"]},
                {"name": "product_name", "dataType": ["text"]},
                {"name": "category", "dataType": ["string"]},
                {"name": "description", "dataType": ["text"]},
                {"name": "specifications", "dataType": ["text"]},
                {"name": "materials", "dataType": ["string[]"]},
            ]
        }
        
        # 工艺知识类
        process_class = {
            "class": "ProcessKnowledge",
            "description": "Manufacturing process knowledge",
            "properties": [
                {"name": "process_name", "dataType": ["text"]},
                {"name": "process_type", "dataType": ["string"]},
                {"name": "description", "dataType": ["text"]},
                {"name": "parameters", "dataType": ["text"]},
                {"name": "best_practices", "dataType": ["text"]},
            ]
        }
        
        # 检查并创建类
        existing_classes = weaviate_client.schema.get()["classes"]
        existing_class_names = [cls["class"] for cls in existing_classes]
        
        for class_def in [article_class, product_class, process_class]:
            if class_def["class"] not in existing_class_names:
                weaviate_client.schema.create_class(class_def)
                logger.info(f"Created Weaviate class: {class_def['class']}")
        
    except Exception as e:
        logger.error(f"Error initializing Weaviate schema: {e}")
    
    # 创建Elasticsearch索引
    try:
        indices = [
            {
                "index": "knowledge_articles",
                "mapping": {
                    "properties": {
                        "title": {"type": "text", "analyzer": "standard"},
                        "content": {"type": "text", "analyzer": "standard"},
                        "category": {"type": "keyword"},
                        "tags": {"type": "keyword"},
                        "embedding": {"type": "dense_vector", "dims": 384},
                        "created_at": {"type": "date"}
                    }
                }
            },
            {
                "index": "product_models",
                "mapping": {
                    "properties": {
                        "product_code": {"type": "keyword"},
                        "product_name": {"type": "text"},
                        "category": {"type": "keyword"},
                        "specifications": {"type": "object"},
                        "embedding": {"type": "dense_vector", "dims": 384},
                        "created_at": {"type": "date"}
                    }
                }
            }
        ]
        
        for index_config in indices:
            index_name = index_config["index"]
            if not await elasticsearch_client.indices.exists(index=index_name):
                await elasticsearch_client.indices.create(
                    index=index_name,
                    body={"mappings": index_config["mapping"]}
                )
                logger.info(f"Created Elasticsearch index: {index_name}")
        
    except Exception as e:
        logger.error(f"Error initializing Elasticsearch indices: {e}")

class KnowledgeManager:
    """知识管理器"""
    
    @staticmethod
    async def add_knowledge_article(article: KnowledgeArticle) -> str:
        """添加知识文章"""
        try:
            # 生成嵌入向量
            embedding = embedding_model.encode(f"{article.title} {article.content}")
            
            # 存储到PostgreSQL
            async with db_pool.acquire() as conn:
                article_id = await conn.fetchval(
                    """
                    INSERT INTO knowledge_articles (title, content, category, tags, author, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING id
                    """,
                    article.title,
                    article.content,
                    article.category,
                    article.tags,
                    article.author,
                    datetime.utcnow()
                )
            
            # 存储到Weaviate
            weaviate_client.data_object.create(
                data_object={
                    "title": article.title,
                    "content": article.content,
                    "category": article.category,
                    "tags": article.tags,
                    "source": article.source,
                    "author": article.author,
                    "confidence": article.confidence,
                    "created_at": datetime.utcnow().isoformat(),
                },
                class_name="KnowledgeArticle",
                uuid=str(article_id)
            )
            
            # 存储到Elasticsearch
            await elasticsearch_client.index(
                index="knowledge_articles",
                id=str(article_id),
                body={
                    "title": article.title,
                    "content": article.content,
                    "category": article.category,
                    "tags": article.tags,
                    "embedding": embedding.tolist(),
                    "created_at": datetime.utcnow().isoformat()
                }
            )
            
            logger.info(f"Added knowledge article: {article.title}")
            return str(article_id)
            
        except Exception as e:
            logger.error(f"Error adding knowledge article: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def search_knowledge(query: SearchQuery) -> List[Dict[str, Any]]:
        """搜索知识"""
        try:
            results = []
            
            # 向量搜索
            query_embedding = embedding_model.encode(query.query)
            
            # Weaviate语义搜索
            weaviate_results = weaviate_client.query.get("KnowledgeArticle", [
                "title", "content", "category", "tags", "confidence"
            ]).with_near_vector({
                "vector": query_embedding.tolist(),
                "certainty": query.similarity_threshold
            }).with_limit(query.limit).do()
            
            if "data" in weaviate_results and "Get" in weaviate_results["data"]:
                for item in weaviate_results["data"]["Get"]["KnowledgeArticle"]:
                    results.append({
                        "source": "weaviate",
                        "type": "semantic",
                        "score": item.get("_additional", {}).get("certainty", 0),
                        **item
                    })
            
            # Elasticsearch全文搜索
            es_query = {
                "query": {
                    "bool": {
                        "should": [
                            {"match": {"title": {"query": query.query, "boost": 2}}},
                            {"match": {"content": query.query}},
                        ]
                    }
                },
                "size": query.limit
            }
            
            if query.category:
                es_query["query"]["bool"]["filter"] = [
                    {"term": {"category": query.category}}
                ]
            
            if query.tags:
                if "filter" not in es_query["query"]["bool"]:
                    es_query["query"]["bool"]["filter"] = []
                es_query["query"]["bool"]["filter"].append(
                    {"terms": {"tags": query.tags}}
                )
            
            es_results = await elasticsearch_client.search(
                index="knowledge_articles",
                body=es_query
            )
            
            for hit in es_results["hits"]["hits"]:
                results.append({
                    "source": "elasticsearch",
                    "type": "fulltext",
                    "score": hit["_score"],
                    **hit["_source"]
                })
            
            # 去重并排序
            seen_titles = set()
            unique_results = []
            
            for result in sorted(results, key=lambda x: x["score"], reverse=True):
                if result["title"] not in seen_titles:
                    seen_titles.add(result["title"])
                    unique_results.append(result)
            
            return unique_results[:query.limit]
            
        except Exception as e:
            logger.error(f"Error searching knowledge: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def add_product_model(product: ProductModel) -> str:
        """添加产品模型"""
        try:
            # 存储到PostgreSQL
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO products (product_code, product_name, category, specifications, created_at)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (product_code) DO UPDATE SET
                        product_name = EXCLUDED.product_name,
                        category = EXCLUDED.category,
                        specifications = EXCLUDED.specifications,
                        updated_at = CURRENT_TIMESTAMP
                    """,
                    product.product_code,
                    product.product_name,
                    product.category,
                    json.dumps(product.specifications),
                    datetime.utcnow()
                )
            
            # 存储到MongoDB（详细模型数据）
            product_doc = {
                "product_code": product.product_code,
                "product_name": product.product_name,
                "category": product.category,
                "specifications": product.specifications,
                "cad_model_url": product.cad_model_url,
                "process_flow": product.process_flow,
                "materials": product.materials,
                "quality_standards": product.quality_standards,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            await mongo_db.product_models.replace_one(
                {"product_code": product.product_code},
                product_doc,
                upsert=True
            )
            
            # 生成嵌入并存储到向量数据库
            description = f"{product.product_name} {product.category} {' '.join(product.materials)}"
            embedding = embedding_model.encode(description)
            
            weaviate_client.data_object.create(
                data_object={
                    "product_code": product.product_code,
                    "product_name": product.product_name,
                    "category": product.category,
                    "description": description,
                    "specifications": json.dumps(product.specifications),
                    "materials": product.materials,
                },
                class_name="ProductModel",
                uuid=hashlib.md5(product.product_code.encode()).hexdigest()
            )
            
            logger.info(f"Added product model: {product.product_code}")
            return product.product_code
            
        except Exception as e:
            logger.error(f"Error adding product model: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def get_product_model(product_code: str) -> Optional[Dict[str, Any]]:
        """获取产品模型"""
        try:
            # 从MongoDB获取详细信息
            product_doc = await mongo_db.product_models.find_one(
                {"product_code": product_code}
            )
            
            if product_doc:
                # 转换ObjectId为字符串
                product_doc["_id"] = str(product_doc["_id"])
                return product_doc
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting product model: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "knowledge-service"}

@app.post("/knowledge/articles")
async def create_knowledge_article(article: KnowledgeArticle):
    """创建知识文章"""
    article_id = await KnowledgeManager.add_knowledge_article(article)
    return {"message": "Knowledge article created", "id": article_id}

@app.post("/knowledge/search")
async def search_knowledge(query: SearchQuery):
    """搜索知识"""
    results = await KnowledgeManager.search_knowledge(query)
    return {"results": results, "count": len(results)}

@app.get("/knowledge/articles")
async def list_knowledge_articles(
    category: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
):
    """获取知识文章列表"""
    async with db_pool.acquire() as conn:
        query = "SELECT * FROM knowledge_articles"
        params = []
        
        if category:
            query += " WHERE category = $1"
            params.append(category)
        
        query += " ORDER BY created_at DESC LIMIT $" + str(len(params) + 1) + " OFFSET $" + str(len(params) + 2)
        params.extend([limit, offset])
        
        rows = await conn.fetch(query, *params)
        return [dict(row) for row in rows]

@app.post("/products/models")
async def create_product_model(product: ProductModel):
    """创建产品模型"""
    product_code = await KnowledgeManager.add_product_model(product)
    return {"message": "Product model created", "product_code": product_code}

@app.get("/products/models/{product_code}")
async def get_product_model(product_code: str):
    """获取产品模型"""
    product = await KnowledgeManager.get_product_model(product_code)
    if not product:
        raise HTTPException(status_code=404, detail="Product model not found")
    return product

@app.post("/knowledge/upload")
async def upload_knowledge_document(
    file: UploadFile = File(...),
    category: str = "general",
    tags: List[str] = []
):
    """上传知识文档"""
    try:
        # 读取文件内容
        content = await file.read()
        
        # 根据文件类型提取文本
        if file.filename.endswith('.pdf'):
            text_content = extract_text_from_pdf(content)
        elif file.filename.endswith('.docx'):
            text_content = extract_text_from_docx(content)
        elif file.filename.endswith('.txt'):
            text_content = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # 创建知识文章
        article = KnowledgeArticle(
            title=file.filename,
            content=text_content,
            category=category,
            tags=tags,
            source="upload"
        )
        
        article_id = await KnowledgeManager.add_knowledge_article(article)
        
        return {
            "message": "Document uploaded and processed",
            "id": article_id,
            "filename": file.filename
        }
        
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def extract_text_from_pdf(content: bytes) -> str:
    """从PDF提取文本"""
    try:
        import io
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return ""

def extract_text_from_docx(content: bytes) -> str:
    """从DOCX提取文本"""
    try:
        import io
        doc = docx.Document(io.BytesIO(content))
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from DOCX: {e}")
        return ""

@app.get("/knowledge/graph/entities")
async def get_knowledge_graph_entities():
    """获取知识图谱实体"""
    # 这里可以集成Neo4j或其他图数据库
    # 暂时返回模拟数据
    entities = [
        {"id": "product_001", "type": "Product", "name": "汽车零部件A"},
        {"id": "process_001", "type": "Process", "name": "CNC加工"},
        {"id": "material_001", "type": "Material", "name": "铝合金"},
        {"id": "equipment_001", "type": "Equipment", "name": "CNC机床001"}
    ]
    return {"entities": entities}

@app.get("/knowledge/graph/relationships")
async def get_knowledge_graph_relationships():
    """获取知识图谱关系"""
    relationships = [
        {"from": "product_001", "to": "process_001", "type": "REQUIRES"},
        {"from": "process_001", "to": "equipment_001", "type": "USES"},
        {"from": "product_001", "to": "material_001", "type": "MADE_OF"}
    ]
    return {"relationships": relationships}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
