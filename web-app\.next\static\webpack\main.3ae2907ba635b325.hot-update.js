"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/router.js":
/*!*************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/router.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _async_to_generator = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\nvar _class_call_check = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_class_call_check.js\");\nvar _create_class = __webpack_require__(/*! @swc/helpers/_/_create_class */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_create_class.js\");\nvar _object_spread = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_object_spread.js\");\nvar _object_spread_props = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_object_spread_props.js\");\nvar _sliced_to_array = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\nvar _ts_generator = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_ts_generator.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createKey: function createKey1() {\n        return createKey;\n    },\n    \"default\": function _default() {\n        return Router;\n    },\n    matchesMiddleware: function matchesMiddleware1() {\n        return matchesMiddleware;\n    }\n});\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nvar _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"(pages-dir-browser)/../node_modules/next/dist/client/route-loader.js\");\nvar _script = __webpack_require__(/*! ../../../client/script */ \"(pages-dir-browser)/../node_modules/next/dist/client/script.js\");\nvar _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"(pages-dir-browser)/../node_modules/next/dist/lib/is-error.js\"));\nvar _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nvar _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nvar _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/mitt.js\"));\nvar _utils = __webpack_require__(/*! ../utils */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nvar _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nvar _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nvar _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/resolve-rewrites.js\"));\nvar _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nvar _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nvar _formaturl = __webpack_require__(/*! ./utils/format-url */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nvar _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"(pages-dir-browser)/../node_modules/next/dist/client/detect-domain-locale.js\");\nvar _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nvar _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"(pages-dir-browser)/../node_modules/next/dist/client/add-locale.js\");\nvar _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"(pages-dir-browser)/../node_modules/next/dist/client/remove-locale.js\");\nvar _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"(pages-dir-browser)/../node_modules/next/dist/client/remove-base-path.js\");\nvar _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"(pages-dir-browser)/../node_modules/next/dist/client/add-base-path.js\");\nvar _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"(pages-dir-browser)/../node_modules/next/dist/client/has-base-path.js\");\nvar _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"(pages-dir-browser)/../node_modules/next/dist/client/resolve-href.js\");\nvar _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"(pages-dir-browser)/../node_modules/next/dist/lib/is-api-route.js\");\nvar _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nvar _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nvar _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nvar _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nvar _isbot = __webpack_require__(/*! ./utils/is-bot */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nvar _omit = __webpack_require__(/*! ./utils/omit */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/omit.js\");\nvar _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nvar _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nvar _constants = __webpack_require__(/*! ../../../lib/constants */ \"(pages-dir-browser)/../node_modules/next/dist/lib/constants.js\");\nfunction buildCancellationError() {\n    return Object.assign(Object.defineProperty(new Error('Route Cancelled'), \"__NEXT_ERROR_CODE\", {\n        value: \"E315\",\n        enumerable: false,\n        configurable: true\n    }), {\n        cancelled: true\n    });\n}\nfunction matchesMiddleware(options) {\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction _matchesMiddleware() {\n    _matchesMiddleware = _async_to_generator._(function(options) {\n        var matchers, _ref, asPathname, cleanedAs, asWithBasePathAndLocale;\n        return _ts_generator._(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    return [\n                        4,\n                        Promise.resolve(options.router.pageLoader.getMiddleware())\n                    ];\n                case 1:\n                    matchers = _state.sent();\n                    if (!matchers) return [\n                        2,\n                        false\n                    ];\n                    _ref = (0, _parsepath.parsePath)(options.asPath), asPathname = _ref.pathname;\n                    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n                    cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n                    asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n                    // Check only path match on client. Matching \"has\" should be done on server\n                    // where we can access more info such as headers, HttpOnly cookie, etc.\n                    return [\n                        2,\n                        matchers.some(function(m) {\n                            return new RegExp(m.regexp).test(asWithBasePathAndLocale);\n                        })\n                    ];\n            }\n        });\n    });\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction stripOrigin(url) {\n    var origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    var _ref = _sliced_to_array._((0, _resolvehref.resolveHref)(router, url, true), 2), resolvedHref = _ref[0], resolvedAs = _ref[1];\n    var origin = (0, _utils.getLocationOrigin)();\n    var hrefWasAbsolute = resolvedHref.startsWith(origin);\n    var asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    var preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    var preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    var cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === '/404' || cleanPathname === '/_error') {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some(function(page) {\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    var nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    var rewriteHeader = response.headers.get('x-nextjs-rewrite');\n    var rewriteTarget = rewriteHeader || response.headers.get('x-nextjs-matched-path');\n    var matchedPath = response.headers.get(_constants.MATCHED_PATH_HEADER);\n    if (matchedPath && !rewriteTarget && !matchedPath.includes('__next_data_catchall') && !matchedPath.includes('/_error') && !matchedPath.includes('/404')) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith('/') || false) {\n            var parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            var pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig: nextConfig,\n                parseData: true\n            });\n            var fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then(function(param) {\n                var _param = _sliced_to_array._(param, 2), pages = _param[0], _param_ = _param[1], rewrites = _param_.__rewrites;\n                var as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    var parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  true ? undefined : 0,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (true) {\n                    var result = (0, _resolverewrites[\"default\"])(as, pages, rewrites, parsedRewriteTarget.query, function(path) {\n                        return resolveDynamicRoute(path, pages);\n                    }, options.router.locales);\n                    if (result.matchedPage) {\n                        parsedRewriteTarget.pathname = result.parsedAs.pathname;\n                        as = parsedRewriteTarget.pathname;\n                        Object.assign(parsedRewriteTarget.query, result.parsedAs.query);\n                    }\n                } else { var resolvedPathname; }\n                var resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    var matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: 'rewrite',\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref: resolvedHref\n                };\n            });\n        }\n        var src = (0, _parsepath.parsePath)(source);\n        var pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)(_object_spread_props._(_object_spread._({}, (0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n            nextConfig: nextConfig,\n            parseData: true\n        })), {\n            defaultLocale: options.router.defaultLocale,\n            buildId: ''\n        }));\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    var redirectTarget = response.headers.get('x-nextjs-redirect');\n    if (redirectTarget) {\n        if (redirectTarget.startsWith('/')) {\n            var src1 = (0, _parsepath.parsePath)(redirectTarget);\n            var pathname1 = (0, _formatnextpathnameinfo.formatNextPathnameInfo)(_object_spread_props._(_object_spread._({}, (0, _getnextpathnameinfo.getNextPathnameInfo)(src1.pathname, {\n                nextConfig: nextConfig,\n                parseData: true\n            })), {\n                defaultLocale: options.router.defaultLocale,\n                buildId: ''\n            }));\n            return Promise.resolve({\n                type: 'redirect-internal',\n                newAs: \"\" + pathname1 + src1.query + src1.hash,\n                newUrl: \"\" + pathname1 + src1.query + src1.hash\n            });\n        }\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: 'next'\n    });\n}\nfunction withMiddlewareEffects(options) {\n    return _withMiddlewareEffects.apply(this, arguments);\n}\nfunction _withMiddlewareEffects() {\n    _withMiddlewareEffects = _async_to_generator._(function(options) {\n        var matches, data, effect;\n        return _ts_generator._(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    return [\n                        4,\n                        matchesMiddleware(options)\n                    ];\n                case 1:\n                    matches = _state.sent();\n                    if (!matches || !options.fetchData) {\n                        return [\n                            2,\n                            null\n                        ];\n                    }\n                    return [\n                        4,\n                        options.fetchData()\n                    ];\n                case 2:\n                    data = _state.sent();\n                    return [\n                        4,\n                        getMiddlewareData(data.dataHref, data.response, options)\n                    ];\n                case 3:\n                    effect = _state.sent();\n                    return [\n                        2,\n                        {\n                            dataHref: data.dataHref,\n                            json: data.json,\n                            response: data.response,\n                            text: data.text,\n                            cacheKey: data.cacheKey,\n                            effect: effect\n                        }\n                    ];\n            }\n        });\n    });\n    return _withMiddlewareEffects.apply(this, arguments);\n}\nvar manualScrollRestoration =  false && 0;\nvar SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND');\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: 'same-origin',\n        method: options.method || 'GET',\n        headers: Object.assign({}, options.headers, {\n            'x-nextjs-data': '1'\n        })\n    }).then(function(response) {\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    var dataHref = param.dataHref, inflightCache = param.inflightCache, isPrefetch = param.isPrefetch, hasMiddleware = param.hasMiddleware, isServerRender = param.isServerRender, parseJSON = param.parseJSON, persistCache = param.persistCache, isBackground = param.isBackground, unstable_skipClientCache = param.unstable_skipClientCache;\n    var _ref = new URL(dataHref, window.location.href), cacheKey = _ref.href;\n    var getData = function(params) {\n        var _params_method;\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: 'prefetch'\n            } : {}, isPrefetch && hasMiddleware ? {\n                'x-middleware-prefetch': '1'\n            } : {},  false ? 0 : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : 'GET'\n        }).then(function(response) {\n            if (response.ok && (params == null ? void 0 : params.method) === 'HEAD') {\n                return {\n                    dataHref: dataHref,\n                    response: response,\n                    text: '',\n                    json: {},\n                    cacheKey: cacheKey\n                };\n            }\n            return response.text().then(function(text) {\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref: dataHref,\n                            response: response,\n                            text: text,\n                            json: {},\n                            cacheKey: cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref: dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response: response,\n                                text: text,\n                                cacheKey: cacheKey\n                            };\n                        }\n                    }\n                    var error = Object.defineProperty(new Error(\"Failed to load static props\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E124\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref: dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response: response,\n                    text: text,\n                    cacheKey: cacheKey\n                };\n            });\n        }).then(function(data) {\n            if (!persistCache || \"development\" !== 'production' || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        })[\"catch\"](function(err) {\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === 'Failed to fetch' || // firefox\n            err.message === 'NetworkError when attempting to fetch resource.' || // safari\n            err.message === 'Load failed') {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then(function(data) {\n            if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n                // only update cache if not marked as no-cache\n                inflightCache[cacheKey] = Promise.resolve(data);\n            }\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: 'HEAD'\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    var url = param.url, router = param.router;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw Object.defineProperty(new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href), \"__NEXT_ERROR_CODE\", {\n            value: \"E282\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    window.location.href = url;\n}\nvar getCancelledHandler = function(param) {\n    var route = param.route, router = param.router;\n    var cancelled = false;\n    var cancel = router.clc = function() {\n        cancelled = true;\n    };\n    var handleCancelled = function() {\n        if (cancelled) {\n            var error = Object.defineProperty(new Error('Abort fetching component for route: \"' + route + '\"'), \"__NEXT_ERROR_CODE\", {\n                value: \"E483\",\n                enumerable: false,\n                configurable: true\n            });\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nvar Router = /*#__PURE__*/ function() {\n    function Router(pathname, query, as, param) {\n        var _this = this;\n        var initialProps = param.initialProps, pageLoader = param.pageLoader, App = param.App, wrapApp = param.wrapApp, Component = param.Component, err = param.err, subscription = param.subscription, isFallback = param.isFallback, locale = param.locale, locales = param.locales, defaultLocale = param.defaultLocale, domainLocales = param.domainLocales, isPreview = param.isPreview;\n        _class_call_check._(this, Router);\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = function(e) {\n            var isFirstPopStateEvent = _this.isFirstPopStateEvent;\n            _this.isFirstPopStateEvent = false;\n            var state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                var _$pathname = _this.pathname, _$query = _this.query;\n                _this.changeState('replaceState', (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(_$pathname),\n                    query: _$query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && _this.locale === state.options.locale && state.as === _this.asPath) {\n                return;\n            }\n            var forcedScroll;\n            var url = state.url, _$as = state.as, options = state.options, key = state.key;\n            if (false) { var v; }\n            _this._key = key;\n            var _$pathname1 = (0, _parserelativeurl.parseRelativeUrl)(url).pathname;\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (_this.isSsr && _$as === (0, _addbasepath.addBasePath)(_this.asPath) && _$pathname1 === (0, _addbasepath.addBasePath)(_this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (_this._bps && !_this._bps(state)) {\n                return;\n            }\n            _this.change('replaceState', url, _$as, Object.assign({}, options, {\n                shallow: options.shallow && _this._shallow,\n                locale: options.locale || _this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        var route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== '/_error') {\n            this.components[route] = {\n                Component: Component,\n                initial: true,\n                props: initialProps,\n                err: err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components['/_app'] = {\n            Component: App,\n            styleSheets: []\n        };\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        var autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || '';\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !true);\n        if (true) {\n            this.locales = locales;\n            this.defaultLocale = defaultLocale;\n            this.domainLocales = domainLocales;\n            this.isLocaleDomain = !!(0, _detectdomainlocale.detectDomainLocale)(domainLocales, self.location.hostname);\n        }\n        this.state = {\n            route: route,\n            pathname: pathname,\n            query: query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  true ? locale : 0,\n            isFallback: isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith('//')) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                var options = {\n                    locale: locale\n                };\n                var asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale: locale,\n                    asPath: asPath\n                }).then(function(matches) {\n                    // if middleware matches we leave resolving to the change function\n                    // as the server needs to resolve for correct priority\n                    ;\n                    options._shouldResolveHref = as !== pathname;\n                    _this.changeState('replaceState', matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query: query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener('popstate', this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n    _create_class._(Router, [\n        {\n            key: \"reload\",\n            value: function reload() {\n                window.location.reload();\n            }\n        },\n        {\n            /**\n   * Go back in history\n   */ key: \"back\",\n            value: function back() {\n                window.history.back();\n            }\n        },\n        {\n            /**\n   * Go forward in history\n   */ key: \"forward\",\n            value: function forward() {\n                window.history.forward();\n            }\n        },\n        {\n            /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ key: \"push\",\n            value: function push(url, as, options) {\n                if (options === void 0) options = {};\n                if (false) {}\n                ;\n                var ref;\n                ref = prepareUrlAs(this, url, as), url = ref.url, as = ref.as, ref;\n                return this.change('pushState', url, as, options);\n            }\n        },\n        {\n            /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ key: \"replace\",\n            value: function replace(url, as, options) {\n                if (options === void 0) options = {};\n                ;\n                var ref;\n                ref = prepareUrlAs(this, url, as), url = ref.url, as = ref.as, ref;\n                return this.change('replaceState', url, as, options);\n            }\n        },\n        {\n            key: \"_bfl\",\n            value: function _bfl(as, resolvedAs, locale, skipNavigate) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var BloomFilter, staticFilterData, dynamicFilterData, ref, err, routerFilterSValue, routerFilterDValue, matchesBflStatic, matchesBflDynamic, pathsToCheck, _iteratorNormalCompletion, _didIteratorError, _iteratorError, _iterator, _step, _step_value, curAs, allowMatchCurrent, asNoSlash, asNoSlashLocale, _this__bfl_s, _this__bfl_s1, _i, _iter, normalizedAS, curAsParts, i, _this__bfl_d, currentPart;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                if (false) {}\n                                if (!(!_this._bfl_s && !_this._bfl_d)) return [\n                                    3,\n                                    5\n                                ];\n                                BloomFilter = (__webpack_require__(/*! ../../lib/bloom-filter */ \"(pages-dir-browser)/../node_modules/next/dist/shared/lib/bloom-filter.js\").BloomFilter);\n                                _state.label = 1;\n                            case 1:\n                                _state.trys.push([\n                                    1,\n                                    3,\n                                    ,\n                                    4\n                                ]);\n                                return [\n                                    4,\n                                    (0, _routeloader.getClientBuildManifest)()\n                                ];\n                            case 2:\n                                ref = _state.sent(), staticFilterData = ref.__routerFilterStatic, dynamicFilterData = ref.__routerFilterDynamic, ref;\n                                return [\n                                    3,\n                                    4\n                                ];\n                            case 3:\n                                err = _state.sent();\n                                // failed to load build manifest hard navigate\n                                // to be safe\n                                console.error(err);\n                                if (skipNavigate) {\n                                    return [\n                                        2,\n                                        true\n                                    ];\n                                }\n                                handleHardNavigation({\n                                    url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || _this.locale, _this.defaultLocale)),\n                                    router: _this\n                                });\n                                return [\n                                    2,\n                                    new Promise(function() {})\n                                ];\n                            case 4:\n                                routerFilterSValue = {\"numItems\":4,\"errorRate\":0.0001,\"numBits\":77,\"numHashes\":14,\"bitArray\":[1,0,0,0,0,0,0,0,0,1,0,1,0,1,0,0,1,0,0,0,1,0,0,1,1,1,1,0,0,0,1,1,1,0,1,0,0,1,0,1,1,0,0,0,1,0,0,1,1,0,1,0,1,0,1,0,0,1,0,0,1,0,0,0,1,1,1,1,0,0,1,0,1,1,0,0,1]};\n                                if (!staticFilterData && routerFilterSValue) {\n                                    staticFilterData = routerFilterSValue ? routerFilterSValue : undefined;\n                                }\n                                routerFilterDValue = {\"numItems\":0,\"errorRate\":0.0001,\"numBits\":0,\"numHashes\":null,\"bitArray\":[]};\n                                if (!dynamicFilterData && routerFilterDValue) {\n                                    dynamicFilterData = routerFilterDValue ? routerFilterDValue : undefined;\n                                }\n                                if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                                    _this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                                    _this._bfl_s[\"import\"](staticFilterData);\n                                }\n                                if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                                    _this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                                    _this._bfl_d[\"import\"](dynamicFilterData);\n                                }\n                                _state.label = 5;\n                            case 5:\n                                matchesBflStatic = false;\n                                matchesBflDynamic = false;\n                                pathsToCheck = [\n                                    {\n                                        as: as\n                                    },\n                                    {\n                                        as: resolvedAs\n                                    }\n                                ];\n                                _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n                                try {\n                                    for(_iterator = pathsToCheck[Symbol.iterator](); !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                                        _step_value = _step.value, curAs = _step_value.as, allowMatchCurrent = _step_value.allowMatchCurrent;\n                                        if (curAs) {\n                                            asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, 'http://n').pathname);\n                                            asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || _this.locale));\n                                            if (allowMatchCurrent || asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(_this.asPath, 'http://n').pathname)) {\n                                                ;\n                                                matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = _this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = _this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                                                for(_i = 0, _iter = [\n                                                    asNoSlash,\n                                                    asNoSlashLocale\n                                                ]; _i < _iter.length; _i++){\n                                                    normalizedAS = _iter[_i];\n                                                    // if any sub-path of as matches a dynamic filter path\n                                                    // it should be hard navigated\n                                                    curAsParts = normalizedAS.split('/');\n                                                    for(i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                                        ;\n                                                        currentPart = curAsParts.slice(0, i).join('/');\n                                                        if (currentPart && ((_this__bfl_d = _this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                                            matchesBflDynamic = true;\n                                                            break;\n                                                        }\n                                                    }\n                                                }\n                                                // if the client router filter is matched then we trigger\n                                                // a hard navigation\n                                                if (matchesBflStatic || matchesBflDynamic) {\n                                                    if (skipNavigate) {\n                                                        return [\n                                                            2,\n                                                            true\n                                                        ];\n                                                    }\n                                                    handleHardNavigation({\n                                                        url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || _this.locale, _this.defaultLocale)),\n                                                        router: _this\n                                                    });\n                                                    return [\n                                                        2,\n                                                        new Promise(function() {})\n                                                    ];\n                                                }\n                                            }\n                                        }\n                                    }\n                                } catch (err) {\n                                    _didIteratorError = true;\n                                    _iteratorError = err;\n                                } finally{\n                                    try {\n                                        if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                                            _iterator[\"return\"]();\n                                        }\n                                    } finally{\n                                        if (_didIteratorError) {\n                                            throw _iteratorError;\n                                        }\n                                    }\n                                }\n                                _state.label = 6;\n                            case 6:\n                                return [\n                                    2,\n                                    false\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"change\",\n            value: function change(method, url, as, options, forcedScroll) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var _this_components_pathname, isQueryUpdating, shouldResolveHref, nextState, readyStateChange, isSsr, prevLocale, parsedAs, localePathResult, didNavigate, _this_locales, detectedDomain, asNoBasePath, _options_shallow, shallow, _options_scroll, scroll, routeProps, cleanedAs, localeChange, err, parsed, pathname, query, pages, rewrites, ref, ref1, err1, resolvedAs, route, parsedAsPathname, isMiddlewareRewrite, isMiddlewareMatch, _tmp, rewritesResult, routeMatch, parsedAs1, asPathname, routeRegex, shouldInterpolate, interpolatedAs, missingParams, isErrorRoute, _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props, routeInfo, cleanedParsedPathname, prefixedAs, rewriteAs, localeResult, routeRegex1, curRouteMatch, component, scripts, destination, parsedHref, _prepareUrlAs, newUrl, newAs, notFoundRoute, _, _routeInfo_route, isValidShallowRoute, _options_scroll1, shouldScroll, resetScroll, upcomingScrollState, upcomingRouterState, _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1, err2, canSkipUpdating, e, hashRegex, err3;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                if (!(0, _islocalurl.isLocalURL)(url)) {\n                                    handleHardNavigation({\n                                        url: url,\n                                        router: _this\n                                    });\n                                    return [\n                                        2,\n                                        false\n                                    ];\n                                }\n                                // WARNING: `_h` is an internal option for handing Next.js client-side\n                                // hydration. Your app should _never_ use this property. It may change at\n                                // any time without notice.\n                                isQueryUpdating = options._h === 1;\n                                if (!(!isQueryUpdating && !options.shallow)) return [\n                                    3,\n                                    2\n                                ];\n                                return [\n                                    4,\n                                    _this._bfl(as, undefined, options.locale)\n                                ];\n                            case 1:\n                                _state.sent();\n                                _state.label = 2;\n                            case 2:\n                                shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n                                nextState = _object_spread._({}, _this.state);\n                                // for static pages with query params in the URL we delay\n                                // marking the router ready until after the query is updated\n                                // or a navigation has occurred\n                                readyStateChange = _this.isReady !== true;\n                                _this.isReady = true;\n                                isSsr = _this.isSsr;\n                                if (!isQueryUpdating) {\n                                    _this.isSsr = false;\n                                }\n                                // if a route transition is already in progress before\n                                // the query updating is triggered ignore query updating\n                                if (isQueryUpdating && _this.clc) {\n                                    return [\n                                        2,\n                                        false\n                                    ];\n                                }\n                                prevLocale = nextState.locale;\n                                if (true) {\n                                    nextState.locale = options.locale === false ? _this.defaultLocale : options.locale || nextState.locale;\n                                    if (typeof options.locale === 'undefined') {\n                                        options.locale = nextState.locale;\n                                    }\n                                    parsedAs = (0, _parserelativeurl.parseRelativeUrl)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as);\n                                    localePathResult = (0, _normalizelocalepath.normalizeLocalePath)(parsedAs.pathname, _this.locales);\n                                    if (localePathResult.detectedLocale) {\n                                        nextState.locale = localePathResult.detectedLocale;\n                                        parsedAs.pathname = (0, _addbasepath.addBasePath)(parsedAs.pathname);\n                                        as = (0, _formaturl.formatWithValidation)(parsedAs);\n                                        url = (0, _addbasepath.addBasePath)((0, _normalizelocalepath.normalizeLocalePath)((0, _hasbasepath.hasBasePath)(url) ? (0, _removebasepath.removeBasePath)(url) : url, _this.locales).pathname);\n                                    }\n                                    didNavigate = false;\n                                    // we need to wrap this in the env check again since regenerator runtime\n                                    // moves this on its own due to the return\n                                    if (true) {\n                                        ;\n                                        // if the locale isn't configured hard navigate to show 404 page\n                                        if (!((_this_locales = _this.locales) == null ? void 0 : _this_locales.includes(nextState.locale))) {\n                                            parsedAs.pathname = (0, _addlocale.addLocale)(parsedAs.pathname, nextState.locale);\n                                            handleHardNavigation({\n                                                url: (0, _formaturl.formatWithValidation)(parsedAs),\n                                                router: _this\n                                            });\n                                            // this was previously a return but was removed in favor\n                                            // of better dead code elimination with regenerator runtime\n                                            didNavigate = true;\n                                        }\n                                    }\n                                    detectedDomain = (0, _detectdomainlocale.detectDomainLocale)(_this.domainLocales, undefined, nextState.locale);\n                                    // we need to wrap this in the env check again since regenerator runtime\n                                    // moves this on its own due to the return\n                                    if (true) {\n                                        // if we are navigating to a domain locale ensure we redirect to the\n                                        // correct domain\n                                        if (!didNavigate && detectedDomain && _this.isLocaleDomain && self.location.hostname !== detectedDomain.domain) {\n                                            asNoBasePath = (0, _removebasepath.removeBasePath)(as);\n                                            handleHardNavigation({\n                                                url: \"http\" + (detectedDomain.http ? '' : 's') + \"://\" + detectedDomain.domain + (0, _addbasepath.addBasePath)(\"\" + (nextState.locale === detectedDomain.defaultLocale ? '' : \"/\" + nextState.locale) + (asNoBasePath === '/' ? '' : asNoBasePath) || '/'),\n                                                router: _this\n                                            });\n                                            // this was previously a return but was removed in favor\n                                            // of better dead code elimination with regenerator runtime\n                                            didNavigate = true;\n                                        }\n                                    }\n                                    if (didNavigate) {\n                                        return [\n                                            2,\n                                            new Promise(function() {})\n                                        ];\n                                    }\n                                }\n                                // marking route changes as a navigation start entry\n                                if (_utils.ST) {\n                                    performance.mark('routeChange');\n                                }\n                                _options_shallow = options.shallow, shallow = _options_shallow === void 0 ? false : _options_shallow, _options_scroll = options.scroll, scroll = _options_scroll === void 0 ? true : _options_scroll;\n                                routeProps = {\n                                    shallow: shallow\n                                };\n                                if (_this._inFlightRoute && _this.clc) {\n                                    if (!isSsr) {\n                                        Router.events.emit('routeChangeError', buildCancellationError(), _this._inFlightRoute, routeProps);\n                                    }\n                                    _this.clc();\n                                    _this.clc = null;\n                                }\n                                as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, _this.defaultLocale));\n                                cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n                                _this._inFlightRoute = as;\n                                localeChange = prevLocale !== nextState.locale;\n                                if (!(!isQueryUpdating && _this.onlyAHashChange(cleanedAs) && !localeChange)) return [\n                                    3,\n                                    7\n                                ];\n                                nextState.asPath = cleanedAs;\n                                Router.events.emit('hashChangeStart', as, routeProps);\n                                // TODO: do we need the resolved href when only a hash change?\n                                _this.changeState(method, url, as, _object_spread_props._(_object_spread._({}, options), {\n                                    scroll: false\n                                }));\n                                if (scroll) {\n                                    _this.scrollToHash(cleanedAs);\n                                }\n                                _state.label = 3;\n                            case 3:\n                                _state.trys.push([\n                                    3,\n                                    5,\n                                    ,\n                                    6\n                                ]);\n                                return [\n                                    4,\n                                    _this.set(nextState, _this.components[nextState.route], null)\n                                ];\n                            case 4:\n                                _state.sent();\n                                return [\n                                    3,\n                                    6\n                                ];\n                            case 5:\n                                err = _state.sent();\n                                if ((0, _iserror[\"default\"])(err) && err.cancelled) {\n                                    Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n                                }\n                                throw err;\n                            case 6:\n                                Router.events.emit('hashChangeComplete', as, routeProps);\n                                return [\n                                    2,\n                                    true\n                                ];\n                            case 7:\n                                parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n                                pathname = parsed.pathname, query = parsed.query;\n                                _state.label = 8;\n                            case 8:\n                                _state.trys.push([\n                                    8,\n                                    10,\n                                    ,\n                                    11\n                                ]);\n                                return [\n                                    4,\n                                    Promise.all([\n                                        _this.pageLoader.getPageList(),\n                                        (0, _routeloader.getClientBuildManifest)(),\n                                        _this.pageLoader.getMiddleware()\n                                    ])\n                                ];\n                            case 9:\n                                ref = _sliced_to_array._.apply(void 0, [\n                                    _state.sent(),\n                                    2\n                                ]), pages = ref[0], ref1 = ref[1], rewrites = ref1.__rewrites, ref1, ref;\n                                return [\n                                    3,\n                                    11\n                                ];\n                            case 10:\n                                err1 = _state.sent();\n                                // If we fail to resolve the page list or client-build manifest, we must\n                                // do a server-side transition:\n                                handleHardNavigation({\n                                    url: as,\n                                    router: _this\n                                });\n                                return [\n                                    2,\n                                    false\n                                ];\n                            case 11:\n                                // If asked to change the current URL we should reload the current page\n                                // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n                                // We also need to set the method = replaceState always\n                                // as this should not go into the history (That's how browsers work)\n                                // We should compare the new asPath to the current asPath, not the url\n                                if (!_this.urlIsNew(cleanedAs) && !localeChange) {\n                                    method = 'replaceState';\n                                }\n                                // we need to resolve the as value using rewrites for dynamic SSG\n                                // pages to allow building the data URL correctly\n                                resolvedAs = as;\n                                // url and as should always be prefixed with basePath by this\n                                // point by either next/link or router.push/replace so strip the\n                                // basePath from the pathname to match the pages dir 1-to-1\n                                pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n                                route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n                                parsedAsPathname = as.startsWith('/') && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n                                // if we detected the path as app route during prefetching\n                                // trigger hard navigation\n                                if ((_this_components_pathname = _this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n                                    handleHardNavigation({\n                                        url: as,\n                                        router: _this\n                                    });\n                                    return [\n                                        2,\n                                        new Promise(function() {})\n                                    ];\n                                }\n                                isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n                                _tmp = !options.shallow;\n                                if (!_tmp) return [\n                                    3,\n                                    13\n                                ];\n                                return [\n                                    4,\n                                    matchesMiddleware({\n                                        asPath: as,\n                                        locale: nextState.locale,\n                                        router: _this\n                                    })\n                                ];\n                            case 12:\n                                _tmp = _state.sent();\n                                _state.label = 13;\n                            case 13:\n                                isMiddlewareMatch = _tmp;\n                                if (isQueryUpdating && isMiddlewareMatch) {\n                                    shouldResolveHref = false;\n                                }\n                                if (shouldResolveHref && pathname !== '/_error') {\n                                    ;\n                                    options._shouldResolveHref = true;\n                                    if ( true && as.startsWith('/')) {\n                                        rewritesResult = (0, _resolverewrites[\"default\"])((0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, nextState.locale), true), pages, rewrites, query, function(p) {\n                                            return resolveDynamicRoute(p, pages);\n                                        }, _this.locales);\n                                        if (rewritesResult.externalDest) {\n                                            handleHardNavigation({\n                                                url: as,\n                                                router: _this\n                                            });\n                                            return [\n                                                2,\n                                                true\n                                            ];\n                                        }\n                                        if (!isMiddlewareMatch) {\n                                            resolvedAs = rewritesResult.asPath;\n                                        }\n                                        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n                                            // if this directly matches a page we need to update the href to\n                                            // allow the correct page chunk to be loaded\n                                            pathname = rewritesResult.resolvedHref;\n                                            parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                                            if (!isMiddlewareMatch) {\n                                                url = (0, _formaturl.formatWithValidation)(parsed);\n                                            }\n                                        }\n                                    } else {\n                                        parsed.pathname = resolveDynamicRoute(pathname, pages);\n                                        if (parsed.pathname !== pathname) {\n                                            pathname = parsed.pathname;\n                                            parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                                            if (!isMiddlewareMatch) {\n                                                url = (0, _formaturl.formatWithValidation)(parsed);\n                                            }\n                                        }\n                                    }\n                                }\n                                if (!(0, _islocalurl.isLocalURL)(as)) {\n                                    if (true) {\n                                        throw Object.defineProperty(new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\"), \"__NEXT_ERROR_CODE\", {\n                                            value: \"E380\",\n                                            enumerable: false,\n                                            configurable: true\n                                        });\n                                    }\n                                    handleHardNavigation({\n                                        url: as,\n                                        router: _this\n                                    });\n                                    return [\n                                        2,\n                                        false\n                                    ];\n                                }\n                                resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n                                route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n                                routeMatch = false;\n                                if ((0, _isdynamic.isDynamicRoute)(route)) {\n                                    parsedAs1 = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n                                    asPathname = parsedAs1.pathname;\n                                    routeRegex = (0, _routeregex.getRouteRegex)(route);\n                                    routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n                                    shouldInterpolate = route === asPathname;\n                                    interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n                                    if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                                        missingParams = Object.keys(routeRegex.groups).filter(function(param) {\n                                            return !query[param] && !routeRegex.groups[param].optional;\n                                        });\n                                        if (missingParams.length > 0 && !isMiddlewareMatch) {\n                                            if (true) {\n                                                console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(', ') + \" in the `href`'s `query`\"));\n                                            }\n                                            throw Object.defineProperty(new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(', ') + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? 'href-interpolation-failed' : 'incompatible-href-as'))), \"__NEXT_ERROR_CODE\", {\n                                                value: \"E344\",\n                                                enumerable: false,\n                                                configurable: true\n                                            });\n                                        }\n                                    } else if (shouldInterpolate) {\n                                        as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs1, {\n                                            pathname: interpolatedAs.result,\n                                            query: (0, _omit.omit)(query, interpolatedAs.params)\n                                        }));\n                                    } else {\n                                        // Merge params into `query`, overwriting any specified in search\n                                        Object.assign(query, routeMatch);\n                                    }\n                                }\n                                if (!isQueryUpdating) {\n                                    Router.events.emit('routeChangeStart', as, routeProps);\n                                }\n                                isErrorRoute = _this.pathname === '/404' || _this.pathname === '/_error';\n                                _state.label = 14;\n                            case 14:\n                                _state.trys.push([\n                                    14,\n                                    35,\n                                    ,\n                                    36\n                                ]);\n                                return [\n                                    4,\n                                    _this.getRouteInfo({\n                                        route: route,\n                                        pathname: pathname,\n                                        query: query,\n                                        as: as,\n                                        resolvedAs: resolvedAs,\n                                        routeProps: routeProps,\n                                        locale: nextState.locale,\n                                        isPreview: nextState.isPreview,\n                                        hasMiddleware: isMiddlewareMatch,\n                                        unstable_skipClientCache: options.unstable_skipClientCache,\n                                        isQueryUpdating: isQueryUpdating && !_this.isFallback,\n                                        isMiddlewareRewrite: isMiddlewareRewrite\n                                    })\n                                ];\n                            case 15:\n                                routeInfo = _state.sent();\n                                if (!(!isQueryUpdating && !options.shallow)) return [\n                                    3,\n                                    17\n                                ];\n                                return [\n                                    4,\n                                    _this._bfl(as, 'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale)\n                                ];\n                            case 16:\n                                _state.sent();\n                                _state.label = 17;\n                            case 17:\n                                if ('route' in routeInfo && isMiddlewareMatch) {\n                                    pathname = routeInfo.route || route;\n                                    route = pathname;\n                                    if (!routeProps.shallow) {\n                                        query = Object.assign({}, routeInfo.query || {}, query);\n                                    }\n                                    cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                                    if (routeMatch && pathname !== cleanedParsedPathname) {\n                                        Object.keys(routeMatch).forEach(function(key) {\n                                            if (routeMatch && query[key] === routeMatch[key]) {\n                                                delete query[key];\n                                            }\n                                        });\n                                    }\n                                    if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                                        prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                                        rewriteAs = prefixedAs;\n                                        if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                                            rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                                        }\n                                        if (true) {\n                                            localeResult = (0, _normalizelocalepath.normalizeLocalePath)(rewriteAs, _this.locales);\n                                            nextState.locale = localeResult.detectedLocale || nextState.locale;\n                                            rewriteAs = localeResult.pathname;\n                                        }\n                                        routeRegex1 = (0, _routeregex.getRouteRegex)(pathname);\n                                        curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex1)(new URL(rewriteAs, location.href).pathname);\n                                        if (curRouteMatch) {\n                                            Object.assign(query, curRouteMatch);\n                                        }\n                                    }\n                                }\n                                // If the routeInfo brings a redirect we simply apply it.\n                                if ('type' in routeInfo) {\n                                    if (routeInfo.type === 'redirect-internal') {\n                                        return [\n                                            2,\n                                            _this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n                                        ];\n                                    } else {\n                                        handleHardNavigation({\n                                            url: routeInfo.destination,\n                                            router: _this\n                                        });\n                                        return [\n                                            2,\n                                            new Promise(function() {})\n                                        ];\n                                    }\n                                }\n                                component = routeInfo.Component;\n                                if (component && component.unstable_scriptLoader) {\n                                    scripts = [].concat(component.unstable_scriptLoader());\n                                    scripts.forEach(function(script) {\n                                        (0, _script.handleClientScriptLoad)(script.props);\n                                    });\n                                }\n                                if (!((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props)) return [\n                                    3,\n                                    23\n                                ];\n                                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                                    // Use the destination from redirect without adding locale\n                                    options.locale = false;\n                                    destination = routeInfo.props.pageProps.__N_REDIRECT;\n                                    // check if destination is internal (resolves to a page) and attempt\n                                    // client-navigation if it is falling back to hard navigation if\n                                    // it's not\n                                    if (destination.startsWith('/') && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                                        parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                                        _prepareUrlAs = prepareUrlAs(_this, destination, destination), newUrl = _prepareUrlAs.url, newAs = _prepareUrlAs.as;\n                                        return [\n                                            2,\n                                            _this.change(method, newUrl, newAs, options)\n                                        ];\n                                    }\n                                    handleHardNavigation({\n                                        url: destination,\n                                        router: _this\n                                    });\n                                    return [\n                                        2,\n                                        new Promise(function() {})\n                                    ];\n                                }\n                                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                                if (!(routeInfo.props.notFound === SSG_DATA_NOT_FOUND)) return [\n                                    3,\n                                    23\n                                ];\n                                _state.label = 18;\n                            case 18:\n                                _state.trys.push([\n                                    18,\n                                    20,\n                                    ,\n                                    21\n                                ]);\n                                return [\n                                    4,\n                                    _this.fetchComponent('/404')\n                                ];\n                            case 19:\n                                _state.sent();\n                                notFoundRoute = '/404';\n                                return [\n                                    3,\n                                    21\n                                ];\n                            case 20:\n                                _ = _state.sent();\n                                notFoundRoute = '/_error';\n                                return [\n                                    3,\n                                    21\n                                ];\n                            case 21:\n                                return [\n                                    4,\n                                    _this.getRouteInfo({\n                                        route: notFoundRoute,\n                                        pathname: notFoundRoute,\n                                        query: query,\n                                        as: as,\n                                        resolvedAs: resolvedAs,\n                                        routeProps: {\n                                            shallow: false\n                                        },\n                                        locale: nextState.locale,\n                                        isPreview: nextState.isPreview,\n                                        isNotFound: true\n                                    })\n                                ];\n                            case 22:\n                                routeInfo = _state.sent();\n                                if ('type' in routeInfo) {\n                                    throw Object.defineProperty(new Error(\"Unexpected middleware effect on /404\"), \"__NEXT_ERROR_CODE\", {\n                                        value: \"E158\",\n                                        enumerable: false,\n                                        configurable: true\n                                    });\n                                }\n                                _state.label = 23;\n                            case 23:\n                                if (isQueryUpdating && _this.pathname === '/_error' && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                                    // ensure statusCode is still correct for static 500 page\n                                    // when updating query information\n                                    routeInfo.props.pageProps.statusCode = 500;\n                                }\n                                // shallow routing is only allowed for same page URL changes.\n                                isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n                                shouldScroll = (_options_scroll1 = options.scroll) != null ? _options_scroll1 : !isQueryUpdating && !isValidShallowRoute;\n                                resetScroll = shouldScroll ? {\n                                    x: 0,\n                                    y: 0\n                                } : null;\n                                upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n                                // the new state that the router gonna set\n                                upcomingRouterState = _object_spread_props._(_object_spread._({}, nextState), {\n                                    route: route,\n                                    pathname: pathname,\n                                    query: query,\n                                    asPath: cleanedAs,\n                                    isFallback: false\n                                });\n                                if (!(isQueryUpdating && isErrorRoute)) return [\n                                    3,\n                                    29\n                                ];\n                                return [\n                                    4,\n                                    _this.getRouteInfo({\n                                        route: _this.pathname,\n                                        pathname: _this.pathname,\n                                        query: query,\n                                        as: as,\n                                        resolvedAs: resolvedAs,\n                                        routeProps: {\n                                            shallow: false\n                                        },\n                                        locale: nextState.locale,\n                                        isPreview: nextState.isPreview,\n                                        isQueryUpdating: isQueryUpdating && !_this.isFallback\n                                    })\n                                ];\n                            case 24:\n                                routeInfo = _state.sent();\n                                if ('type' in routeInfo) {\n                                    throw Object.defineProperty(new Error(\"Unexpected middleware effect on \" + _this.pathname), \"__NEXT_ERROR_CODE\", {\n                                        value: \"E225\",\n                                        enumerable: false,\n                                        configurable: true\n                                    });\n                                }\n                                if (_this.pathname === '/_error' && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                                    // ensure statusCode is still correct for static 500 page\n                                    // when updating query information\n                                    routeInfo.props.pageProps.statusCode = 500;\n                                }\n                                _state.label = 25;\n                            case 25:\n                                _state.trys.push([\n                                    25,\n                                    27,\n                                    ,\n                                    28\n                                ]);\n                                return [\n                                    4,\n                                    _this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n                                ];\n                            case 26:\n                                _state.sent();\n                                return [\n                                    3,\n                                    28\n                                ];\n                            case 27:\n                                err2 = _state.sent();\n                                if ((0, _iserror[\"default\"])(err2) && err2.cancelled) {\n                                    Router.events.emit('routeChangeError', err2, cleanedAs, routeProps);\n                                }\n                                throw err2;\n                            case 28:\n                                return [\n                                    2,\n                                    true\n                                ];\n                            case 29:\n                                Router.events.emit('beforeHistoryChange', as, routeProps);\n                                _this.changeState(method, url, as, options);\n                                // for query updates we can skip it if the state is unchanged and we don't\n                                // need to scroll\n                                // https://github.com/vercel/next.js/issues/37139\n                                canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, _this.state);\n                                if (!!canSkipUpdating) return [\n                                    3,\n                                    34\n                                ];\n                                _state.label = 30;\n                            case 30:\n                                _state.trys.push([\n                                    30,\n                                    32,\n                                    ,\n                                    33\n                                ]);\n                                return [\n                                    4,\n                                    _this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n                                ];\n                            case 31:\n                                _state.sent();\n                                return [\n                                    3,\n                                    33\n                                ];\n                            case 32:\n                                e = _state.sent();\n                                if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                                else throw e;\n                                return [\n                                    3,\n                                    33\n                                ];\n                            case 33:\n                                if (routeInfo.error) {\n                                    if (!isQueryUpdating) {\n                                        Router.events.emit('routeChangeError', routeInfo.error, cleanedAs, routeProps);\n                                    }\n                                    throw routeInfo.error;\n                                }\n                                if (true) {\n                                    if (nextState.locale) {\n                                        document.documentElement.lang = nextState.locale;\n                                    }\n                                }\n                                if (!isQueryUpdating) {\n                                    Router.events.emit('routeChangeComplete', as, routeProps);\n                                }\n                                // A hash mark # is the optional last part of a URL\n                                hashRegex = /#.+$/;\n                                if (shouldScroll && hashRegex.test(as)) {\n                                    _this.scrollToHash(as);\n                                }\n                                _state.label = 34;\n                            case 34:\n                                return [\n                                    2,\n                                    true\n                                ];\n                            case 35:\n                                err3 = _state.sent();\n                                if ((0, _iserror[\"default\"])(err3) && err3.cancelled) {\n                                    return [\n                                        2,\n                                        false\n                                    ];\n                                }\n                                throw err3;\n                            case 36:\n                                return [\n                                    2\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"changeState\",\n            value: function changeState(method, url, as, options) {\n                if (options === void 0) options = {};\n                if (true) {\n                    if (typeof window.history === 'undefined') {\n                        console.error(\"Warning: window.history is not available.\");\n                        return;\n                    }\n                    if (typeof window.history[method] === 'undefined') {\n                        console.error(\"Warning: window.history.\" + method + \" is not available\");\n                        return;\n                    }\n                }\n                if (method !== 'pushState' || (0, _utils.getURL)() !== as) {\n                    this._shallow = options.shallow;\n                    window.history[method]({\n                        url: url,\n                        as: as,\n                        options: options,\n                        __N: true,\n                        key: this._key = method !== 'pushState' ? this._key : createKey()\n                    }, // Passing the empty string here should be safe against future changes to the method.\n                    // https://developer.mozilla.org/docs/Web/API/History/replaceState\n                    '', as);\n                }\n            }\n        },\n        {\n            key: \"handleRouteInfoError\",\n            value: function handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var props, _ref, Component, styleSheets, routeInfo, gipErr, routeInfoErr;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                if (err.cancelled) {\n                                    // bubble up cancellation errors\n                                    throw err;\n                                }\n                                if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n                                    Router.events.emit('routeChangeError', err, as, routeProps);\n                                    // If we can't load the page it could be one of following reasons\n                                    //  1. Page doesn't exists\n                                    //  2. Page does exist in a different zone\n                                    //  3. Internal error while loading the page\n                                    // So, doing a hard reload is the proper way to deal with this.\n                                    handleHardNavigation({\n                                        url: as,\n                                        router: _this\n                                    });\n                                    // Changing the URL doesn't block executing the current code path.\n                                    // So let's throw a cancellation error stop the routing logic.\n                                    throw buildCancellationError();\n                                }\n                                console.error(err);\n                                _state.label = 1;\n                            case 1:\n                                _state.trys.push([\n                                    1,\n                                    7,\n                                    ,\n                                    8\n                                ]);\n                                return [\n                                    4,\n                                    _this.fetchComponent('/_error')\n                                ];\n                            case 2:\n                                _ref = _state.sent(), Component = _ref.page, styleSheets = _ref.styleSheets;\n                                routeInfo = {\n                                    props: props,\n                                    Component: Component,\n                                    styleSheets: styleSheets,\n                                    err: err,\n                                    error: err\n                                };\n                                if (!!routeInfo.props) return [\n                                    3,\n                                    6\n                                ];\n                                _state.label = 3;\n                            case 3:\n                                _state.trys.push([\n                                    3,\n                                    5,\n                                    ,\n                                    6\n                                ]);\n                                return [\n                                    4,\n                                    _this.getInitialProps(Component, {\n                                        err: err,\n                                        pathname: pathname,\n                                        query: query\n                                    })\n                                ];\n                            case 4:\n                                routeInfo.props = _state.sent();\n                                return [\n                                    3,\n                                    6\n                                ];\n                            case 5:\n                                gipErr = _state.sent();\n                                console.error('Error in error page `getInitialProps`: ', gipErr);\n                                routeInfo.props = {};\n                                return [\n                                    3,\n                                    6\n                                ];\n                            case 6:\n                                return [\n                                    2,\n                                    routeInfo\n                                ];\n                            case 7:\n                                routeInfoErr = _state.sent();\n                                return [\n                                    2,\n                                    _this.handleRouteInfoError((0, _iserror[\"default\"])(routeInfoErr) ? routeInfoErr : Object.defineProperty(new Error(routeInfoErr + ''), \"__NEXT_ERROR_CODE\", {\n                                        value: \"E394\",\n                                        enumerable: false,\n                                        configurable: true\n                                    }), pathname, query, as, routeProps, true)\n                                ];\n                            case 8:\n                                return [\n                                    2\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"getRouteInfo\",\n            value: function getRouteInfo(param) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound, route, _data_effect, _data_effect1, _data_effect2, _data_response, existingInfo, handleCancelled, cachedRouteInfo, isBackground, fetchNextDataParams, data, _tmp, resolvedRoute, pages, routeInfo, _tmp1, isValidElementType, wasBailedPrefetch, shouldFetchData, _ref, props, cacheKey, err;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                requestedRoute = param.route, pathname = param.pathname, query = param.query, as = param.as, resolvedAs = param.resolvedAs, routeProps = param.routeProps, locale = param.locale, hasMiddleware = param.hasMiddleware, isPreview = param.isPreview, unstable_skipClientCache = param.unstable_skipClientCache, isQueryUpdating = param.isQueryUpdating, isMiddlewareRewrite = param.isMiddlewareRewrite, isNotFound = param.isNotFound;\n                                /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ route = requestedRoute;\n                                _state.label = 1;\n                            case 1:\n                                _state.trys.push([\n                                    1,\n                                    10,\n                                    ,\n                                    11\n                                ]);\n                                existingInfo = _this.components[route];\n                                if (routeProps.shallow && existingInfo && _this.route === route) {\n                                    return [\n                                        2,\n                                        existingInfo\n                                    ];\n                                }\n                                handleCancelled = getCancelledHandler({\n                                    route: route,\n                                    router: _this\n                                });\n                                if (hasMiddleware) {\n                                    existingInfo = undefined;\n                                }\n                                cachedRouteInfo = existingInfo && !('initial' in existingInfo) && \"development\" !== 'development' ? 0 : undefined;\n                                isBackground = isQueryUpdating;\n                                fetchNextDataParams = {\n                                    dataHref: _this.pageLoader.getDataHref({\n                                        href: (0, _formaturl.formatWithValidation)({\n                                            pathname: pathname,\n                                            query: query\n                                        }),\n                                        skipInterpolation: true,\n                                        asPath: isNotFound ? '/404' : resolvedAs,\n                                        locale: locale\n                                    }),\n                                    hasMiddleware: true,\n                                    isServerRender: _this.isSsr,\n                                    parseJSON: true,\n                                    inflightCache: isBackground ? _this.sbc : _this.sdc,\n                                    persistCache: !isPreview,\n                                    isPrefetch: false,\n                                    unstable_skipClientCache: unstable_skipClientCache,\n                                    isBackground: isBackground\n                                };\n                                if (!(isQueryUpdating && !isMiddlewareRewrite)) return [\n                                    3,\n                                    2\n                                ];\n                                _tmp = null;\n                                return [\n                                    3,\n                                    4\n                                ];\n                            case 2:\n                                return [\n                                    4,\n                                    withMiddlewareEffects({\n                                        fetchData: function() {\n                                            return fetchNextData(fetchNextDataParams);\n                                        },\n                                        asPath: isNotFound ? '/404' : resolvedAs,\n                                        locale: locale,\n                                        router: _this\n                                    })[\"catch\"](function(err) {\n                                        // we don't hard error during query updating\n                                        // as it's un-necessary and doesn't need to be fatal\n                                        // unless it is a fallback route and the props can't\n                                        // be loaded\n                                        if (isQueryUpdating) {\n                                            return null;\n                                        }\n                                        throw err;\n                                    })\n                                ];\n                            case 3:\n                                _tmp = _state.sent();\n                                _state.label = 4;\n                            case 4:\n                                data = _tmp;\n                                // when rendering error routes we don't apply middleware\n                                // effects\n                                if (data && (pathname === '/_error' || pathname === '/404')) {\n                                    data.effect = undefined;\n                                }\n                                if (isQueryUpdating) {\n                                    if (!data) {\n                                        data = {\n                                            json: self.__NEXT_DATA__.props\n                                        };\n                                    } else {\n                                        data.json = self.__NEXT_DATA__.props;\n                                    }\n                                }\n                                handleCancelled();\n                                if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === 'redirect-internal' || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === 'redirect-external') {\n                                    return [\n                                        2,\n                                        data.effect\n                                    ];\n                                }\n                                if (!((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === 'rewrite')) return [\n                                    3,\n                                    6\n                                ];\n                                resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                                return [\n                                    4,\n                                    _this.pageLoader.getPageList()\n                                ];\n                            case 5:\n                                pages = _state.sent();\n                                // during query updating the page must match although during\n                                // client-transition a redirect that doesn't match a page\n                                // can be returned and this should trigger a hard navigation\n                                // which is valid for incremental migration\n                                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                                    route = resolvedRoute;\n                                    pathname = data.effect.resolvedHref;\n                                    query = _object_spread._({}, query, data.effect.parsedAs.query);\n                                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, _this.locales).pathname);\n                                    // Check again the cache with the new destination.\n                                    existingInfo = _this.components[route];\n                                    if (routeProps.shallow && existingInfo && _this.route === route && !hasMiddleware) {\n                                        // If we have a match with the current route due to rewrite,\n                                        // we can copy the existing information to the rewritten one.\n                                        // Then, we return the information along with the matched route.\n                                        return [\n                                            2,\n                                            _object_spread_props._(_object_spread._({}, existingInfo), {\n                                                route: route\n                                            })\n                                        ];\n                                    }\n                                }\n                                _state.label = 6;\n                            case 6:\n                                if ((0, _isapiroute.isAPIRoute)(route)) {\n                                    handleHardNavigation({\n                                        url: as,\n                                        router: _this\n                                    });\n                                    return [\n                                        2,\n                                        new Promise(function() {})\n                                    ];\n                                }\n                                _tmp1 = cachedRouteInfo;\n                                if (_tmp1) return [\n                                    3,\n                                    8\n                                ];\n                                return [\n                                    4,\n                                    _this.fetchComponent(route).then(function(res) {\n                                        return {\n                                            Component: res.page,\n                                            styleSheets: res.styleSheets,\n                                            __N_SSG: res.mod.__N_SSG,\n                                            __N_SSP: res.mod.__N_SSP\n                                        };\n                                    })\n                                ];\n                            case 7:\n                                _tmp1 = _state.sent();\n                                _state.label = 8;\n                            case 8:\n                                routeInfo = _tmp1;\n                                if (true) {\n                                    isValidElementType = (__webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/../node_modules/next/dist/compiled/react-is/index.js\").isValidElementType);\n                                    if (!isValidElementType(routeInfo.Component)) {\n                                        throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + pathname + '\"'), \"__NEXT_ERROR_CODE\", {\n                                            value: \"E286\",\n                                            enumerable: false,\n                                            configurable: true\n                                        });\n                                    }\n                                }\n                                wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get('x-middleware-skip');\n                                shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n                                // For non-SSG prefetches that bailed before sending data\n                                // we clear the cache to fetch full response\n                                if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                                    delete _this.sdc[data.dataHref];\n                                }\n                                return [\n                                    4,\n                                    _this._getData(/*#__PURE__*/ _async_to_generator._(function() {\n                                        var dataHref, fetched, _tmp;\n                                        return _ts_generator._(this, function(_state) {\n                                            switch(_state.label){\n                                                case 0:\n                                                    if (!shouldFetchData) return [\n                                                        3,\n                                                        2\n                                                    ];\n                                                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                                                        return [\n                                                            2,\n                                                            {\n                                                                cacheKey: data.cacheKey,\n                                                                props: data.json\n                                                            }\n                                                        ];\n                                                    }\n                                                    dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : _this.pageLoader.getDataHref({\n                                                        href: (0, _formaturl.formatWithValidation)({\n                                                            pathname: pathname,\n                                                            query: query\n                                                        }),\n                                                        asPath: resolvedAs,\n                                                        locale: locale\n                                                    });\n                                                    return [\n                                                        4,\n                                                        fetchNextData({\n                                                            dataHref: dataHref,\n                                                            isServerRender: _this.isSsr,\n                                                            parseJSON: true,\n                                                            inflightCache: wasBailedPrefetch ? {} : _this.sdc,\n                                                            persistCache: !isPreview,\n                                                            isPrefetch: false,\n                                                            unstable_skipClientCache: unstable_skipClientCache\n                                                        })\n                                                    ];\n                                                case 1:\n                                                    fetched = _state.sent();\n                                                    return [\n                                                        2,\n                                                        {\n                                                            cacheKey: fetched.cacheKey,\n                                                            props: fetched.json || {}\n                                                        }\n                                                    ];\n                                                case 2:\n                                                    _tmp = {\n                                                        headers: {}\n                                                    };\n                                                    return [\n                                                        4,\n                                                        _this.getInitialProps(routeInfo.Component, {\n                                                            pathname: pathname,\n                                                            query: query,\n                                                            asPath: as,\n                                                            locale: locale,\n                                                            locales: _this.locales,\n                                                            defaultLocale: _this.defaultLocale\n                                                        })\n                                                    ];\n                                                case 3:\n                                                    return [\n                                                        2,\n                                                        (_tmp.props = _state.sent(), _tmp)\n                                                    ];\n                                            }\n                                        });\n                                    }))\n                                ];\n                            case 9:\n                                _ref = _state.sent(), props = _ref.props, cacheKey = _ref.cacheKey;\n                                // Only bust the data cache for SSP routes although\n                                // middleware can skip cache per request with\n                                // x-middleware-cache: no-cache as well\n                                if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                                    delete _this.sdc[cacheKey];\n                                }\n                                // we kick off a HEAD request in the background\n                                // when a non-prefetch request is made to signal revalidation\n                                if (!_this.isPreview && routeInfo.__N_SSG && \"development\" !== 'development' && 0) {}\n                                props.pageProps = Object.assign({}, props.pageProps);\n                                routeInfo.props = props;\n                                routeInfo.route = route;\n                                routeInfo.query = query;\n                                routeInfo.resolvedAs = resolvedAs;\n                                _this.components[route] = routeInfo;\n                                return [\n                                    2,\n                                    routeInfo\n                                ];\n                            case 10:\n                                err = _state.sent();\n                                return [\n                                    2,\n                                    _this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps)\n                                ];\n                            case 11:\n                                return [\n                                    2\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"set\",\n            value: function set(state, data, resetScroll) {\n                this.state = state;\n                return this.sub(data, this.components['/_app'].Component, resetScroll);\n            }\n        },\n        {\n            /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ key: \"beforePopState\",\n            value: function beforePopState(cb) {\n                this._bps = cb;\n            }\n        },\n        {\n            key: \"onlyAHashChange\",\n            value: function onlyAHashChange(as) {\n                if (!this.asPath) return false;\n                var _this_asPath_split = _sliced_to_array._(this.asPath.split('#', 2), 2), oldUrlNoHash = _this_asPath_split[0], oldHash = _this_asPath_split[1];\n                var _as_split = _sliced_to_array._(as.split('#', 2), 2), newUrlNoHash = _as_split[0], newHash = _as_split[1];\n                // Makes sure we scroll to the provided hash if the url/hash are the same\n                if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n                    return true;\n                }\n                // If the urls are change, there's more than a hash change\n                if (oldUrlNoHash !== newUrlNoHash) {\n                    return false;\n                }\n                // If the hash has changed, then it's a hash only change.\n                // This check is necessary to handle both the enter and\n                // leave hash === '' cases. The identity case falls through\n                // and is treated as a next reload.\n                return oldHash !== newHash;\n            }\n        },\n        {\n            key: \"scrollToHash\",\n            value: function scrollToHash(as) {\n                var _as_split = _sliced_to_array._(as.split('#', 2), 2), tmp = _as_split[1], hash = tmp === void 0 ? '' : tmp;\n                (0, _handlesmoothscroll.handleSmoothScroll)(function() {\n                    // Scroll to top if the hash is just `#` with no value or `#top`\n                    // To mirror browsers\n                    if (hash === '' || hash === 'top') {\n                        window.scrollTo(0, 0);\n                        return;\n                    }\n                    // Decode hash to make non-latin anchor works.\n                    var rawHash = decodeURIComponent(hash);\n                    // First we check if the element by id is found\n                    var idEl = document.getElementById(rawHash);\n                    if (idEl) {\n                        idEl.scrollIntoView();\n                        return;\n                    }\n                    // If there's no element with the id, we check the `name` property\n                    // To mirror browsers\n                    var nameEl = document.getElementsByName(rawHash)[0];\n                    if (nameEl) {\n                        nameEl.scrollIntoView();\n                    }\n                }, {\n                    onlyHashChange: this.onlyAHashChange(as)\n                });\n            }\n        },\n        {\n            key: \"urlIsNew\",\n            value: function urlIsNew(asPath) {\n                return this.asPath !== asPath;\n            }\n        },\n        {\n            key: \"prefetch\",\n            value: /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ function prefetch(url, asPath, options) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var parsed, urlPathname, pathname, query, originalPathname, parsedAs, localePathResult, pages, resolvedAs, locale, isMiddlewareMatch, rewrites, ref, rewritesResult, data, _tmp, route;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                if (asPath === void 0) asPath = url;\n                                if (options === void 0) options = {};\n                                // Prefetch is not supported in development mode because it would trigger on-demand-entries\n                                if (true) {\n                                    return [\n                                        2\n                                    ];\n                                }\n                                if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n                                    // No prefetches for bots that render the link since they are typically navigating\n                                    // links via the equivalent of a hard navigation and hence never utilize these\n                                    // prefetches.\n                                    return [\n                                        2\n                                    ];\n                                }\n                                parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n                                urlPathname = parsed.pathname;\n                                pathname = parsed.pathname, query = parsed.query;\n                                originalPathname = pathname;\n                                if (true) {\n                                    if (options.locale === false) {\n                                        pathname = (0, _normalizelocalepath.normalizeLocalePath)(pathname, _this.locales).pathname;\n                                        parsed.pathname = pathname;\n                                        url = (0, _formaturl.formatWithValidation)(parsed);\n                                        parsedAs = (0, _parserelativeurl.parseRelativeUrl)(asPath);\n                                        localePathResult = (0, _normalizelocalepath.normalizeLocalePath)(parsedAs.pathname, _this.locales);\n                                        parsedAs.pathname = localePathResult.pathname;\n                                        options.locale = localePathResult.detectedLocale || _this.defaultLocale;\n                                        asPath = (0, _formaturl.formatWithValidation)(parsedAs);\n                                    }\n                                }\n                                return [\n                                    4,\n                                    _this.pageLoader.getPageList()\n                                ];\n                            case 1:\n                                pages = _state.sent();\n                                resolvedAs = asPath;\n                                locale = typeof options.locale !== 'undefined' ? options.locale || undefined : _this.locale;\n                                return [\n                                    4,\n                                    matchesMiddleware({\n                                        asPath: asPath,\n                                        locale: locale,\n                                        router: _this\n                                    })\n                                ];\n                            case 2:\n                                isMiddlewareMatch = _state.sent();\n                                if (!( true && asPath.startsWith('/'))) return [\n                                    3,\n                                    4\n                                ];\n                                return [\n                                    4,\n                                    (0, _routeloader.getClientBuildManifest)()\n                                ];\n                            case 3:\n                                ref = _state.sent(), rewrites = ref.__rewrites, ref;\n                                rewritesResult = (0, _resolverewrites[\"default\"])((0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asPath, _this.locale), true), pages, rewrites, parsed.query, function(p) {\n                                    return resolveDynamicRoute(p, pages);\n                                }, _this.locales);\n                                if (rewritesResult.externalDest) {\n                                    return [\n                                        2\n                                    ];\n                                }\n                                if (!isMiddlewareMatch) {\n                                    resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(rewritesResult.asPath), _this.locale);\n                                }\n                                if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n                                    // if this directly matches a page we need to update the href to\n                                    // allow the correct page chunk to be loaded\n                                    pathname = rewritesResult.resolvedHref;\n                                    parsed.pathname = pathname;\n                                    if (!isMiddlewareMatch) {\n                                        url = (0, _formaturl.formatWithValidation)(parsed);\n                                    }\n                                }\n                                _state.label = 4;\n                            case 4:\n                                parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n                                if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n                                    pathname = parsed.pathname;\n                                    parsed.pathname = pathname;\n                                    Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n                                    if (!isMiddlewareMatch) {\n                                        url = (0, _formaturl.formatWithValidation)(parsed);\n                                    }\n                                }\n                                if (true) return [\n                                    3,\n                                    5\n                                ];\n                                _tmp = null;\n                                return [\n                                    3,\n                                    7\n                                ];\n                            case 5:\n                                return [\n                                    4,\n                                    withMiddlewareEffects({\n                                        fetchData: function() {\n                                            return fetchNextData({\n                                                dataHref: _this.pageLoader.getDataHref({\n                                                    href: (0, _formaturl.formatWithValidation)({\n                                                        pathname: originalPathname,\n                                                        query: query\n                                                    }),\n                                                    skipInterpolation: true,\n                                                    asPath: resolvedAs,\n                                                    locale: locale\n                                                }),\n                                                hasMiddleware: true,\n                                                isServerRender: false,\n                                                parseJSON: true,\n                                                inflightCache: _this.sdc,\n                                                persistCache: !_this.isPreview,\n                                                isPrefetch: true\n                                            });\n                                        },\n                                        asPath: asPath,\n                                        locale: locale,\n                                        router: _this\n                                    })\n                                ];\n                            case 6:\n                                _tmp = _state.sent();\n                                _state.label = 7;\n                            case 7:\n                                data = _tmp;\n                                /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === 'rewrite') {\n                                    parsed.pathname = data.effect.resolvedHref;\n                                    pathname = data.effect.resolvedHref;\n                                    query = _object_spread._({}, query, data.effect.parsedAs.query);\n                                    resolvedAs = data.effect.parsedAs.pathname;\n                                    url = (0, _formaturl.formatWithValidation)(parsed);\n                                }\n                                /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === 'redirect-external') {\n                                    return [\n                                        2\n                                    ];\n                                }\n                                route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n                                return [\n                                    4,\n                                    _this._bfl(asPath, resolvedAs, options.locale, true)\n                                ];\n                            case 8:\n                                if (_state.sent()) {\n                                    _this.components[urlPathname] = {\n                                        __appRouter: true\n                                    };\n                                }\n                                return [\n                                    4,\n                                    Promise.all([\n                                        _this.pageLoader._isSsg(route).then(function(isSsg) {\n                                            return isSsg ? fetchNextData({\n                                                dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : _this.pageLoader.getDataHref({\n                                                    href: url,\n                                                    asPath: resolvedAs,\n                                                    locale: locale\n                                                }),\n                                                isServerRender: false,\n                                                parseJSON: true,\n                                                inflightCache: _this.sdc,\n                                                persistCache: !_this.isPreview,\n                                                isPrefetch: true,\n                                                unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                                            }).then(function() {\n                                                return false;\n                                            })[\"catch\"](function() {\n                                                return false;\n                                            }) : false;\n                                        }),\n                                        _this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route)\n                                    ])\n                                ];\n                            case 9:\n                                _state.sent();\n                                return [\n                                    2\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"fetchComponent\",\n            value: function fetchComponent(route) {\n                var _this = this;\n                return _async_to_generator._(function() {\n                    var handleCancelled, componentResult, err;\n                    return _ts_generator._(this, function(_state) {\n                        switch(_state.label){\n                            case 0:\n                                handleCancelled = getCancelledHandler({\n                                    route: route,\n                                    router: _this\n                                });\n                                _state.label = 1;\n                            case 1:\n                                _state.trys.push([\n                                    1,\n                                    3,\n                                    ,\n                                    4\n                                ]);\n                                return [\n                                    4,\n                                    _this.pageLoader.loadPage(route)\n                                ];\n                            case 2:\n                                componentResult = _state.sent();\n                                handleCancelled();\n                                return [\n                                    2,\n                                    componentResult\n                                ];\n                            case 3:\n                                err = _state.sent();\n                                handleCancelled();\n                                throw err;\n                            case 4:\n                                return [\n                                    2\n                                ];\n                        }\n                    });\n                })();\n            }\n        },\n        {\n            key: \"_getData\",\n            value: function _getData(fn) {\n                var _this = this;\n                var cancelled = false;\n                var cancel = function() {\n                    cancelled = true;\n                };\n                this.clc = cancel;\n                return fn().then(function(data) {\n                    if (cancel === _this.clc) {\n                        _this.clc = null;\n                    }\n                    if (cancelled) {\n                        var err = Object.defineProperty(new Error('Loading initial props cancelled'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E405\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                        err.cancelled = true;\n                        throw err;\n                    }\n                    return data;\n                });\n            }\n        },\n        {\n            key: \"getInitialProps\",\n            value: function getInitialProps(Component, ctx) {\n                var _this_components__app = this.components['/_app'], App = _this_components__app.Component;\n                var AppTree = this._wrapApp(App);\n                ctx.AppTree = AppTree;\n                return (0, _utils.loadGetInitialProps)(App, {\n                    AppTree: AppTree,\n                    Component: Component,\n                    router: this,\n                    ctx: ctx\n                });\n            }\n        },\n        {\n            key: \"route\",\n            get: function get() {\n                return this.state.route;\n            }\n        },\n        {\n            key: \"pathname\",\n            get: function get() {\n                return this.state.pathname;\n            }\n        },\n        {\n            key: \"query\",\n            get: function get() {\n                return this.state.query;\n            }\n        },\n        {\n            key: \"asPath\",\n            get: function get() {\n                return this.state.asPath;\n            }\n        },\n        {\n            key: \"locale\",\n            get: function get() {\n                return this.state.locale;\n            }\n        },\n        {\n            key: \"isFallback\",\n            get: function get() {\n                return this.state.isFallback;\n            }\n        },\n        {\n            key: \"isPreview\",\n            get: function get() {\n                return this.state.isPreview;\n            }\n        }\n    ]);\n    return Router;\n}();\nRouter.events = (0, _mitt[\"default\"])(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});