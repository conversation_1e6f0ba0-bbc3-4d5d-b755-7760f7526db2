'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Card, Row, Col, Statistic, Progress, Timeline, Table, Tag } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  RobotOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  SafetyOutlined,
  ToolOutlined,
  ShoppingCartOutlined,
  BookOutlined,
  MessageOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { Line, Column, Gauge, Liquid } from '@ant-design/charts';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const { Header, Sider, Content } = Layout;

// 模拟数据
const mockData = {
  overview: {
    totalEquipment: 156,
    runningEquipment: 142,
    maintenanceEquipment: 8,
    errorEquipment: 6,
    oeeRate: 0.85,
    qualityRate: 0.96,
    productionOutput: 2847,
    energyConsumption: 1234.5,
  },
  productionTrend: [
    { time: '00:00', output: 120, target: 130 },
    { time: '02:00', output: 132, target: 130 },
    { time: '04:00', output: 101, target: 130 },
    { time: '06:00', output: 134, target: 130 },
    { time: '08:00', output: 90, target: 130 },
    { time: '10:00', output: 230, target: 130 },
    { time: '12:00', output: 210, target: 130 },
    { time: '14:00', output: 220, target: 130 },
    { time: '16:00', output: 200, target: 130 },
    { time: '18:00', output: 180, target: 130 },
    { time: '20:00', output: 160, target: 130 },
    { time: '22:00', output: 140, target: 130 },
  ],
  equipmentStatus: [
    { name: 'CNC-001', status: 'running', utilization: 85, temperature: 45 },
    { name: 'CNC-002', status: 'running', utilization: 92, temperature: 48 },
    { name: 'Robot-001', status: 'idle', utilization: 0, temperature: 25 },
    { name: 'Press-001', status: 'maintenance', utilization: 0, temperature: 30 },
    { name: 'Grinder-001', status: 'error', utilization: 0, temperature: 65 },
  ],
  recentAlerts: [
    { id: 1, type: 'warning', message: 'CNC-001温度异常', time: '2分钟前' },
    { id: 2, type: 'error', message: 'Grinder-001故障停机', time: '15分钟前' },
    { id: 3, type: 'info', message: 'Robot-001完成维护', time: '1小时前' },
    { id: 4, type: 'success', message: '生产线A达成日产目标', time: '2小时前' },
  ],
  agentTasks: [
    { id: 1, name: '生产排产优化', agent: '排产智能体', status: 'running', progress: 75 },
    { id: 2, name: '设备预测维护', agent: '维护智能体', status: 'completed', progress: 100 },
    { id: 3, name: '质量异常分析', agent: '质量智能体', status: 'pending', progress: 0 },
    { id: 4, name: '供应链风险评估', agent: '供应链智能体', status: 'running', progress: 45 },
  ],
};

export default function HomePage() {
  const [collapsed, setCollapsed] = useState(false);
  const router = useRouter();

  // 菜单项
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '总览',
    },
    {
      key: 'agents',
      icon: <RobotOutlined />,
      label: '智能体',
      children: [
        { key: 'agent-chat', label: '智能对话' },
        { key: 'agent-workflow', label: '工作流' },
        { key: 'agent-tasks', label: '任务管理' },
      ],
    },
    {
      key: 'production',
      icon: <BarChartOutlined />,
      label: '生产管理',
      children: [
        { key: 'production-planning', label: '生产计划' },
        { key: 'production-monitoring', label: '生产监控' },
        { key: 'production-analysis', label: '生产分析' },
      ],
    },
    {
      key: 'quality',
      icon: <SafetyOutlined />,
      label: '质量管理',
      children: [
        { key: 'quality-inspection', label: '质量检验' },
        { key: 'quality-analysis', label: '质量分析' },
        { key: 'quality-standards', label: '质量标准' },
      ],
    },
    {
      key: 'maintenance',
      icon: <ToolOutlined />,
      label: '设备维护',
      children: [
        { key: 'equipment-monitoring', label: '设备监控' },
        { key: 'predictive-maintenance', label: '预测维护' },
        { key: 'maintenance-planning', label: '维护计划' },
      ],
    },
    {
      key: 'supply-chain',
      icon: <ShoppingCartOutlined />,
      label: '供应链',
      children: [
        { key: 'inventory-management', label: '库存管理' },
        { key: 'supplier-management', label: '供应商管理' },
        { key: 'procurement', label: '采购管理' },
      ],
    },
    {
      key: 'knowledge',
      icon: <BookOutlined />,
      label: '知识库',
      children: [
        { key: 'knowledge-search', label: '知识搜索' },
        { key: 'knowledge-graph', label: '知识图谱' },
        { key: 'document-management', label: '文档管理' },
      ],
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 生产趋势图配置
  const productionTrendConfig = {
    data: mockData.productionTrend,
    xField: 'time',
    yField: 'output',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // OEE仪表盘配置
  const oeeGaugeConfig = {
    percent: mockData.overview.oeeRate,
    range: {
      color: ['#F4664A', '#FAAD14', '#30BF78'],
    },
    indicator: {
      pointer: {
        style: {
          stroke: '#D0D0D0',
        },
      },
      pin: {
        style: {
          stroke: '#D0D0D0',
        },
      },
    },
    statistic: {
      content: {
        style: {
          fontSize: '36px',
          lineHeight: '36px',
        },
      },
    },
  };

  // 设备状态表格列
  const equipmentColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          running: { color: 'green', text: '运行中' },
          idle: { color: 'orange', text: '空闲' },
          maintenance: { color: 'blue', text: '维护中' },
          error: { color: 'red', text: '故障' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (utilization: number) => (
        <Progress percent={utilization} size="small" />
      ),
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
      render: (temperature: number) => `${temperature}°C`,
    },
  ];

  // 智能体任务表格列
  const taskColumns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '智能体',
      dataIndex: 'agent',
      key: 'agent',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          running: { color: 'blue', text: '执行中', icon: <PlayCircleOutlined /> },
          completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> },
          pending: { color: 'orange', text: '等待中', icon: <ClockCircleOutlined /> },
          error: { color: 'red', text: '失败', icon: <ExclamationCircleOutlined /> },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return (
          <Tag color={statusInfo.color} icon={statusInfo.icon}>
            {statusInfo.text}
          </Tag>
        );
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="dark">
        <div style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.3)', borderRadius: 6 }} />
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          items={menuItems}
          onClick={({ key }) => {
            if (key !== 'dashboard') {
              router.push(`/${key}`);
            }
          }}
        />
      </Sider>
      
      <Layout>
        <Header style={{ padding: 0, background: '#fff', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          
          <div style={{ display: 'flex', alignItems: 'center', marginRight: 24 }}>
            <Badge count={5} style={{ marginRight: 24 }}>
              <Button type="text" icon={<BellOutlined />} size="large" />
            </Badge>
            
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
                <span>管理员</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content style={{ margin: '24px 16px', padding: 24, background: '#f0f2f5', minHeight: 280 }}>
          {/* 概览统计 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="设备总数"
                  value={mockData.overview.totalEquipment}
                  prefix={<ToolOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="运行设备"
                  value={mockData.overview.runningEquipment}
                  prefix={<PlayCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="今日产量"
                  value={mockData.overview.productionOutput}
                  suffix="件"
                  prefix={<BarChartOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="质量合格率"
                  value={mockData.overview.qualityRate * 100}
                  suffix="%"
                  prefix={<SafetyOutlined />}
                  valueStyle={{ color: '#13c2c2' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 主要图表 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} lg={16}>
              <Card title="生产趋势" extra={<Link href="/production/monitoring">查看详情</Link>}>
                <div style={{ height: 300 }}>
                  <Line {...productionTrendConfig} />
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="OEE指标">
                <div style={{ height: 300 }}>
                  <Gauge {...oeeGaugeConfig} />
                </div>
              </Card>
            </Col>
          </Row>

          {/* 详细信息 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="设备状态" extra={<Link href="/maintenance/equipment-monitoring">查看全部</Link>}>
                <Table
                  dataSource={mockData.equipmentStatus}
                  columns={equipmentColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="智能体任务" extra={<Link href="/agents/tasks">查看全部</Link>}>
                <Table
                  dataSource={mockData.agentTasks}
                  columns={taskColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>

          {/* 最近告警 */}
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="最近告警" extra={<Link href="/alerts">查看全部</Link>}>
                <Timeline>
                  {mockData.recentAlerts.map((alert) => (
                    <Timeline.Item
                      key={alert.id}
                      color={
                        alert.type === 'error' ? 'red' :
                        alert.type === 'warning' ? 'orange' :
                        alert.type === 'success' ? 'green' : 'blue'
                      }
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>{alert.message}</span>
                        <span style={{ color: '#999', fontSize: '12px' }}>{alert.time}</span>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
}
