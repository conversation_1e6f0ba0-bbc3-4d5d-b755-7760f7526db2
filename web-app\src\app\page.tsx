'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Card, Row, Col, Statistic, Progress, Timeline, Table, Tag } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  RobotOutlined,
  Bar<PERSON>hartOutlined,
  SafetyOutlined,
  ToolOutlined,
  ShoppingCartOutlined,
  BookOutlined,
  MessageOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { Line, Column, Gauge, Liquid } from '@ant-design/charts';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import CountUp from 'react-countup';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useDashboard, useSystemHealth } from '@/hooks/useApi';

const { Header, Sider, Content } = Layout;

// 工业风格样式组件
const IndustrialLayout = styled(Layout)`
  min-height: 100vh;
  background: var(--bg-primary);
`;

const IndustrialHeader = styled(Header)`
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--color-accent-blue) 50%,
      transparent 100%);
    opacity: 0.6;
  }
`;

const IndustrialSider = styled(Sider)`
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);

  .ant-layout-sider-trigger {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
    color: var(--text-secondary);

    &:hover {
      background: var(--color-accent-blue);
      color: white;
    }
  }
`;

const IndustrialContent = styled(Content)`
  background: var(--bg-primary);
  padding: 24px;
  overflow-y: auto;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);

  .logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
  }
`;

const StatusIndicator = styled.div<{ status: 'online' | 'warning' | 'offline' }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props =>
    props.status === 'online' ? 'var(--status-success)' :
    props.status === 'warning' ? 'var(--status-warning)' :
    'var(--status-error)'
  };
  box-shadow: 0 0 10px ${props =>
    props.status === 'online' ? 'var(--status-success)' :
    props.status === 'warning' ? 'var(--status-warning)' :
    'var(--status-error)'
  };
  animation: pulse 2s infinite;
`;

const DashboardCard = styled(Card)`
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      var(--color-accent-blue),
      var(--color-accent-green));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);

    &::before {
      opacity: 1;
    }
  }

  .ant-card-head {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
  }

  .ant-card-body {
    background: var(--bg-card);
  }
`;

const MetricCard = styled(DashboardCard)`
  text-align: center;

  .ant-statistic-title {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    color: var(--text-primary);
    font-size: 32px;
    font-weight: 700;
  }
`;

const SectionTitle = styled.h2`
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

// 模拟数据
const mockData = {
  overview: {
    totalEquipment: 156,
    runningEquipment: 142,
    maintenanceEquipment: 8,
    errorEquipment: 6,
    oeeRate: 0.85,
    qualityRate: 0.96,
    productionOutput: 2847,
    energyConsumption: 1234.5,
  },
  productionTrend: [
    { time: '00:00', output: 120, target: 130 },
    { time: '02:00', output: 132, target: 130 },
    { time: '04:00', output: 101, target: 130 },
    { time: '06:00', output: 134, target: 130 },
    { time: '08:00', output: 90, target: 130 },
    { time: '10:00', output: 230, target: 130 },
    { time: '12:00', output: 210, target: 130 },
    { time: '14:00', output: 220, target: 130 },
    { time: '16:00', output: 200, target: 130 },
    { time: '18:00', output: 180, target: 130 },
    { time: '20:00', output: 160, target: 130 },
    { time: '22:00', output: 140, target: 130 },
  ],
  equipmentStatus: [
    { name: 'CNC-001', status: 'running', utilization: 85, temperature: 45 },
    { name: 'CNC-002', status: 'running', utilization: 92, temperature: 48 },
    { name: 'Robot-001', status: 'idle', utilization: 0, temperature: 25 },
    { name: 'Press-001', status: 'maintenance', utilization: 0, temperature: 30 },
    { name: 'Grinder-001', status: 'error', utilization: 0, temperature: 65 },
  ],
  recentAlerts: [
    { id: 1, type: 'warning', message: 'CNC-001温度异常', time: '2分钟前' },
    { id: 2, type: 'error', message: 'Grinder-001故障停机', time: '15分钟前' },
    { id: 3, type: 'info', message: 'Robot-001完成维护', time: '1小时前' },
    { id: 4, type: 'success', message: '生产线A达成日产目标', time: '2小时前' },
  ],
  agentTasks: [
    { id: 1, name: '生产排产优化', agent: '排产智能体', status: 'running', progress: 75 },
    { id: 2, name: '设备预测维护', agent: '维护智能体', status: 'completed', progress: 100 },
    { id: 3, name: '质量异常分析', agent: '质量智能体', status: 'pending', progress: 0 },
    { id: 4, name: '供应链风险评估', agent: '供应链智能体', status: 'running', progress: 45 },
  ],
};

export default function HomePage() {
  const [collapsed, setCollapsed] = useState(false);
  const router = useRouter();

  // 获取仪表板数据
  const { data: dashboardData, loading, error, refetch } = useDashboard();

  // 获取系统健康状态
  const { isHealthy } = useSystemHealth();

  // 根据系统健康状态确定状态指示器
  const systemStatus: 'online' | 'warning' | 'offline' = isHealthy ? 'online' : 'offline';

  // 使用真实数据或回退到模拟数据
  const displayData = dashboardData || {
    overview: {
      total_production_lines: mockData.overview.totalEquipment,
      active_lines: mockData.overview.runningEquipment,
      average_efficiency: mockData.overview.oeeRate * 100,
      total_equipment: mockData.overview.totalEquipment,
      operational_equipment: mockData.overview.runningEquipment
    },
    production_lines: [],
    equipment: [],
    quality_metrics: {
      defect_rate: 1 - mockData.overview.qualityRate,
      first_pass_yield: mockData.overview.qualityRate,
      customer_complaints: 2,
      quality_score: mockData.overview.qualityRate * 100
    },
    inventory_alerts: []
  };

  // 路径映射
  const pathMapping: { [key: string]: string } = {
    'dashboard': '/',
    'agents': '/agents',
    'agent-chat': '/agents/chat',
    'agent-workflow': '/agents/orchestration',
    'agent-tasks': '/agents/tasks',
    'production': '/production',
    'production-planning': '/production',
    'production-monitoring': '/production',
    'production-analysis': '/production',
    'quality': '/quality',
    'quality-inspection': '/quality',
    'quality-analysis': '/quality',
    'quality-standards': '/quality',
    'maintenance': '/maintenance',
    'equipment-monitoring': '/maintenance',
    'predictive-maintenance': '/maintenance',
    'maintenance-planning': '/maintenance',
    'supply-chain': '/supply-chain',
    'inventory-management': '/supply-chain',
    'supplier-management': '/supply-chain',
    'procurement': '/supply-chain',
    'knowledge': '/knowledge',
    'knowledge-search': '/knowledge',
    'knowledge-graph': '/knowledge',
    'document-management': '/knowledge',
    'monitoring': '/monitoring'
  };

  // 菜单项
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '总览',
    },
    {
      key: 'agents',
      icon: <RobotOutlined />,
      label: '智能体',
      children: [
        { key: 'agent-chat', label: '智能对话' },
        { key: 'agent-workflow', label: '工作流' },
        { key: 'agent-tasks', label: '任务管理' },
      ],
    },
    {
      key: 'production',
      icon: <BarChartOutlined />,
      label: '生产管理',
      children: [
        { key: 'production-planning', label: '生产计划' },
        { key: 'production-monitoring', label: '生产监控' },
        { key: 'production-analysis', label: '生产分析' },
      ],
    },
    {
      key: 'quality',
      icon: <SafetyOutlined />,
      label: '质量管理',
      children: [
        { key: 'quality-inspection', label: '质量检验' },
        { key: 'quality-analysis', label: '质量分析' },
        { key: 'quality-standards', label: '质量标准' },
      ],
    },
    {
      key: 'maintenance',
      icon: <ToolOutlined />,
      label: '设备维护',
      children: [
        { key: 'equipment-monitoring', label: '设备监控' },
        { key: 'predictive-maintenance', label: '预测维护' },
        { key: 'maintenance-planning', label: '维护计划' },
      ],
    },
    {
      key: 'supply-chain',
      icon: <ShoppingCartOutlined />,
      label: '供应链',
      children: [
        { key: 'inventory-management', label: '库存管理' },
        { key: 'supplier-management', label: '供应商管理' },
        { key: 'procurement', label: '采购管理' },
      ],
    },
    {
      key: 'knowledge',
      icon: <BookOutlined />,
      label: '知识库',
      children: [
        { key: 'knowledge-search', label: '知识搜索' },
        { key: 'knowledge-graph', label: '知识图谱' },
        { key: 'document-management', label: '文档管理' },
      ],
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 生产趋势图配置
  const productionTrendConfig = {
    data: mockData.productionTrend,
    xField: 'time',
    yField: 'output',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // OEE仪表盘配置
  const oeeGaugeConfig = {
    percent: mockData.overview.oeeRate,
    range: {
      color: ['#F4664A', '#FAAD14', '#30BF78'],
    },
    indicator: {
      pointer: {
        style: {
          stroke: '#D0D0D0',
        },
      },
      pin: {
        style: {
          stroke: '#D0D0D0',
        },
      },
    },
    statistic: {
      content: {
        style: {
          fontSize: '36px',
          lineHeight: '36px',
        },
      },
    },
  };

  // 设备状态表格列
  const equipmentColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          running: { color: 'green', text: '运行中' },
          idle: { color: 'orange', text: '空闲' },
          maintenance: { color: 'blue', text: '维护中' },
          error: { color: 'red', text: '故障' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (utilization: number) => (
        <Progress percent={utilization} size="small" />
      ),
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
      render: (temperature: number) => `${temperature}°C`,
    },
  ];

  // 智能体任务表格列
  const taskColumns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '智能体',
      dataIndex: 'agent',
      key: 'agent',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          running: { color: 'blue', text: '执行中', icon: <PlayCircleOutlined /> },
          completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> },
          pending: { color: 'orange', text: '等待中', icon: <ClockCircleOutlined /> },
          error: { color: 'red', text: '失败', icon: <ExclamationCircleOutlined /> },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return (
          <Tag color={statusInfo.color} icon={statusInfo.icon}>
            {statusInfo.text}
          </Tag>
        );
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
  ];

  return (
    <IndustrialLayout>
      <IndustrialSider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        collapsedWidth={80}
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          style={{ padding: '16px' }}
        >
          <Logo>
            <div className="logo-icon">
              <RobotOutlined />
            </div>
            <AnimatePresence>
              {!collapsed && (
                <motion.span
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  工业智能体
                </motion.span>
              )}
            </AnimatePresence>
          </Logo>
        </motion.div>

        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          items={menuItems}
          style={{ border: 'none' }}
          onClick={({ key }) => {
            const targetPath = pathMapping[key];
            if (targetPath && targetPath !== '/') {
              router.push(targetPath);
            }
          }}
        />
      </IndustrialSider>

      <Layout>
        <IndustrialHeader>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 40,
                height: 40,
                color: 'var(--text-secondary)'
              }}
            />

            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <StatusIndicator status={systemStatus} />
              <span style={{ color: 'var(--text-secondary)', fontSize: '14px' }}>
                系统状态: {
                  systemStatus === 'online' ? '正常' :
                  systemStatus === 'warning' ? '警告' : '离线'
                }
              </span>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: 'var(--text-secondary)' }}
              />
            </Badge>

            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <Avatar
                  icon={<UserOutlined />}
                  style={{
                    marginRight: 8,
                    background: 'var(--color-accent-blue)'
                  }}
                />
                <span style={{ color: 'var(--text-primary)' }}>管理员</span>
              </div>
            </Dropdown>
          </div>
        </IndustrialHeader>
        
        <IndustrialContent>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <SectionTitle>总览仪表板</SectionTitle>
              <Button
                icon={<ReloadOutlined />}
                onClick={refetch}
                loading={loading}
                className="metal-button"
              >
                刷新数据
              </Button>
            </div>

            {/* 关键指标卡片 */}
            <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
              <Col xs={24} sm={12} lg={6}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <MetricCard>
                    <Statistic
                      title="生产线总数"
                      value={dashboardData?.overview?.total_production_lines || displayData.overview.total_production_lines}
                      prefix={<ToolOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                      formatter={(value) => <CountUp end={value as number} duration={2} />}
                    />
                    <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--text-muted)' }}>
                      运行中: {dashboardData?.overview?.active_lines || displayData.overview.active_lines} 条
                    </div>
                  </MetricCard>
                </motion.div>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <MetricCard>
                    <Statistic
                      title="平均效率"
                      value={dashboardData?.overview?.average_efficiency || displayData.overview.average_efficiency}
                      precision={1}
                      suffix="%"
                      prefix={<ArrowUpOutlined style={{ color: 'var(--status-success)' }} />}
                      formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                    />
                    <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-success)' }}>
                      较昨日 +2.3%
                    </div>
                  </MetricCard>
                </motion.div>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <MetricCard>
                    <Statistic
                      title="质量评分"
                      value={dashboardData?.quality_metrics?.quality_score || displayData.quality_metrics.quality_score}
                      precision={1}
                      prefix={<SafetyOutlined style={{ color: 'var(--color-accent-green)' }} />}
                      formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                    />
                    <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-success)' }}>
                      缺陷率: {((dashboardData?.quality_metrics?.defect_rate || displayData.quality_metrics.defect_rate) * 100).toFixed(2)}%
                    </div>
                  </MetricCard>
                </motion.div>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <MetricCard>
                    <Statistic
                      title="库存预警"
                      value={dashboardData?.inventory_alerts?.length || displayData.inventory_alerts.length}
                      prefix={<ShoppingCartOutlined style={{ color: 'var(--status-warning)' }} />}
                      formatter={(value) => <CountUp end={value as number} duration={2} />}
                    />
                    <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-warning)' }}>
                      需要立即处理
                    </div>
                  </MetricCard>
                </motion.div>
              </Col>
          </Row>

          {/* 主要图表 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} lg={16}>
              <Card title="生产趋势" extra={<Link href="/production/monitoring">查看详情</Link>}>
                <div style={{ height: 300 }}>
                  <Line {...productionTrendConfig} />
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="OEE指标">
                <div style={{ height: 300 }}>
                  <Gauge {...oeeGaugeConfig} />
                </div>
              </Card>
            </Col>
          </Row>

          {/* 详细信息 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="设备状态" extra={<Link href="/maintenance/equipment-monitoring">查看全部</Link>}>
                <Table
                  dataSource={mockData.equipmentStatus}
                  columns={equipmentColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="智能体任务" extra={<Link href="/agents/tasks">查看全部</Link>}>
                <Table
                  dataSource={mockData.agentTasks}
                  columns={taskColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>

          {/* 最近告警 */}
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="最近告警" extra={<Link href="/alerts">查看全部</Link>}>
                <Timeline>
                  {mockData.recentAlerts.map((alert) => (
                    <Timeline.Item
                      key={alert.id}
                      color={
                        alert.type === 'error' ? 'red' :
                        alert.type === 'warning' ? 'orange' :
                        alert.type === 'success' ? 'green' : 'blue'
                      }
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>{alert.message}</span>
                        <span style={{ color: '#999', fontSize: '12px' }}>{alert.time}</span>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Col>
          </Row>
          </motion.div>
        </IndustrialContent>
      </Layout>
    </IndustrialLayout>
  );
}
