"""
工业智能体平台 - 质量管理服务
实现质量检验、缺陷分析、质量控制等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, UploadFile, File
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import numpy as np
import pandas as pd
from dataclasses import dataclass
import uuid
from enum import Enum
import cv2
import base64
from io import BytesIO
from PIL import Image

# 导入算法模块
import sys
sys.path.append('../llm-service')
from algorithms import QualityInspectionAI

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
quality_ai = QualityInspectionAI()

class InspectionType(str, Enum):
    INCOMING = "incoming"
    IN_PROCESS = "in_process"
    FINAL = "final"
    PATROL = "patrol"
    CUSTOMER_RETURN = "customer_return"

class InspectionResult(str, Enum):
    PASS = "pass"
    FAIL = "fail"
    REWORK = "rework"
    CONDITIONAL_PASS = "conditional_pass"

class DefectSeverity(str, Enum):
    CRITICAL = "critical"
    MAJOR = "major"
    MINOR = "minor"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化数据库表
    await initialize_database()
    
    # 训练质量检测AI模型
    await train_quality_models()
    
    logger.info("Quality service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Quality service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="质量管理服务",
    description="质量检验、缺陷分析、质量控制",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class QualityInspectionCreate(BaseModel):
    product_id: str
    production_order_id: Optional[str] = None
    inspection_type: InspectionType
    inspector_id: str
    sample_size: int = 1
    inspection_plan_id: Optional[str] = None
    notes: Optional[str] = None

class QualityMeasurement(BaseModel):
    measurement_type: str  # dimension, weight, surface, hardness, etc.
    measured_value: float
    target_value: float
    tolerance_upper: float
    tolerance_lower: float
    unit: str
    measurement_method: Optional[str] = None
    equipment_id: Optional[str] = None

class QualityDefect(BaseModel):
    defect_type: str
    defect_description: str
    severity: DefectSeverity
    quantity: int = 1
    location: Optional[str] = None
    root_cause: Optional[str] = None
    corrective_action: Optional[str] = None
    image_data: Optional[str] = None  # Base64编码的图片

class InspectionResultCreate(BaseModel):
    inspection_id: str
    measurements: List[QualityMeasurement] = []
    defects: List[QualityDefect] = []
    overall_result: InspectionResult
    inspector_notes: Optional[str] = None
    images: List[str] = []  # Base64编码的图片列表

class QualityAnalysisRequest(BaseModel):
    start_date: datetime
    end_date: datetime
    product_ids: Optional[List[str]] = None
    inspection_types: Optional[List[InspectionType]] = None
    analysis_type: str = "trend"  # trend, pareto, control_chart, capability

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 检验计划表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS inspection_plans (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                plan_name VARCHAR(200) NOT NULL,
                product_id VARCHAR(50) NOT NULL REFERENCES products(product_code),
                inspection_type VARCHAR(20) NOT NULL,
                sampling_plan JSONB,
                measurement_requirements JSONB,
                acceptance_criteria JSONB,
                frequency VARCHAR(50),
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 质量测量数据表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS quality_measurements (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                inspection_id UUID NOT NULL REFERENCES quality_inspections(id),
                measurement_type VARCHAR(50) NOT NULL,
                measured_value DECIMAL(15,6),
                target_value DECIMAL(15,6),
                tolerance_upper DECIMAL(15,6),
                tolerance_lower DECIMAL(15,6),
                unit VARCHAR(20),
                measurement_method VARCHAR(100),
                equipment_id VARCHAR(50) REFERENCES equipment(id),
                is_within_tolerance BOOLEAN,
                deviation DECIMAL(15,6),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 质量控制图数据表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS quality_control_charts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                product_id VARCHAR(50) NOT NULL REFERENCES products(product_code),
                measurement_type VARCHAR(50) NOT NULL,
                chart_type VARCHAR(20) NOT NULL, -- xbar_r, xbar_s, p, np, c, u
                sample_date DATE,
                sample_value DECIMAL(15,6),
                sample_range DECIMAL(15,6),
                sample_size INTEGER,
                center_line DECIMAL(15,6),
                upper_control_limit DECIMAL(15,6),
                lower_control_limit DECIMAL(15,6),
                is_out_of_control BOOLEAN DEFAULT false,
                violation_rules TEXT[],
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 质量成本表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS quality_costs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                cost_category VARCHAR(20) NOT NULL, -- prevention, appraisal, internal_failure, external_failure
                cost_type VARCHAR(50) NOT NULL,
                amount DECIMAL(12,2),
                currency VARCHAR(3) DEFAULT 'CNY',
                cost_date DATE,
                product_id VARCHAR(50) REFERENCES products(product_code),
                description TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 供应商质量评估表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS supplier_quality_ratings (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                supplier_id UUID NOT NULL REFERENCES suppliers(id),
                evaluation_period_start DATE,
                evaluation_period_end DATE,
                quality_score DECIMAL(3,2),
                delivery_score DECIMAL(3,2),
                service_score DECIMAL(3,2),
                overall_score DECIMAL(3,2),
                defect_rate DECIMAL(5,4),
                on_time_delivery_rate DECIMAL(3,2),
                corrective_actions_count INTEGER DEFAULT 0,
                evaluation_notes TEXT,
                evaluator_id UUID REFERENCES users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

async def train_quality_models():
    """训练质量检测AI模型"""
    try:
        # 获取历史检验数据用于训练
        async with db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT qm.*, qi.result
                FROM quality_measurements qm
                JOIN quality_inspections qi ON qm.inspection_id = qi.id
                WHERE qi.inspection_date >= NOW() - INTERVAL '6 months'
                LIMIT 1000
            """)
            
            if len(rows) > 100:  # 需要足够的训练数据
                # 转换为DataFrame
                data = []
                for row in rows:
                    data.append({
                        'measured_value': float(row['measured_value']),
                        'target_value': float(row['target_value']),
                        'tolerance_upper': float(row['tolerance_upper']),
                        'tolerance_lower': float(row['tolerance_lower']),
                        'result': row['result']
                    })
                
                df = pd.DataFrame(data)
                
                # 训练模型
                quality_ai.train(df)
                logger.info("Quality AI models trained successfully")
            else:
                logger.warning("Insufficient data for training quality models")
                
    except Exception as e:
        logger.error(f"Error training quality models: {e}")

class QualityManager:
    """质量管理器"""
    
    @staticmethod
    async def create_inspection(inspection_data: QualityInspectionCreate) -> str:
        """创建质量检验"""
        try:
            inspection_id = str(uuid.uuid4())
            inspection_code = f"QI{datetime.now().strftime('%Y%m%d')}{inspection_id[:8]}"
            
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO quality_inspections 
                    (id, inspection_code, product_id, production_order_id, inspection_type,
                     inspector_id, sample_size, inspection_date, notes, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    """,
                    inspection_id,
                    inspection_code,
                    inspection_data.product_id,
                    inspection_data.production_order_id,
                    inspection_data.inspection_type.value,
                    inspection_data.inspector_id,
                    inspection_data.sample_size,
                    datetime.utcnow(),
                    inspection_data.notes,
                    datetime.utcnow()
                )
            
            # 发送事件
            await kafka_producer.send("quality_events", {
                "type": "inspection_created",
                "inspection_id": inspection_id,
                "product_id": inspection_data.product_id,
                "inspection_type": inspection_data.inspection_type.value,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Created quality inspection: {inspection_id}")
            return inspection_id
            
        except Exception as e:
            logger.error(f"Error creating quality inspection: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def record_inspection_result(result_data: InspectionResultCreate) -> bool:
        """记录检验结果"""
        try:
            async with db_pool.acquire() as conn:
                async with conn.transaction():
                    # 更新检验记录
                    await conn.execute(
                        """
                        UPDATE quality_inspections 
                        SET result = $1, defect_count = $2, updated_at = $3
                        WHERE id = $4
                        """,
                        result_data.overall_result.value,
                        len(result_data.defects),
                        datetime.utcnow(),
                        result_data.inspection_id
                    )
                    
                    # 记录测量数据
                    for measurement in result_data.measurements:
                        # 判断是否在公差范围内
                        is_within_tolerance = (
                            measurement.tolerance_lower <= measurement.measured_value <= measurement.tolerance_upper
                        )
                        deviation = abs(measurement.measured_value - measurement.target_value)
                        
                        await conn.execute(
                            """
                            INSERT INTO quality_measurements 
                            (inspection_id, measurement_type, measured_value, target_value,
                             tolerance_upper, tolerance_lower, unit, measurement_method,
                             equipment_id, is_within_tolerance, deviation)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                            """,
                            result_data.inspection_id,
                            measurement.measurement_type,
                            measurement.measured_value,
                            measurement.target_value,
                            measurement.tolerance_upper,
                            measurement.tolerance_lower,
                            measurement.unit,
                            measurement.measurement_method,
                            measurement.equipment_id,
                            is_within_tolerance,
                            deviation
                        )
                        
                        # 使用AI预测质量
                        try:
                            ai_prediction = quality_ai.predict_quality({
                                'measured_value': measurement.measured_value,
                                'target_value': measurement.target_value,
                                'tolerance_upper': measurement.tolerance_upper,
                                'tolerance_lower': measurement.tolerance_lower
                            })
                            
                            # 如果AI预测有问题，创建告警
                            if ai_prediction['is_defective'] and ai_prediction['defect_probability'] > 0.7:
                                await QualityManager._create_quality_alert(
                                    result_data.inspection_id,
                                    measurement.measurement_type,
                                    ai_prediction
                                )
                        except Exception as e:
                            logger.warning(f"AI prediction failed: {e}")
                    
                    # 记录缺陷数据
                    for defect in result_data.defects:
                        defect_id = str(uuid.uuid4())
                        await conn.execute(
                            """
                            INSERT INTO quality_defects 
                            (id, inspection_id, defect_type, defect_description, severity,
                             quantity, root_cause, corrective_action)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                            """,
                            defect_id,
                            result_data.inspection_id,
                            defect.defect_type,
                            defect.defect_description,
                            defect.severity.value,
                            defect.quantity,
                            defect.root_cause,
                            defect.corrective_action
                        )
                        
                        # 保存缺陷图片
                        if defect.image_data:
                            await QualityManager._save_defect_image(defect_id, defect.image_data)
            
            # 更新质量控制图
            await QualityManager._update_control_charts(result_data)
            
            # 发送事件
            await kafka_producer.send("quality_events", {
                "type": "inspection_completed",
                "inspection_id": result_data.inspection_id,
                "result": result_data.overall_result.value,
                "defect_count": len(result_data.defects),
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Recorded inspection result: {result_data.inspection_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording inspection result: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _create_quality_alert(inspection_id: str, measurement_type: str, ai_prediction: Dict[str, Any]):
        """创建质量告警"""
        try:
            # 发送到维护服务
            alert_data = {
                "type": "quality_alert",
                "inspection_id": inspection_id,
                "measurement_type": measurement_type,
                "defect_probability": ai_prediction['defect_probability'],
                "recommendations": ai_prediction['recommendations'],
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await kafka_producer.send("maintenance_events", alert_data)
            
        except Exception as e:
            logger.error(f"Error creating quality alert: {e}")
    
    @staticmethod
    async def _save_defect_image(defect_id: str, image_data: str):
        """保存缺陷图片"""
        try:
            # 解码Base64图片
            image_bytes = base64.b64decode(image_data)
            image = Image.open(BytesIO(image_bytes))
            
            # 保存到文件系统或对象存储
            image_path = f"defect_images/{defect_id}.jpg"
            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            image.save(image_path)
            
            # 可以进一步使用计算机视觉进行缺陷分析
            # defect_analysis = await analyze_defect_image(image)
            
        except Exception as e:
            logger.error(f"Error saving defect image: {e}")
    
    @staticmethod
    async def _update_control_charts(result_data: InspectionResultCreate):
        """更新质量控制图"""
        try:
            async with db_pool.acquire() as conn:
                # 获取检验信息
                inspection = await conn.fetchrow(
                    "SELECT product_id FROM quality_inspections WHERE id = $1",
                    result_data.inspection_id
                )
                
                if not inspection:
                    return
                
                product_id = inspection['product_id']
                
                for measurement in result_data.measurements:
                    # 计算控制限
                    control_limits = await QualityManager._calculate_control_limits(
                        product_id, measurement.measurement_type
                    )
                    
                    if control_limits:
                        # 检查是否超出控制限
                        is_out_of_control = (
                            measurement.measured_value > control_limits['ucl'] or
                            measurement.measured_value < control_limits['lcl']
                        )
                        
                        # 记录控制图数据点
                        await conn.execute(
                            """
                            INSERT INTO quality_control_charts 
                            (product_id, measurement_type, chart_type, sample_date,
                             sample_value, center_line, upper_control_limit, 
                             lower_control_limit, is_out_of_control)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                            """,
                            product_id,
                            measurement.measurement_type,
                            "xbar_r",  # 默认使用X-R控制图
                            datetime.utcnow().date(),
                            measurement.measured_value,
                            control_limits['center_line'],
                            control_limits['ucl'],
                            control_limits['lcl'],
                            is_out_of_control
                        )
                        
                        # 如果超出控制限，发送告警
                        if is_out_of_control:
                            await kafka_producer.send("quality_events", {
                                "type": "control_limit_violation",
                                "product_id": product_id,
                                "measurement_type": measurement.measurement_type,
                                "measured_value": measurement.measured_value,
                                "control_limits": control_limits,
                                "timestamp": datetime.utcnow().isoformat()
                            })
                            
        except Exception as e:
            logger.error(f"Error updating control charts: {e}")
    
    @staticmethod
    async def _calculate_control_limits(product_id: str, measurement_type: str) -> Optional[Dict[str, float]]:
        """计算控制限"""
        try:
            async with db_pool.acquire() as conn:
                # 获取最近25个数据点
                rows = await conn.fetch(
                    """
                    SELECT measured_value
                    FROM quality_measurements qm
                    JOIN quality_inspections qi ON qm.inspection_id = qi.id
                    WHERE qi.product_id = $1 AND qm.measurement_type = $2
                    ORDER BY qm.created_at DESC
                    LIMIT 25
                    """,
                    product_id,
                    measurement_type
                )
                
                if len(rows) < 10:  # 需要足够的数据点
                    return None
                
                values = [float(row['measured_value']) for row in rows]
                
                # 计算均值和标准差
                mean_value = np.mean(values)
                std_value = np.std(values, ddof=1)
                
                # 3-sigma控制限
                ucl = mean_value + 3 * std_value
                lcl = mean_value - 3 * std_value
                
                return {
                    'center_line': mean_value,
                    'ucl': ucl,
                    'lcl': lcl
                }
                
        except Exception as e:
            logger.error(f"Error calculating control limits: {e}")
            return None

    @staticmethod
    async def analyze_quality_trends(request: QualityAnalysisRequest) -> Dict[str, Any]:
        """质量趋势分析"""
        try:
            async with db_pool.acquire() as conn:
                # 构建查询条件
                where_conditions = ["qi.inspection_date BETWEEN $1 AND $2"]
                params = [request.start_date, request.end_date]
                param_count = 3

                if request.product_ids:
                    where_conditions.append(f"qi.product_id = ANY(${param_count})")
                    params.append(request.product_ids)
                    param_count += 1

                if request.inspection_types:
                    where_conditions.append(f"qi.inspection_type = ANY(${param_count})")
                    params.append([t.value for t in request.inspection_types])
                    param_count += 1

                where_clause = " AND ".join(where_conditions)

                if request.analysis_type == "trend":
                    # 质量趋势分析
                    rows = await conn.fetch(f"""
                        SELECT
                            DATE(qi.inspection_date) as inspection_date,
                            COUNT(*) as total_inspections,
                            SUM(CASE WHEN qi.result = 'pass' THEN 1 ELSE 0 END) as pass_count,
                            SUM(CASE WHEN qi.result = 'fail' THEN 1 ELSE 0 END) as fail_count,
                            SUM(qi.defect_count) as total_defects,
                            AVG(CASE WHEN qi.result = 'pass' THEN 1.0 ELSE 0.0 END) as pass_rate
                        FROM quality_inspections qi
                        WHERE {where_clause}
                        GROUP BY DATE(qi.inspection_date)
                        ORDER BY inspection_date
                    """, *params)

                    trend_data = []
                    for row in rows:
                        trend_data.append({
                            "date": row['inspection_date'].isoformat(),
                            "total_inspections": row['total_inspections'],
                            "pass_count": row['pass_count'],
                            "fail_count": row['fail_count'],
                            "total_defects": row['total_defects'],
                            "pass_rate": float(row['pass_rate']) if row['pass_rate'] else 0
                        })

                    return {
                        "analysis_type": "trend",
                        "period": {
                            "start_date": request.start_date.isoformat(),
                            "end_date": request.end_date.isoformat()
                        },
                        "data": trend_data
                    }

                elif request.analysis_type == "pareto":
                    # 帕累托分析（缺陷类型分布）
                    rows = await conn.fetch(f"""
                        SELECT
                            qd.defect_type,
                            COUNT(*) as defect_count,
                            SUM(qd.quantity) as total_quantity
                        FROM quality_defects qd
                        JOIN quality_inspections qi ON qd.inspection_id = qi.id
                        WHERE {where_clause}
                        GROUP BY qd.defect_type
                        ORDER BY defect_count DESC
                    """, *params)

                    total_defects = sum(row['defect_count'] for row in rows)
                    cumulative_percentage = 0
                    pareto_data = []

                    for row in rows:
                        percentage = (row['defect_count'] / total_defects * 100) if total_defects > 0 else 0
                        cumulative_percentage += percentage

                        pareto_data.append({
                            "defect_type": row['defect_type'],
                            "defect_count": row['defect_count'],
                            "total_quantity": row['total_quantity'],
                            "percentage": percentage,
                            "cumulative_percentage": cumulative_percentage
                        })

                    return {
                        "analysis_type": "pareto",
                        "total_defects": total_defects,
                        "data": pareto_data
                    }

                else:
                    return {"error": "Unsupported analysis type"}

        except Exception as e:
            logger.error(f"Error analyzing quality trends: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "quality-service"}

@app.post("/inspections")
async def create_inspection(inspection_data: QualityInspectionCreate):
    """创建质量检验"""
    inspection_id = await QualityManager.create_inspection(inspection_data)
    return {"message": "Quality inspection created", "inspection_id": inspection_id}

@app.post("/inspections/{inspection_id}/results")
async def record_inspection_result(inspection_id: str, result_data: InspectionResultCreate):
    """记录检验结果"""
    result_data.inspection_id = inspection_id
    success = await QualityManager.record_inspection_result(result_data)
    if success:
        return {"message": "Inspection result recorded"}
    else:
        raise HTTPException(status_code=500, detail="Failed to record result")

@app.post("/analysis")
async def analyze_quality(request: QualityAnalysisRequest):
    """质量分析"""
    analysis_result = await QualityManager.analyze_quality_trends(request)
    return analysis_result

@app.post("/ai/predict")
async def predict_quality(measurement_data: QualityMeasurement):
    """AI质量预测"""
    try:
        prediction = quality_ai.predict_quality({
            'measured_value': measurement_data.measured_value,
            'target_value': measurement_data.target_value,
            'tolerance_upper': measurement_data.tolerance_upper,
            'tolerance_lower': measurement_data.tolerance_lower
        })
        return prediction
    except Exception as e:
        logger.error(f"Error in AI quality prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
