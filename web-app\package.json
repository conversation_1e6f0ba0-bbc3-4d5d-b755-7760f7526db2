{"name": "industry-ai-platform-web", "version": "1.0.0", "description": "工业智能体平台Web前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "@ant-design/charts": "^2.0.0", "axios": "^1.6.0", "swr": "^2.2.0", "socket.io-client": "^4.7.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "dayjs": "^1.11.0", "lodash": "^4.17.0", "@types/lodash": "^4.14.0", "classnames": "^2.3.0", "react-markdown": "^9.0.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "three": "^0.158.0", "@types/three": "^0.158.0", "@react-three/fiber": "^8.15.0", "@react-three/drei": "^9.88.0", "framer-motion": "^10.16.0", "react-beautiful-dnd": "^13.1.0", "@types/react-beautiful-dnd": "^13.1.0", "react-flow-renderer": "^10.3.0", "d3": "^7.8.0", "@types/d3": "^7.4.0", "echarts": "^5.4.0", "echarts-for-react": "^3.0.0", "react-webcam": "^7.1.0", "react-dropzone": "^14.2.0", "react-pdf": "^7.5.0", "react-virtualized": "^9.22.0", "@types/react-virtualized": "^9.21.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.0.0", "jest-environment-jsdom": "^29.0.0", "tailwindcss": "^3.3.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "@tailwindcss/typography": "^0.5.0", "@tailwindcss/forms": "^0.5.0", "sass": "^1.69.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}