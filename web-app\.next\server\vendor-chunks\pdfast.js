"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pdfast";
exports.ids = ["vendor-chunks/pdfast"];
exports.modules = {

/***/ "(ssr)/../node_modules/pdfast/src/helper.js":
/*!********************************************!*\
  !*** ../node_modules/pdfast/src/helper.js ***!
  \********************************************/
/***/ ((module) => {

eval("\n\nvar self = module.exports;\n\nmodule.exports.isNumber = function (x) {\n  return (typeof x === 'number');\n};\n\nmodule.exports.findMin = function (arr) {\n  if (arr.length === 0) {\n    return Infinity;\n  }\n\n  var curr = arr[0];\n  for (var i = 1; i < arr.length; i++) {\n    curr = Math.min(curr, arr[i]);\n  }\n  return curr;\n};\n\nmodule.exports.findMax = function (arr) {\n  if (arr.length === 0) {\n    return -Infinity;\n  }\n\n  var curr = arr[0];\n  for (var i = 1; i < arr.length; i++) {\n    curr = Math.max(curr, arr[i]);\n  }\n  return curr;\n};\n\nmodule.exports.findMinMulti = function (arr) {\n  var curr = self.findMin(arr[0]);\n  for (var i = 1; i < arr.length; i++) {\n    curr = Math.min(curr, self.findMin(arr[i]));\n  }\n  return curr;\n};\n\nmodule.exports.findMaxMulti = function (arr) {\n  var curr = self.findMax(arr[0]);\n  for (var i = 1; i < arr.length; i++) {\n    curr = Math.max(curr, self.findMax(arr[i]));\n  }\n  return curr;\n};\n\nmodule.exports.inside = function (min, max, x) {\n  return (min <= x) && (x <= max);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/pdfast/src/helper.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/pdfast/src/index.js":
/*!*******************************************!*\
  !*** ../node_modules/pdfast/src/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar DEFAULT_SIZE = 50;\nvar DEFAULT_WIDTH = 2;\n\nvar LN_2 = Math.log(2);\nvar self = module.exports;\n\nvar helper = __webpack_require__(/*! ./helper */ \"(ssr)/../node_modules/pdfast/src/helper.js\");\n\n// Triangle\nfunction kernel(x) {\n  return 1 - Math.abs(x);\n}\n\n/**\n * Get min and max value for the pdf, covering all arr data range while respecting options' data\n * @param arr\n * @param options\n * @returns {*}\n */\nmodule.exports.getUnifiedMinMax = function (arr, options) {\n  return self.getUnifiedMinMaxMulti([arr], options);\n};\n\nmodule.exports.getUnifiedMinMaxMulti = function (arrMulti, options) {\n  options = options || {};\n\n  var relaxMin = false;\n  var relaxMax = false;\n\n  var width = helper.isNumber(options.width) ? options.width : DEFAULT_WIDTH;\n  var size = helper.isNumber(options.size) ? options.size : DEFAULT_SIZE;\n  var min = helper.isNumber(options.min) ? options.min : (relaxMin = true, helper.findMinMulti(arrMulti));\n  var max = helper.isNumber(options.max) ? options.max : (relaxMax = true, helper.findMaxMulti(arrMulti));\n\n  var range = max - min;\n  var step = range / (size - 1);\n\n  // Relax?\n  if (relaxMin) {\n    min = min - 2 * width * step;\n  }\n  if (relaxMax) {\n    max = max + 2 * width * step;\n  }\n\n  return {\n    min: min,\n    max: max\n  };\n};\n\nmodule.exports.create = function (arr, options) {\n  options = options || {};\n\n  if (!arr || (arr.length === 0)) {\n    return [];\n  }\n\n  var size = helper.isNumber(options.size) ? options.size : DEFAULT_SIZE;\n  var width = helper.isNumber(options.width) ? options.width : DEFAULT_WIDTH;\n  var normalizedMinMax = self.getUnifiedMinMax(arr, {\n    size: size,\n    width: width,\n    min: options.min,\n    max: options.max\n  });\n\n  var min = normalizedMinMax.min;\n  var max = normalizedMinMax.max;\n\n  var range = max - min;\n  var step = range / (size - 1);\n  if (range === 0) {\n    // Special case...\n    return [{x: min, y: 1}];\n  }\n\n  // Good to go\n\n  var buckets = [];\n  for (var i = 0; i < size; i++) {\n    buckets.push({\n      x: min + i * step,\n      y: 0\n    });\n  }\n\n  var xToBucket = function (x) {\n    return Math.floor((x - min) / step);\n  };\n\n  var partialArea = generatePartialAreas(kernel, width);\n  var fullArea = partialArea[width];\n  var c = partialArea[width-1] - partialArea[width-2];\n\n  var initalValue = 0;\n  arr.forEach(function (x) {\n    var bucket = xToBucket(x);\n\n    // Totally outside?\n    if ((bucket + width < 0) || (bucket - width >= buckets.length)) {\n      return;\n    }\n\n    var start = Math.max(bucket - width, 0);\n    var mid = bucket;\n    var end = Math.min(bucket + width, buckets.length - 1);\n\n    var leftBlockCount = start - (bucket - width);\n    var rightBlockCount = (bucket + width) - end;\n    var spilledAreaLeft = partialArea[-width-1 + leftBlockCount] || 0;\n    var spilledAreaRight = partialArea[-width-1 + rightBlockCount] || 0;\n    var weight = fullArea / (fullArea - spilledAreaLeft - spilledAreaRight);\n\n    if (leftBlockCount > 0) {\n      initalValue += weight * (leftBlockCount - 1) * c;\n    }\n\n    // Add grads\n    var startGradPos = Math.max(0, bucket-width+1);\n    if (helper.inside(0, buckets.length-1, startGradPos)) {\n      buckets[startGradPos].y += weight * 1 * c;\n    }\n    if (helper.inside(0, buckets.length-1, mid + 1)) {\n      buckets[mid + 1].y -= weight * 2 * c;\n    }\n    if (helper.inside(0, buckets.length-1, end + 1)) {\n      buckets[end + 1].y += weight * 1 * c;\n    }\n  });\n\n  var accumulator = initalValue;\n  var gradAccumulator = 0;\n  var area = 0;\n  buckets.forEach(function (bucket) {\n    gradAccumulator += bucket.y;\n    accumulator += gradAccumulator;\n\n    bucket.y = accumulator;\n    area += accumulator;\n  });\n\n  // Normalize\n  if (area > 0) {\n    buckets.forEach(function (bucket) {\n      bucket.y /= area;\n    });\n  }\n\n  return buckets;\n};\n\nfunction generatePartialAreas(kernel, width) {\n  var partialAreas = {};\n\n  var accumulator = 0;\n  for (var i = -width; i <= width; i++) {\n    accumulator += kernel(i/width);\n    partialAreas[i] = accumulator;\n  }\n\n  return partialAreas;\n}\n\nmodule.exports.getExpectedValueFromPdf = function (pdf) {\n  if (!pdf || (pdf.length === 0)) {\n    return undefined;\n  }\n\n  var expected = 0;\n\n  pdf.forEach(function (obj) {\n    expected += obj.x * obj.y;\n  });\n\n  return expected;\n};\n\nmodule.exports.getXWithLeftTailArea = function (pdf, area) {\n  if (!pdf || (pdf.length === 0)) {\n    return undefined;\n  }\n\n  var accumulator = 0;\n  var last = 0;\n  for (var i = 0; i < pdf.length; i++) {\n    last = i;\n    accumulator += pdf[i].y;\n\n    if (accumulator >= area) {\n      break;\n    }\n  }\n\n  return pdf[last].x;\n};\n\nmodule.exports.getPerplexity = function (pdf) {\n  if (!pdf || (pdf.length === 0)) {\n    return undefined;\n  }\n\n  var entropy = 0;\n  pdf.forEach(function (obj) {\n    var ln = Math.log(obj.y);\n\n    if (isFinite(ln)) {\n      entropy += obj.y * ln;\n    }\n  });\n  entropy = -entropy / LN_2;\n\n  return Math.pow(2, entropy);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/pdfast/src/index.js\n");

/***/ })

};
;