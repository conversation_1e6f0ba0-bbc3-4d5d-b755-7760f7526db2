function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SketchOutlinedSvg from "@ant-design/icons-svg/es/asn/SketchOutlined";
import AntdIcon from "../components/AntdIcon";
const SketchOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SketchOutlinedSvg
}));

/**![sketch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS42IDQwNS4xbC0yMDMtMjUzLjdhNi41IDYuNSAwIDAwLTUtMi40SDMwNi40Yy0xLjkgMC0zLjguOS01IDIuNGwtMjAzIDI1My43YTYuNSA2LjUgMCAwMC4yIDguM2w0MDguNiA0NTkuNWMxLjIgMS40IDMgMi4xIDQuOCAyLjEgMS44IDAgMy41LS44IDQuOC0yLjFsNDA4LjYtNDU5LjVhNi41IDYuNSAwIDAwLjItOC4zek02NDUuMiAyMDYuNGwzNC40IDEzMy45LTEzMi41LTEzMy45aDk4LjF6bTguMiAxNzguNUgzNzAuNkw1MTIgMjQybDE0MS40IDE0Mi45ek0zNzguOCAyMDYuNGg5OC4xTDM0NC4zIDM0MC4zbDM0LjUtMTMzLjl6bS01My40IDdsLTQ0LjEgMTcxLjVoLTkzLjFsMTM3LjItMTcxLjV6TTE5NC42IDQzNC45SDI4OWwxMjUuOCAyNDcuNy0yMjAuMi0yNDcuN3pNNTEyIDc2My40TDM0NS4xIDQzNC45aDMzMy43TDUxMiA3NjMuNHptOTcuMS04MC44TDczNSA0MzQuOWg5NC40TDYwOS4xIDY4Mi42em0xMzMuNi0yOTcuN2wtNDQuMS0xNzEuNSAxMzcuMiAxNzEuNWgtOTMuMXoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(SketchOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SketchOutlined';
}
export default RefIcon;