#!/usr/bin/env python3
"""
工业智能体平台服务管理脚本
用于启动、停止和管理所有微服务
"""

import os
import sys
import subprocess
import time
import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import requests

# 服务配置
SERVICES = {
    "api-gateway": {
        "path": "services/api-gateway",
        "port": 8000,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "auth-service": {
        "path": "services/auth-service", 
        "port": 8001,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "llm-service": {
        "path": "services/llm-service",
        "port": 8002,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "agent-orchestrator": {
        "path": "services/agent-orchestrator",
        "port": 8003,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "production-planning": {
        "path": "services/production-planning",
        "port": 8004,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "maintenance-service": {
        "path": "services/maintenance-service",
        "port": 8005,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "quality-service": {
        "path": "services/quality-service",
        "port": 8006,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "supply-chain": {
        "path": "services/supply-chain",
        "port": 8007,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "knowledge-service": {
        "path": "services/knowledge-service",
        "port": 8008,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "data-ingestion": {
        "path": "services/data-ingestion",
        "port": 8009,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "monitoring-service": {
        "path": "services/monitoring-service",
        "port": 8010,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "security-service": {
        "path": "services/security-service",
        "port": 8011,
        "command": "python main.py",
        "health_endpoint": "/health"
    },
    "devops-service": {
        "path": "services/devops-service",
        "port": 8012,
        "command": "python main.py",
        "health_endpoint": "/health"
    }
}

class ServiceManager:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.processes: Dict[str, subprocess.Popen] = {}
        
    def check_port(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False
    
    def wait_for_service(self, service_name: str, timeout: int = 60) -> bool:
        """等待服务启动"""
        service_config = SERVICES[service_name]
        port = service_config["port"]
        health_endpoint = service_config["health_endpoint"]
        
        print(f"等待服务 {service_name} 启动...")
        
        for i in range(timeout):
            try:
                response = requests.get(f"http://localhost:{port}{health_endpoint}", timeout=2)
                if response.status_code == 200:
                    print(f"✓ 服务 {service_name} 已启动")
                    return True
            except:
                pass
            
            time.sleep(1)
            if i % 10 == 0 and i > 0:
                print(f"  仍在等待 {service_name}... ({i}s)")
        
        print(f"✗ 服务 {service_name} 启动超时")
        return False
    
    def start_infrastructure(self):
        """启动基础设施服务"""
        print("启动基础设施服务...")
        
        # 检查Docker是否运行
        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
        except:
            print("警告: Docker未安装或未运行，跳过基础设施启动")
            return True
        
        # 启动Docker Compose
        compose_file = self.project_root / "docker-compose.yml"
        if compose_file.exists():
            try:
                subprocess.run(
                    ["docker-compose", "up", "-d"],
                    cwd=self.project_root,
                    check=True
                )
                print("✓ 基础设施服务已启动")
                
                # 等待服务就绪
                print("等待基础设施服务就绪...")
                time.sleep(10)
                
                return True
            except subprocess.CalledProcessError as e:
                print(f"警告: 启动基础设施失败: {e}")
                return True
        else:
            print("警告: docker-compose.yml 文件不存在")
            return True
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        if service_name in self.processes and self.processes[service_name].poll() is None:
            print(f"服务 {service_name} 已在运行")
            return True
        
        service_config = SERVICES[service_name]
        service_path = self.project_root / service_config["path"]
        
        if not service_path.exists():
            print(f"错误: 服务路径不存在: {service_path}")
            return False
        
        # 检查端口是否被占用
        if self.check_port(service_config["port"]):
            print(f"警告: 端口 {service_config['port']} 已被占用")
        
        print(f"启动服务: {service_name}")
        
        try:
            # 启动服务进程
            process = subprocess.Popen(
                service_config["command"].split(),
                cwd=service_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env={**os.environ, "PYTHONPATH": str(self.project_root)}
            )
            
            self.processes[service_name] = process
            
            # 等待服务启动
            if self.wait_for_service(service_name, timeout=30):
                return True
            else:
                # 启动失败，终止进程
                process.terminate()
                return False
                
        except Exception as e:
            print(f"错误: 启动服务 {service_name} 失败: {e}")
            return False
    
    def stop_service(self, service_name: str):
        """停止单个服务"""
        if service_name in self.processes:
            process = self.processes[service_name]
            if process.poll() is None:
                print(f"停止服务: {service_name}")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            del self.processes[service_name]
    
    def start_all_services(self, exclude: List[str] = None):
        """启动所有服务"""
        exclude = exclude or []
        
        print("=== 启动工业智能体平台 ===")
        
        # 1. 启动基础设施
        if not self.start_infrastructure():
            print("基础设施启动失败，继续启动应用服务")
        
        # 2. 按依赖顺序启动服务
        service_order = [
            "auth-service",
            "security-service",
            "monitoring-service",
            "llm-service",
            "knowledge-service",
            "data-ingestion",
            "agent-orchestrator",
            "production-planning",
            "maintenance-service",
            "quality-service",
            "supply-chain",
            "devops-service",
            "api-gateway"
        ]
        
        failed_services = []
        
        for service_name in service_order:
            if service_name not in exclude:
                if not self.start_service(service_name):
                    failed_services.append(service_name)
        
        if failed_services:
            print(f"\n警告: 以下服务启动失败: {', '.join(failed_services)}")
        
        print("\n=== 服务启动完成 ===")
        self.show_status()
        
        return len(failed_services) == 0
    
    def stop_all_services(self):
        """停止所有服务"""
        print("=== 停止所有服务 ===")
        
        # 停止Python服务
        for service_name in list(self.processes.keys()):
            self.stop_service(service_name)
        
        # 停止基础设施
        compose_file = self.project_root / "docker-compose.yml"
        if compose_file.exists():
            try:
                subprocess.run(
                    ["docker-compose", "down"],
                    cwd=self.project_root,
                    check=True
                )
                print("✓ 基础设施服务已停止")
            except:
                print("警告: 停止基础设施服务失败")
        
        print("✓ 所有服务已停止")
    
    def show_status(self):
        """显示服务状态"""
        print("\n=== 服务状态 ===")
        
        # 检查应用服务
        print("\n应用服务:")
        for name, config in SERVICES.items():
            port = config["port"]
            if name in self.processes and self.processes[name].poll() is None:
                status = "运行中"
            elif self.check_port(port):
                status = "运行中 (外部)"
            else:
                status = "未运行"
            
            print(f"  {name:20} | 端口 {port:5} | {status}")
        
        print("\n访问地址:")
        print(f"  Web前端:     http://localhost:3000")
        print(f"  API网关:     http://localhost:8000")
        print(f"  API文档:     http://localhost:8000/docs")
    
    def restart_service(self, service_name: str):
        """重启服务"""
        print(f"重启服务: {service_name}")
        self.stop_service(service_name)
        time.sleep(2)
        self.start_service(service_name)

def main():
    parser = argparse.ArgumentParser(description="工业智能体平台服务管理")
    parser.add_argument("action", choices=["start", "stop", "restart", "status"],
                       help="操作类型")
    parser.add_argument("--service", "-s", help="指定服务名称")
    parser.add_argument("--exclude", "-e", nargs="+", help="排除的服务")
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    try:
        if args.action == "start":
            if args.service:
                manager.start_service(args.service)
            else:
                manager.start_all_services(exclude=args.exclude)
                
                # 保持运行状态
                print("\n按 Ctrl+C 停止所有服务")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n收到中断信号，停止所有服务...")
                    manager.stop_all_services()
        
        elif args.action == "stop":
            if args.service:
                manager.stop_service(args.service)
            else:
                manager.stop_all_services()
        
        elif args.action == "restart":
            if args.service:
                manager.restart_service(args.service)
            else:
                manager.stop_all_services()
                time.sleep(2)
                manager.start_all_services(exclude=args.exclude)
        
        elif args.action == "status":
            manager.show_status()
    
    except KeyboardInterrupt:
        print("\n收到中断信号，停止所有服务...")
        manager.stop_all_services()
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
