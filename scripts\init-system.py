#!/usr/bin/env python3
"""
工业智能体平台系统初始化脚本
用于初始化数据库、创建默认用户、配置基础数据等
"""

import asyncio
import asyncpg
import aioredis
import json
import os
import sys
from pathlib import Path
from datetime import datetime
import hashlib
import uuid
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

class SystemInitializer:
    def __init__(self):
        self.db_pool = None
        self.redis_client = None
    
    async def initialize(self):
        """初始化系统"""
        try:
            logger.info("开始初始化工业智能体平台...")
            
            # 1. 连接数据库
            await self.connect_databases()
            
            # 2. 创建数据库表结构
            await self.create_database_schema()
            
            # 3. 初始化基础数据
            await self.initialize_base_data()
            
            # 4. 创建默认用户和角色
            await self.create_default_users()
            
            # 5. 初始化配置数据
            await self.initialize_configurations()
            
            # 6. 创建示例数据
            await self.create_sample_data()
            
            logger.info("系统初始化完成！")
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            raise
        finally:
            await self.cleanup()
    
    async def connect_databases(self):
        """连接数据库"""
        logger.info("连接数据库...")
        
        # PostgreSQL连接
        self.db_pool = await asyncpg.create_pool(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "5432")),
            user=os.getenv("DB_USER", "admin"),
            password=os.getenv("DB_PASSWORD", "admin123"),
            database=os.getenv("DB_NAME", "industry_platform"),
            min_size=1,
            max_size=5
        )
        
        # Redis连接
        self.redis_client = await aioredis.from_url(
            f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
        )
        
        logger.info("数据库连接成功")
    
    async def create_database_schema(self):
        """创建数据库表结构"""
        logger.info("创建数据库表结构...")
        
        async with self.db_pool.acquire() as conn:
            # 启用UUID扩展
            await conn.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
            
            # 用户表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    department VARCHAR(100),
                    position VARCHAR(100),
                    phone VARCHAR(20),
                    is_active BOOLEAN DEFAULT true,
                    is_superuser BOOLEAN DEFAULT false,
                    last_login TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 角色表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS roles (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    role_name VARCHAR(50) UNIQUE NOT NULL,
                    description TEXT,
                    permissions JSONB,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 用户角色关联表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS user_roles (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    user_id UUID NOT NULL REFERENCES users(id),
                    role_id UUID NOT NULL REFERENCES roles(id),
                    assigned_by UUID REFERENCES users(id),
                    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT true,
                    UNIQUE(user_id, role_id)
                )
            """)
            
            # 物料表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS materials (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    material_code VARCHAR(50) UNIQUE NOT NULL,
                    material_name VARCHAR(200) NOT NULL,
                    category VARCHAR(100),
                    unit VARCHAR(20),
                    specification TEXT,
                    current_stock DECIMAL(15,3) DEFAULT 0,
                    reserved_stock DECIMAL(15,3) DEFAULT 0,
                    available_stock DECIMAL(15,3) DEFAULT 0,
                    minimum_stock DECIMAL(15,3) DEFAULT 0,
                    maximum_stock DECIMAL(15,3) DEFAULT 0,
                    reorder_point DECIMAL(15,3) DEFAULT 0,
                    safety_stock DECIMAL(15,3) DEFAULT 0,
                    unit_cost DECIMAL(12,4) DEFAULT 0,
                    supplier_id UUID,
                    location VARCHAR(100),
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 产品表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    product_code VARCHAR(50) UNIQUE NOT NULL,
                    product_name VARCHAR(200) NOT NULL,
                    category VARCHAR(100),
                    specification TEXT,
                    unit VARCHAR(20),
                    standard_cost DECIMAL(12,4),
                    selling_price DECIMAL(12,4),
                    lead_time_days INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 设备表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS equipment (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    equipment_code VARCHAR(50) UNIQUE NOT NULL,
                    equipment_name VARCHAR(200) NOT NULL,
                    equipment_type VARCHAR(100),
                    model VARCHAR(100),
                    manufacturer VARCHAR(100),
                    location VARCHAR(100),
                    installation_date DATE,
                    warranty_expiry DATE,
                    maintenance_interval_days INTEGER DEFAULT 30,
                    last_maintenance_date DATE,
                    next_maintenance_date DATE,
                    status VARCHAR(20) DEFAULT 'operational',
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 供应商表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS suppliers (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    supplier_code VARCHAR(50) UNIQUE NOT NULL,
                    supplier_name VARCHAR(200) NOT NULL,
                    contact_person VARCHAR(100),
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    city VARCHAR(100),
                    country VARCHAR(100),
                    payment_terms VARCHAR(100),
                    quality_rating DECIMAL(3,2) DEFAULT 0,
                    delivery_rating DECIMAL(3,2) DEFAULT 0,
                    price_rating DECIMAL(3,2) DEFAULT 0,
                    overall_rating DECIMAL(3,2) DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 安全日志表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS security_logs (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    event_type VARCHAR(50) NOT NULL,
                    user_id UUID REFERENCES users(id),
                    ip_address INET,
                    details JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
        
        logger.info("数据库表结构创建完成")
    
    async def initialize_base_data(self):
        """初始化基础数据"""
        logger.info("初始化基础数据...")
        
        async with self.db_pool.acquire() as conn:
            # 创建默认角色
            roles = [
                {
                    "role_name": "超级管理员",
                    "description": "系统超级管理员，拥有所有权限",
                    "permissions": {
                        "users": ["create", "read", "update", "delete"],
                        "roles": ["create", "read", "update", "delete"],
                        "production": ["create", "read", "update", "delete"],
                        "maintenance": ["create", "read", "update", "delete"],
                        "quality": ["create", "read", "update", "delete"],
                        "supply_chain": ["create", "read", "update", "delete"],
                        "security": ["create", "read", "update", "delete"],
                        "monitoring": ["create", "read", "update", "delete"],
                        "devops": ["create", "read", "update", "delete"]
                    }
                },
                {
                    "role_name": "生产管理员",
                    "description": "生产管理员，负责生产计划和调度",
                    "permissions": {
                        "production": ["create", "read", "update"],
                        "materials": ["read"],
                        "equipment": ["read"],
                        "quality": ["read"]
                    }
                },
                {
                    "role_name": "质量工程师",
                    "description": "质量工程师，负责质量检验和分析",
                    "permissions": {
                        "quality": ["create", "read", "update"],
                        "production": ["read"],
                        "materials": ["read"]
                    }
                },
                {
                    "role_name": "维护工程师",
                    "description": "维护工程师，负责设备维护",
                    "permissions": {
                        "maintenance": ["create", "read", "update"],
                        "equipment": ["read", "update"],
                        "production": ["read"]
                    }
                },
                {
                    "role_name": "采购员",
                    "description": "采购员，负责采购管理",
                    "permissions": {
                        "supply_chain": ["create", "read", "update"],
                        "materials": ["read", "update"],
                        "suppliers": ["read", "update"]
                    }
                },
                {
                    "role_name": "操作员",
                    "description": "普通操作员，只能查看基础信息",
                    "permissions": {
                        "production": ["read"],
                        "quality": ["read"],
                        "materials": ["read"],
                        "equipment": ["read"]
                    }
                }
            ]
            
            for role in roles:
                await conn.execute(
                    """
                    INSERT INTO roles (role_name, description, permissions)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (role_name) DO NOTHING
                    """,
                    role["role_name"],
                    role["description"],
                    json.dumps(role["permissions"])
                )
        
        logger.info("基础数据初始化完成")
    
    async def create_default_users(self):
        """创建默认用户"""
        logger.info("创建默认用户...")
        
        async with self.db_pool.acquire() as conn:
            # 创建超级管理员
            admin_password = self.hash_password("admin123")
            admin_id = str(uuid.uuid4())
            
            await conn.execute(
                """
                INSERT INTO users (id, username, email, password_hash, full_name, 
                                 department, position, is_superuser)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (username) DO NOTHING
                """,
                admin_id,
                "admin",
                "<EMAIL>",
                admin_password,
                "系统管理员",
                "IT部门",
                "系统管理员",
                True
            )
            
            # 分配超级管理员角色
            admin_role = await conn.fetchrow(
                "SELECT id FROM roles WHERE role_name = '超级管理员'"
            )
            
            if admin_role:
                await conn.execute(
                    """
                    INSERT INTO user_roles (user_id, role_id, assigned_by)
                    VALUES ($1, $2, $1)
                    ON CONFLICT (user_id, role_id) DO NOTHING
                    """,
                    admin_id,
                    admin_role['id']
                )
            
            # 创建示例用户
            demo_users = [
                {
                    "username": "prod_manager",
                    "email": "<EMAIL>",
                    "full_name": "张生产",
                    "department": "生产部",
                    "position": "生产经理",
                    "role": "生产管理员"
                },
                {
                    "username": "quality_engineer",
                    "email": "<EMAIL>",
                    "full_name": "李质量",
                    "department": "质量部",
                    "position": "质量工程师",
                    "role": "质量工程师"
                },
                {
                    "username": "maintenance_engineer",
                    "email": "<EMAIL>",
                    "full_name": "王维护",
                    "department": "设备部",
                    "position": "维护工程师",
                    "role": "维护工程师"
                }
            ]
            
            for user in demo_users:
                user_id = str(uuid.uuid4())
                password_hash = self.hash_password("demo123")
                
                await conn.execute(
                    """
                    INSERT INTO users (id, username, email, password_hash, full_name, 
                                     department, position)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (username) DO NOTHING
                    """,
                    user_id,
                    user["username"],
                    user["email"],
                    password_hash,
                    user["full_name"],
                    user["department"],
                    user["position"]
                )
                
                # 分配角色
                role = await conn.fetchrow(
                    "SELECT id FROM roles WHERE role_name = $1",
                    user["role"]
                )
                
                if role:
                    await conn.execute(
                        """
                        INSERT INTO user_roles (user_id, role_id, assigned_by)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (user_id, role_id) DO NOTHING
                        """,
                        user_id,
                        role['id'],
                        admin_id
                    )
        
        logger.info("默认用户创建完成")
    
    async def initialize_configurations(self):
        """初始化配置数据"""
        logger.info("初始化配置数据...")
        
        # 在Redis中设置一些基础配置
        await self.redis_client.hset("system_config", mapping={
            "company_name": "汽车零部件制造企业",
            "timezone": "Asia/Shanghai",
            "default_language": "zh-CN",
            "max_login_attempts": "5",
            "session_timeout": "3600",
            "backup_retention_days": "30"
        })
        
        logger.info("配置数据初始化完成")
    
    async def create_sample_data(self):
        """创建示例数据"""
        logger.info("创建示例数据...")
        
        async with self.db_pool.acquire() as conn:
            # 创建示例供应商
            suppliers = [
                ("SUP001", "优质钢材供应商", "张经理", "13800138001", "<EMAIL>"),
                ("SUP002", "精密零件制造商", "李经理", "13800138002", "<EMAIL>"),
                ("SUP003", "电子元件供应商", "王经理", "13800138003", "<EMAIL>")
            ]
            
            for supplier in suppliers:
                await conn.execute(
                    """
                    INSERT INTO suppliers (supplier_code, supplier_name, contact_person, 
                                         phone, email, quality_rating, delivery_rating, 
                                         price_rating, overall_rating)
                    VALUES ($1, $2, $3, $4, $5, 4.5, 4.3, 4.2, 4.3)
                    ON CONFLICT (supplier_code) DO NOTHING
                    """,
                    *supplier
                )
            
            # 创建示例物料
            materials = [
                ("MAT001", "钢板", "原材料", "kg", 1000, 100, 50, 2000),
                ("MAT002", "螺栓", "标准件", "个", 5000, 500, 200, 10000),
                ("MAT003", "轴承", "机械件", "个", 200, 20, 10, 500),
                ("MAT004", "电机", "电气件", "台", 50, 5, 2, 100)
            ]
            
            for material in materials:
                await conn.execute(
                    """
                    INSERT INTO materials (material_code, material_name, category, unit,
                                         current_stock, minimum_stock, safety_stock, maximum_stock)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (material_code) DO NOTHING
                    """,
                    *material
                )
            
            # 创建示例产品
            products = [
                ("PROD001", "汽车发动机缸体", "发动机零件", "台", 5000.00, 6000.00),
                ("PROD002", "变速箱齿轮", "传动零件", "个", 800.00, 1000.00),
                ("PROD003", "制动盘", "制动系统", "个", 300.00, 400.00)
            ]
            
            for product in products:
                await conn.execute(
                    """
                    INSERT INTO products (product_code, product_name, category, unit,
                                        standard_cost, selling_price)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (product_code) DO NOTHING
                    """,
                    *product
                )
            
            # 创建示例设备
            equipment_list = [
                ("EQP001", "数控机床01", "加工设备", "CNC-1000", "哈斯", "车间A"),
                ("EQP002", "冲压机02", "成型设备", "PRESS-500", "济南二机床", "车间B"),
                ("EQP003", "焊接机器人03", "焊接设备", "ROBOT-W200", "库卡", "车间C"),
                ("EQP004", "检测设备04", "质检设备", "CMM-300", "海克斯康", "质检室")
            ]
            
            for equipment in equipment_list:
                await conn.execute(
                    """
                    INSERT INTO equipment (equipment_code, equipment_name, equipment_type,
                                         model, manufacturer, location, installation_date,
                                         maintenance_interval_days, status)
                    VALUES ($1, $2, $3, $4, $5, $6, CURRENT_DATE - INTERVAL '1 year',
                            30, 'operational')
                    ON CONFLICT (equipment_code) DO NOTHING
                    """,
                    *equipment
                )
        
        logger.info("示例数据创建完成")
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = "industry_platform_salt"
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    async def cleanup(self):
        """清理资源"""
        if self.db_pool:
            await self.db_pool.close()
        if self.redis_client:
            await self.redis_client.close()

async def main():
    """主函数"""
    initializer = SystemInitializer()
    await initializer.initialize()

if __name__ == "__main__":
    asyncio.run(main())
