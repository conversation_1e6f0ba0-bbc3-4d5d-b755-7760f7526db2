"""
数据预处理管道
负责数据清洗、验证、转换和标准化
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from pydantic import BaseModel, ValidationError
import re
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class DataProcessor(ABC):
    """数据处理器基类"""
    
    @abstractmethod
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理数据"""
        pass

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_timestamp(timestamp: Any) -> Optional[datetime]:
        """验证时间戳"""
        if isinstance(timestamp, datetime):
            return timestamp
        
        if isinstance(timestamp, str):
            try:
                # 尝试多种时间格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S.%f",
                    "%Y-%m-%dT%H:%M:%SZ",
                    "%Y-%m-%dT%H:%M:%S.%fZ",
                    "%Y/%m/%d %H:%M:%S",
                    "%d/%m/%Y %H:%M:%S"
                ]
                
                for fmt in formats:
                    try:
                        return datetime.strptime(timestamp, fmt)
                    except ValueError:
                        continue
                
                # 尝试ISO格式
                return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                
            except Exception:
                logger.warning(f"Invalid timestamp format: {timestamp}")
                return None
        
        if isinstance(timestamp, (int, float)):
            try:
                # 假设是Unix时间戳
                if timestamp > 1e10:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                return datetime.fromtimestamp(timestamp)
            except Exception:
                logger.warning(f"Invalid timestamp value: {timestamp}")
                return None
        
        return None
    
    @staticmethod
    def validate_numeric_value(value: Any) -> Optional[float]:
        """验证数值"""
        if value is None:
            return None
        
        if isinstance(value, (int, float)):
            if np.isnan(value) or np.isinf(value):
                return None
            return float(value)
        
        if isinstance(value, str):
            # 清理字符串
            value = value.strip().replace(',', '')
            
            # 尝试转换为数值
            try:
                return float(value)
            except ValueError:
                # 尝试提取数字
                match = re.search(r'-?\d+\.?\d*', value)
                if match:
                    try:
                        return float(match.group())
                    except ValueError:
                        pass
        
        return None
    
    @staticmethod
    def validate_range(value: float, min_val: float = None, max_val: float = None) -> bool:
        """验证数值范围"""
        if value is None:
            return False
        
        if min_val is not None and value < min_val:
            return False
        
        if max_val is not None and value > max_val:
            return False
        
        return True

class DataCleaner(DataProcessor):
    """数据清洗器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
    
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """清洗数据"""
        cleaned_data = []
        
        for record in data:
            cleaned_record = await self.clean_record(record)
            if cleaned_record:
                cleaned_data.append(cleaned_record)
        
        return cleaned_data
    
    async def clean_record(self, record: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """清洗单条记录"""
        try:
            cleaned = record.copy()
            
            # 清洗时间戳
            if 'timestamp' in cleaned:
                timestamp = DataValidator.validate_timestamp(cleaned['timestamp'])
                if timestamp:
                    cleaned['timestamp'] = timestamp
                else:
                    # 使用当前时间作为默认值
                    cleaned['timestamp'] = datetime.utcnow()
                    cleaned['_timestamp_corrected'] = True
            
            # 清洗数值字段
            numeric_fields = ['value', 'temperature', 'pressure', 'flow_rate', 'speed', 'power']
            for field in numeric_fields:
                if field in cleaned:
                    value = DataValidator.validate_numeric_value(cleaned[field])
                    if value is not None:
                        cleaned[field] = value
                    else:
                        # 移除无效数值
                        cleaned.pop(field, None)
                        cleaned[f'_{field}_invalid'] = True
            
            # 清洗字符串字段
            string_fields = ['sensor_id', 'equipment_id', 'status', 'unit']
            for field in string_fields:
                if field in cleaned and isinstance(cleaned[field], str):
                    # 去除前后空格，转换为小写
                    cleaned[field] = cleaned[field].strip()
                    if not cleaned[field]:  # 空字符串
                        cleaned.pop(field, None)
            
            # 标准化状态值
            if 'status' in cleaned:
                cleaned['status'] = self.normalize_status(cleaned['status'])
            
            # 标准化质量等级
            if 'quality' in cleaned:
                cleaned['quality'] = self.normalize_quality(cleaned['quality'])
            
            return cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning record: {e}")
            return None
    
    def normalize_status(self, status: str) -> str:
        """标准化状态值"""
        if not isinstance(status, str):
            return 'unknown'
        
        status = status.lower().strip()
        
        # 状态映射
        status_mapping = {
            'run': 'running',
            'running': 'running',
            'start': 'running',
            'on': 'running',
            'stop': 'stopped',
            'stopped': 'stopped',
            'off': 'stopped',
            'idle': 'idle',
            'standby': 'idle',
            'wait': 'idle',
            'error': 'error',
            'fault': 'error',
            'alarm': 'error',
            'maintenance': 'maintenance',
            'repair': 'maintenance',
            'service': 'maintenance'
        }
        
        return status_mapping.get(status, status)
    
    def normalize_quality(self, quality: str) -> str:
        """标准化质量等级"""
        if not isinstance(quality, str):
            return 'unknown'
        
        quality = quality.lower().strip()
        
        quality_mapping = {
            'good': 'good',
            'ok': 'good',
            'normal': 'good',
            'valid': 'good',
            'bad': 'bad',
            'error': 'bad',
            'invalid': 'bad',
            'fault': 'bad',
            'uncertain': 'uncertain',
            'unknown': 'uncertain',
            'questionable': 'uncertain'
        }
        
        return quality_mapping.get(quality, 'uncertain')

class OutlierDetector(DataProcessor):
    """异常值检测器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.z_threshold = self.config.get('z_threshold', 3.0)
        self.iqr_factor = self.config.get('iqr_factor', 1.5)
    
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检测异常值"""
        if len(data) < 10:  # 数据量太少，跳过异常检测
            return data
        
        # 按传感器分组检测
        sensor_groups = {}
        for record in data:
            sensor_id = record.get('sensor_id', 'unknown')
            if sensor_id not in sensor_groups:
                sensor_groups[sensor_id] = []
            sensor_groups[sensor_id].append(record)
        
        processed_data = []
        for sensor_id, records in sensor_groups.items():
            processed_records = await self.detect_outliers_for_sensor(records)
            processed_data.extend(processed_records)
        
        return processed_data
    
    async def detect_outliers_for_sensor(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为单个传感器检测异常值"""
        if len(records) < 5:
            return records
        
        # 提取数值
        values = []
        for record in records:
            if 'value' in record and isinstance(record['value'], (int, float)):
                values.append(record['value'])
        
        if len(values) < 5:
            return records
        
        values = np.array(values)
        
        # Z-score方法检测异常值
        z_scores = np.abs((values - np.mean(values)) / np.std(values))
        z_outliers = z_scores > self.z_threshold
        
        # IQR方法检测异常值
        q1, q3 = np.percentile(values, [25, 75])
        iqr = q3 - q1
        lower_bound = q1 - self.iqr_factor * iqr
        upper_bound = q3 + self.iqr_factor * iqr
        iqr_outliers = (values < lower_bound) | (values > upper_bound)
        
        # 标记异常值
        processed_records = []
        value_index = 0
        
        for record in records:
            if 'value' in record and isinstance(record['value'], (int, float)):
                record = record.copy()
                
                if z_outliers[value_index] or iqr_outliers[value_index]:
                    record['_is_outlier'] = True
                    record['_outlier_z_score'] = float(z_scores[value_index])
                    record['quality'] = 'uncertain'
                
                value_index += 1
            
            processed_records.append(record)
        
        return processed_records

class DataEnricher(DataProcessor):
    """数据增强器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
    
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """增强数据"""
        enriched_data = []
        
        for record in data:
            enriched_record = await self.enrich_record(record)
            enriched_data.append(enriched_record)
        
        return enriched_data
    
    async def enrich_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """增强单条记录"""
        enriched = record.copy()
        
        # 添加处理时间戳
        enriched['processed_at'] = datetime.utcnow()
        
        # 计算衍生指标
        if 'value' in enriched and isinstance(enriched['value'], (int, float)):
            # 添加数值范围分类
            enriched['value_range'] = self.categorize_value(enriched['value'])
        
        # 添加时间相关信息
        if 'timestamp' in enriched:
            timestamp = enriched['timestamp']
            if isinstance(timestamp, datetime):
                enriched['hour_of_day'] = timestamp.hour
                enriched['day_of_week'] = timestamp.weekday()
                enriched['is_weekend'] = timestamp.weekday() >= 5
                enriched['shift'] = self.determine_shift(timestamp.hour)
        
        # 添加设备相关信息
        if 'equipment_id' in enriched:
            enriched['equipment_type'] = self.get_equipment_type(enriched['equipment_id'])
        
        # 添加位置信息
        if 'sensor_id' in enriched:
            location_info = self.get_location_info(enriched['sensor_id'])
            enriched.update(location_info)
        
        return enriched
    
    def categorize_value(self, value: float) -> str:
        """数值范围分类"""
        if value < 0:
            return 'negative'
        elif value == 0:
            return 'zero'
        elif value < 10:
            return 'low'
        elif value < 100:
            return 'medium'
        elif value < 1000:
            return 'high'
        else:
            return 'very_high'
    
    def determine_shift(self, hour: int) -> str:
        """确定班次"""
        if 6 <= hour < 14:
            return 'day'
        elif 14 <= hour < 22:
            return 'evening'
        else:
            return 'night'
    
    def get_equipment_type(self, equipment_id: str) -> str:
        """获取设备类型"""
        # 简单的设备类型推断
        equipment_id = equipment_id.lower()
        
        if 'cnc' in equipment_id or 'mill' in equipment_id:
            return 'cnc_machine'
        elif 'robot' in equipment_id or 'arm' in equipment_id:
            return 'robot'
        elif 'conveyor' in equipment_id or 'belt' in equipment_id:
            return 'conveyor'
        elif 'press' in equipment_id:
            return 'press'
        elif 'weld' in equipment_id:
            return 'welding'
        elif 'paint' in equipment_id:
            return 'painting'
        elif 'test' in equipment_id or 'inspect' in equipment_id:
            return 'testing'
        else:
            return 'unknown'
    
    def get_location_info(self, sensor_id: str) -> Dict[str, str]:
        """获取位置信息"""
        # 简单的位置信息推断
        sensor_id = sensor_id.lower()
        
        location_info = {}
        
        if 'line1' in sensor_id:
            location_info['production_line'] = 'line_1'
        elif 'line2' in sensor_id:
            location_info['production_line'] = 'line_2'
        elif 'line3' in sensor_id:
            location_info['production_line'] = 'line_3'
        
        if 'station' in sensor_id:
            # 提取工站编号
            match = re.search(r'station(\d+)', sensor_id)
            if match:
                location_info['station'] = f"station_{match.group(1)}"
        
        if 'zone' in sensor_id:
            # 提取区域编号
            match = re.search(r'zone([a-z]|\d+)', sensor_id)
            if match:
                location_info['zone'] = f"zone_{match.group(1)}"
        
        return location_info

class DataAggregator(DataProcessor):
    """数据聚合器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.aggregation_window = self.config.get('window_seconds', 60)
    
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """聚合数据"""
        if len(data) <= 1:
            return data
        
        # 按传感器和时间窗口分组
        groups = {}
        
        for record in data:
            if 'timestamp' not in record or 'sensor_id' not in record:
                continue
            
            timestamp = record['timestamp']
            if not isinstance(timestamp, datetime):
                continue
            
            sensor_id = record['sensor_id']
            
            # 计算时间窗口
            window_start = timestamp.replace(second=0, microsecond=0)
            window_start = window_start.replace(
                minute=(window_start.minute // (self.aggregation_window // 60)) * (self.aggregation_window // 60)
            )
            
            group_key = (sensor_id, window_start)
            
            if group_key not in groups:
                groups[group_key] = []
            
            groups[group_key].append(record)
        
        # 聚合每个组
        aggregated_data = []
        for (sensor_id, window_start), records in groups.items():
            if len(records) > 1:
                aggregated_record = self.aggregate_records(records, window_start)
                if aggregated_record:
                    aggregated_data.append(aggregated_record)
            else:
                aggregated_data.extend(records)
        
        return aggregated_data
    
    def aggregate_records(self, records: List[Dict[str, Any]], window_start: datetime) -> Optional[Dict[str, Any]]:
        """聚合记录"""
        if not records:
            return None
        
        # 提取数值
        values = []
        for record in records:
            if 'value' in record and isinstance(record['value'], (int, float)):
                values.append(record['value'])
        
        if not values:
            return records[0]  # 返回第一条记录
        
        # 计算聚合统计
        aggregated = {
            'sensor_id': records[0]['sensor_id'],
            'timestamp': window_start,
            'value': np.mean(values),
            'value_min': np.min(values),
            'value_max': np.max(values),
            'value_std': np.std(values),
            'value_count': len(values),
            'aggregation_window': self.aggregation_window,
            'is_aggregated': True
        }
        
        # 复制其他字段
        for key in ['source_id', 'unit', 'equipment_id', 'production_line']:
            if key in records[0]:
                aggregated[key] = records[0][key]
        
        # 质量评估
        good_count = sum(1 for r in records if r.get('quality') == 'good')
        if good_count / len(records) >= 0.8:
            aggregated['quality'] = 'good'
        elif good_count / len(records) >= 0.5:
            aggregated['quality'] = 'uncertain'
        else:
            aggregated['quality'] = 'bad'
        
        return aggregated

class DataProcessingPipeline:
    """数据处理管道"""
    
    def __init__(self, processors: List[DataProcessor] = None):
        self.processors = processors or [
            DataCleaner(),
            OutlierDetector(),
            DataEnricher(),
            # DataAggregator()  # 可选的聚合步骤
        ]
    
    async def process(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行完整的数据处理管道"""
        if not data:
            return data
        
        processed_data = data
        
        for processor in self.processors:
            try:
                processed_data = await processor.process(processed_data)
                logger.debug(f"Processed {len(processed_data)} records with {processor.__class__.__name__}")
            except Exception as e:
                logger.error(f"Error in {processor.__class__.__name__}: {e}")
                # 继续处理，不中断管道
        
        return processed_data
    
    def add_processor(self, processor: DataProcessor):
        """添加处理器"""
        self.processors.append(processor)
    
    def remove_processor(self, processor_type: type):
        """移除处理器"""
        self.processors = [p for p in self.processors if not isinstance(p, processor_type)]
