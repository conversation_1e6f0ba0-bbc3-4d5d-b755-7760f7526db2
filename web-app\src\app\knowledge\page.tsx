'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Input, Space, Modal, Form, Upload, message, Tabs, Tree, List, Avatar } from 'antd';
import {
  BookOutlined,
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOutlined,
  ShareAltOutlined,
  NodeIndexOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import dayjs from 'dayjs';

const { Search } = Input;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 样式组件
const KnowledgeContainer = styled.div`
  .knowledge-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .file-icon {
    font-size: 24px;
    margin-right: 12px;
    
    &.pdf { color: #ff4757; }
    &.word { color: #4a90e2; }
    &.excel { color: #00d4aa; }
    &.text { color: #ff8c42; }
  }
  
  .knowledge-item {
    padding: 16px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--color-accent-blue);
      background: rgba(74, 144, 226, 0.05);
    }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const KnowledgePage: React.FC = () => {
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [form] = Form.useForm();

  // 模拟文档数据
  const mockDocuments = [
    {
      id: 'doc_001',
      name: '生产工艺标准操作规程',
      type: 'pdf',
      category: '工艺文档',
      size: '2.5MB',
      author: '工艺工程师',
      created_at: '2024-12-20',
      updated_at: '2024-12-25',
      downloads: 156,
      tags: ['工艺', '标准', 'SOP']
    },
    {
      id: 'doc_002',
      name: '设备维护手册',
      type: 'word',
      category: '维护文档',
      size: '1.8MB',
      author: '维护工程师',
      created_at: '2024-12-18',
      updated_at: '2024-12-22',
      downloads: 89,
      tags: ['维护', '设备', '手册']
    },
    {
      id: 'doc_003',
      name: '质量检验标准',
      type: 'excel',
      category: '质量文档',
      size: '856KB',
      author: '质量工程师',
      created_at: '2024-12-15',
      updated_at: '2024-12-20',
      downloads: 234,
      tags: ['质量', '检验', '标准']
    },
    {
      id: 'doc_004',
      name: '安全操作指南',
      type: 'pdf',
      category: '安全文档',
      size: '3.2MB',
      author: '安全工程师',
      created_at: '2024-12-10',
      updated_at: '2024-12-15',
      downloads: 312,
      tags: ['安全', '操作', '指南']
    }
  ];

  // 知识图谱数据
  const mockKnowledgeGraph = [
    {
      title: '生产工艺',
      key: 'production',
      icon: <NodeIndexOutlined />,
      children: [
        { title: '冲压工艺', key: 'stamping', icon: <BulbOutlined /> },
        { title: '焊接工艺', key: 'welding', icon: <BulbOutlined /> },
        { title: '机加工艺', key: 'machining', icon: <BulbOutlined /> },
        { title: '装配工艺', key: 'assembly', icon: <BulbOutlined /> }
      ]
    },
    {
      title: '设备管理',
      key: 'equipment',
      icon: <NodeIndexOutlined />,
      children: [
        { title: '数控机床', key: 'cnc', icon: <BulbOutlined /> },
        { title: '冲压设备', key: 'press', icon: <BulbOutlined /> },
        { title: '焊接设备', key: 'welding_eq', icon: <BulbOutlined /> },
        { title: '检测设备', key: 'inspection', icon: <BulbOutlined /> }
      ]
    },
    {
      title: '质量控制',
      key: 'quality',
      icon: <NodeIndexOutlined />,
      children: [
        { title: '检验标准', key: 'standards', icon: <BulbOutlined /> },
        { title: '测量工具', key: 'tools', icon: <BulbOutlined /> },
        { title: '缺陷分析', key: 'defects', icon: <BulbOutlined /> },
        { title: '改进措施', key: 'improvements', icon: <BulbOutlined /> }
      ]
    }
  ];

  // 智能搜索结果
  const mockSearchResults = [
    {
      title: '如何提高冲压件质量？',
      content: '冲压件质量控制需要从模具设计、材料选择、工艺参数设置等多个方面入手...',
      type: '问答',
      relevance: 95,
      source: '质量管理手册'
    },
    {
      title: '数控机床维护周期',
      content: '数控机床的维护周期应根据使用频率、加工材料、环境条件等因素确定...',
      type: '文档',
      relevance: 88,
      source: '设备维护规程'
    },
    {
      title: '焊接缺陷预防措施',
      content: '常见焊接缺陷包括气孔、夹渣、裂纹等，预防措施包括控制焊接参数...',
      type: '案例',
      relevance: 82,
      source: '工艺改进案例集'
    }
  ];

  // 文档表格列
  const documentColumns = [
    {
      title: '文档名称',
      key: 'document',
      render: (_, record: any) => {
        const getFileIcon = (type: string) => {
          const icons = {
            pdf: <FilePdfOutlined className="file-icon pdf" />,
            word: <FileWordOutlined className="file-icon word" />,
            excel: <FileExcelOutlined className="file-icon excel" />,
            text: <FileTextOutlined className="file-icon text" />
          };
          return icons[type as keyof typeof icons] || <FileTextOutlined className="file-icon text" />;
        };

        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {getFileIcon(record.type)}
            <div>
              <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{record.name}</div>
              <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
                {record.size} | {record.category}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag} color="blue" style={{ marginBottom: '2px' }}>
              {tag}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author'
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (date: string) => dayjs(date).format('MM-DD')
    },
    {
      title: '下载次数',
      dataIndex: 'downloads',
      key: 'downloads',
      render: (count: number) => (
        <span style={{ color: 'var(--color-accent-blue)', fontWeight: 500 }}>
          {count}
        </span>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button size="small" icon={<EyeOutlined />} type="primary">
            查看
          </Button>
          <Button size="small" icon={<DownloadOutlined />}>
            下载
          </Button>
          <Button size="small" icon={<ShareAltOutlined />}>
            分享
          </Button>
        </Space>
      )
    }
  ];

  // 上传文档
  const handleUploadDocument = async (values: any) => {
    try {
      // 这里调用API上传文档
      message.success('文档上传成功！');
      setUploadModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('文档上传失败');
    }
  };

  // 智能搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 这里调用智能搜索API
    console.log('搜索:', value);
  };

  return (
    <MainLayout>
      <KnowledgeContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>知识管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setUploadModalVisible(true)}
              >
                上传文档
              </Button>
              <Button icon={<ReloadOutlined />}>
                刷新
              </Button>
            </Space>
          </div>

          {/* 智能搜索 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col span={24}>
              <Card className="knowledge-card">
                <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                  <h3 style={{ color: 'var(--text-primary)', marginBottom: '8px' }}>
                    <SearchOutlined style={{ marginRight: '8px', color: 'var(--color-accent-blue)' }} />
                    智能知识搜索
                  </h3>
                  <p style={{ color: 'var(--text-secondary)' }}>
                    支持自然语言查询，快速找到相关文档、工艺、案例等知识内容
                  </p>
                </div>
                <Search
                  placeholder="请输入您要查找的内容，如：如何提高冲压件质量？"
                  allowClear
                  enterButton="智能搜索"
                  size="large"
                  onSearch={handleSearch}
                  style={{ maxWidth: '600px', margin: '0 auto', display: 'block' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 搜索结果 */}
          {searchValue && (
            <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
              <Col span={24}>
                <Card className="knowledge-card" title={`搜索结果 - "${searchValue}"`}>
                  <List
                    dataSource={mockSearchResults}
                    renderItem={(item) => (
                      <List.Item
                        actions={[
                          <Button key="view" type="link" icon={<EyeOutlined />}>查看</Button>,
                          <Button key="share" type="link" icon={<ShareAltOutlined />}>分享</Button>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={<Avatar icon={<BulbOutlined />} style={{ background: 'var(--color-accent-green)' }} />}
                          title={
                            <div>
                              <span style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{item.title}</span>
                              <Tag color="blue" style={{ marginLeft: '8px' }}>{item.type}</Tag>
                              <span style={{ fontSize: '12px', color: 'var(--color-accent-green)', marginLeft: '8px' }}>
                                相关度: {item.relevance}%
                              </span>
                            </div>
                          }
                          description={
                            <div>
                              <div style={{ color: 'var(--text-secondary)', marginBottom: '4px' }}>
                                {item.content}
                              </div>
                              <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
                                来源: {item.source}
                              </div>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* 主要内容区域 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="knowledge-card">
              <Tabs defaultActiveKey="documents">
                <TabPane tab="文档库" key="documents">
                  <Table
                    dataSource={mockDocuments}
                    columns={documentColumns}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }}
                  />
                </TabPane>
                
                <TabPane tab="知识图谱" key="graph">
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={12}>
                      <div style={{ background: 'var(--bg-secondary)', padding: '16px', borderRadius: '8px' }}>
                        <h4 style={{ color: 'var(--text-primary)', marginBottom: '16px' }}>
                          <NodeIndexOutlined style={{ marginRight: '8px', color: 'var(--color-accent-blue)' }} />
                          知识结构
                        </h4>
                        <Tree
                          showIcon
                          defaultExpandAll
                          treeData={mockKnowledgeGraph}
                          style={{ background: 'transparent' }}
                        />
                      </div>
                    </Col>
                    
                    <Col xs={24} lg={12}>
                      <div style={{ background: 'var(--bg-secondary)', padding: '16px', borderRadius: '8px' }}>
                        <h4 style={{ color: 'var(--text-primary)', marginBottom: '16px' }}>
                          <BulbOutlined style={{ marginRight: '8px', color: 'var(--color-accent-green)' }} />
                          知识关联
                        </h4>
                        <div className="knowledge-item">
                          <div style={{ fontWeight: 500, marginBottom: '8px' }}>冲压工艺 → 质量控制</div>
                          <div style={{ fontSize: '14px', color: 'var(--text-secondary)' }}>
                            冲压工艺参数直接影响产品质量，需要严格控制压力、速度、温度等关键参数
                          </div>
                        </div>
                        <div className="knowledge-item">
                          <div style={{ fontWeight: 500, marginBottom: '8px' }}>设备维护 → 生产效率</div>
                          <div style={{ fontSize: '14px', color: 'var(--text-secondary)' }}>
                            定期维护保养能够确保设备稳定运行，提高生产效率和产品质量
                          </div>
                        </div>
                        <div className="knowledge-item">
                          <div style={{ fontWeight: 500, marginBottom: '8px' }}>检验标准 → 缺陷预防</div>
                          <div style={{ fontSize: '14px', color: 'var(--text-secondary)' }}>
                            建立完善的检验标准体系，能够及早发现和预防质量缺陷
                          </div>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </TabPane>
              </Tabs>
            </Card>
          </motion.div>

          {/* 上传文档模态框 */}
          <Modal
            title="上传文档"
            open={uploadModalVisible}
            onCancel={() => {
              setUploadModalVisible(false);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            okText="上传"
            cancelText="取消"
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUploadDocument}
            >
              <Form.Item
                name="file"
                label="选择文件"
                rules={[{ required: true, message: '请选择要上传的文件' }]}
              >
                <Upload.Dragger
                  name="file"
                  multiple={false}
                  beforeUpload={() => false}
                  style={{ background: 'var(--bg-secondary)' }}
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined style={{ color: 'var(--color-accent-blue)' }} />
                  </p>
                  <p className="ant-upload-text" style={{ color: 'var(--text-primary)' }}>
                    点击或拖拽文件到此区域上传
                  </p>
                  <p className="ant-upload-hint" style={{ color: 'var(--text-secondary)' }}>
                    支持 PDF、Word、Excel、PowerPoint 等格式
                  </p>
                </Upload.Dragger>
              </Form.Item>
              
              <Form.Item
                name="name"
                label="文档名称"
                rules={[{ required: true, message: '请输入文档名称' }]}
              >
                <Input placeholder="请输入文档名称" />
              </Form.Item>
              
              <Form.Item
                name="category"
                label="文档分类"
                rules={[{ required: true, message: '请选择文档分类' }]}
              >
                <Input placeholder="请输入文档分类" />
              </Form.Item>
              
              <Form.Item
                name="tags"
                label="标签"
              >
                <Input placeholder="请输入标签，用逗号分隔" />
              </Form.Item>
              
              <Form.Item
                name="description"
                label="文档描述"
              >
                <TextArea rows={4} placeholder="请输入文档描述" />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </KnowledgeContainer>
    </MainLayout>
  );
};

export default KnowledgePage;
