"""
工业智能体平台 - LLM服务
提供大语言模型接口，支持多种LLM提供商和本地模型
"""

from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
import asyncio
import aioredis
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import openai
import anthropic
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import torch
from sentence_transformers import SentenceTransformer
import numpy as np
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
redis_client = None
local_models = {}
embedding_models = {}

class ModelProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"
    HUGGINGFACE = "huggingface"

class ModelType(str, Enum):
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    CLASSIFICATION = "classification"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global redis_client, local_models, embedding_models
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # 加载本地模型
    await load_local_models()
    
    # 加载嵌入模型
    await load_embedding_models()
    
    logger.info("LLM service initialized")
    
    yield
    
    # 清理资源
    if redis_client:
        await redis_client.close()
    
    logger.info("LLM service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="LLM服务",
    description="大语言模型服务，支持多种LLM提供商",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class ChatMessage(BaseModel):
    role: str  # system, user, assistant
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: str = "gpt-3.5-turbo"
    provider: ModelProvider = ModelProvider.OPENAI
    temperature: float = 0.7
    max_tokens: int = 1000
    stream: bool = False
    system_prompt: Optional[str] = None

class CompletionRequest(BaseModel):
    prompt: str
    model: str = "gpt-3.5-turbo"
    provider: ModelProvider = ModelProvider.OPENAI
    temperature: float = 0.7
    max_tokens: int = 1000
    stop: Optional[List[str]] = None

class EmbeddingRequest(BaseModel):
    text: Union[str, List[str]]
    model: str = "text-embedding-ada-002"
    provider: ModelProvider = ModelProvider.OPENAI

class ClassificationRequest(BaseModel):
    text: str
    labels: List[str]
    model: str = "local-classifier"
    provider: ModelProvider = ModelProvider.LOCAL

class ModelInfo(BaseModel):
    name: str
    provider: ModelProvider
    type: ModelType
    description: str
    max_tokens: int
    cost_per_token: float = 0.0
    is_available: bool = True

async def load_local_models():
    """加载本地模型"""
    global local_models
    
    try:
        # 检查是否有GPU
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {device}")
        
        # 加载轻量级模型用于演示
        model_configs = [
            {
                "name": "local-chat",
                "model_name": "microsoft/DialoGPT-medium",
                "type": "chat"
            },
            {
                "name": "local-classifier",
                "model_name": "distilbert-base-uncased",
                "type": "classification"
            }
        ]
        
        for config in model_configs:
            try:
                if config["type"] == "chat":
                    tokenizer = AutoTokenizer.from_pretrained(config["model_name"])
                    model = AutoModelForCausalLM.from_pretrained(config["model_name"])
                    
                    # 设置pad_token
                    if tokenizer.pad_token is None:
                        tokenizer.pad_token = tokenizer.eos_token
                    
                    local_models[config["name"]] = {
                        "tokenizer": tokenizer,
                        "model": model,
                        "type": config["type"],
                        "device": device
                    }
                    
                elif config["type"] == "classification":
                    classifier = pipeline(
                        "text-classification",
                        model=config["model_name"],
                        device=0 if device == "cuda" else -1
                    )
                    
                    local_models[config["name"]] = {
                        "pipeline": classifier,
                        "type": config["type"],
                        "device": device
                    }
                
                logger.info(f"Loaded local model: {config['name']}")
                
            except Exception as e:
                logger.warning(f"Failed to load model {config['name']}: {e}")
        
    except Exception as e:
        logger.error(f"Error loading local models: {e}")

async def load_embedding_models():
    """加载嵌入模型"""
    global embedding_models
    
    try:
        # 加载句子嵌入模型
        models = [
            "all-MiniLM-L6-v2",
            "all-mpnet-base-v2"
        ]
        
        for model_name in models:
            try:
                model = SentenceTransformer(model_name)
                embedding_models[model_name] = model
                logger.info(f"Loaded embedding model: {model_name}")
            except Exception as e:
                logger.warning(f"Failed to load embedding model {model_name}: {e}")
        
    except Exception as e:
        logger.error(f"Error loading embedding models: {e}")

class LLMManager:
    """LLM管理器"""
    
    @staticmethod
    async def chat_completion(request: ChatRequest) -> Dict[str, Any]:
        """聊天完成"""
        try:
            if request.provider == ModelProvider.OPENAI:
                return await LLMManager._openai_chat(request)
            elif request.provider == ModelProvider.ANTHROPIC:
                return await LLMManager._anthropic_chat(request)
            elif request.provider == ModelProvider.LOCAL:
                return await LLMManager._local_chat(request)
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported provider: {request.provider}")
        
        except Exception as e:
            logger.error(f"Chat completion error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _openai_chat(request: ChatRequest) -> Dict[str, Any]:
        """OpenAI聊天"""
        client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        if request.system_prompt:
            messages.insert(0, {"role": "system", "content": request.system_prompt})
        
        response = await client.chat.completions.create(
            model=request.model,
            messages=messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            stream=request.stream
        )
        
        if request.stream:
            # 流式响应处理
            return {"stream": True, "response": response}
        else:
            return {
                "content": response.choices[0].message.content,
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "provider": "openai"
            }
    
    @staticmethod
    async def _anthropic_chat(request: ChatRequest) -> Dict[str, Any]:
        """Anthropic聊天"""
        client = anthropic.AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        
        # 转换消息格式
        messages = []
        system_message = request.system_prompt or ""
        
        for msg in request.messages:
            if msg.role == "system":
                system_message += f"\n{msg.content}"
            else:
                messages.append({"role": msg.role, "content": msg.content})
        
        response = await client.messages.create(
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            system=system_message,
            messages=messages
        )
        
        return {
            "content": response.content[0].text,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            },
            "provider": "anthropic"
        }
    
    @staticmethod
    async def _local_chat(request: ChatRequest) -> Dict[str, Any]:
        """本地模型聊天"""
        if request.model not in local_models:
            raise HTTPException(status_code=404, detail=f"Local model {request.model} not found")
        
        model_info = local_models[request.model]
        tokenizer = model_info["tokenizer"]
        model = model_info["model"]
        
        # 构建输入文本
        conversation = ""
        for msg in request.messages:
            if msg.role == "user":
                conversation += f"User: {msg.content}\n"
            elif msg.role == "assistant":
                conversation += f"Bot: {msg.content}\n"
        
        conversation += "Bot:"
        
        # 生成响应
        inputs = tokenizer.encode(conversation, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=inputs.shape[1] + request.max_tokens,
                temperature=request.temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取新生成的部分
        generated_text = response_text[len(conversation):].strip()
        
        return {
            "content": generated_text,
            "model": request.model,
            "usage": {
                "prompt_tokens": inputs.shape[1],
                "completion_tokens": len(tokenizer.encode(generated_text)),
                "total_tokens": outputs.shape[1]
            },
            "provider": "local"
        }
    
    @staticmethod
    async def generate_embedding(request: EmbeddingRequest) -> Dict[str, Any]:
        """生成嵌入向量"""
        try:
            if request.provider == ModelProvider.OPENAI:
                return await LLMManager._openai_embedding(request)
            elif request.provider == ModelProvider.LOCAL:
                return await LLMManager._local_embedding(request)
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported provider: {request.provider}")
        
        except Exception as e:
            logger.error(f"Embedding generation error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _openai_embedding(request: EmbeddingRequest) -> Dict[str, Any]:
        """OpenAI嵌入"""
        client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        texts = request.text if isinstance(request.text, list) else [request.text]
        
        response = await client.embeddings.create(
            model=request.model,
            input=texts
        )
        
        embeddings = [data.embedding for data in response.data]
        
        return {
            "embeddings": embeddings,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "total_tokens": response.usage.total_tokens
            },
            "provider": "openai"
        }
    
    @staticmethod
    async def _local_embedding(request: EmbeddingRequest) -> Dict[str, Any]:
        """本地嵌入"""
        if request.model not in embedding_models:
            raise HTTPException(status_code=404, detail=f"Embedding model {request.model} not found")
        
        model = embedding_models[request.model]
        texts = request.text if isinstance(request.text, list) else [request.text]
        
        embeddings = model.encode(texts)
        
        return {
            "embeddings": embeddings.tolist(),
            "model": request.model,
            "usage": {
                "prompt_tokens": sum(len(text.split()) for text in texts),
                "total_tokens": sum(len(text.split()) for text in texts)
            },
            "provider": "local"
        }
    
    @staticmethod
    async def classify_text(request: ClassificationRequest) -> Dict[str, Any]:
        """文本分类"""
        try:
            if request.provider == ModelProvider.LOCAL:
                return await LLMManager._local_classification(request)
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported provider: {request.provider}")
        
        except Exception as e:
            logger.error(f"Text classification error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _local_classification(request: ClassificationRequest) -> Dict[str, Any]:
        """本地文本分类"""
        if request.model not in local_models:
            raise HTTPException(status_code=404, detail=f"Classification model {request.model} not found")
        
        model_info = local_models[request.model]
        classifier = model_info["pipeline"]
        
        # 使用预训练分类器
        results = classifier(request.text)
        
        # 如果提供了自定义标签，进行标签映射
        if request.labels:
            # 简单的相似度匹配
            best_label = request.labels[0]  # 默认第一个标签
            
            return {
                "label": best_label,
                "confidence": 0.8,  # 模拟置信度
                "all_scores": [{"label": label, "score": 0.8 if label == best_label else 0.2} for label in request.labels],
                "model": request.model,
                "provider": "local"
            }
        else:
            return {
                "label": results[0]["label"],
                "confidence": results[0]["score"],
                "all_scores": results,
                "model": request.model,
                "provider": "local"
            }

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "llm-service"}

@app.get("/models")
async def list_models():
    """获取可用模型列表"""
    models = []
    
    # OpenAI模型
    if os.getenv("OPENAI_API_KEY"):
        models.extend([
            ModelInfo(name="gpt-4", provider=ModelProvider.OPENAI, type=ModelType.CHAT, 
                     description="GPT-4 Chat Model", max_tokens=8192, cost_per_token=0.00003),
            ModelInfo(name="gpt-3.5-turbo", provider=ModelProvider.OPENAI, type=ModelType.CHAT,
                     description="GPT-3.5 Turbo Chat Model", max_tokens=4096, cost_per_token=0.000002),
            ModelInfo(name="text-embedding-ada-002", provider=ModelProvider.OPENAI, type=ModelType.EMBEDDING,
                     description="OpenAI Embedding Model", max_tokens=8191, cost_per_token=0.0000001)
        ])
    
    # Anthropic模型
    if os.getenv("ANTHROPIC_API_KEY"):
        models.extend([
            ModelInfo(name="claude-3-sonnet-20240229", provider=ModelProvider.ANTHROPIC, type=ModelType.CHAT,
                     description="Claude 3 Sonnet", max_tokens=200000, cost_per_token=0.000015),
            ModelInfo(name="claude-3-haiku-20240307", provider=ModelProvider.ANTHROPIC, type=ModelType.CHAT,
                     description="Claude 3 Haiku", max_tokens=200000, cost_per_token=0.00000025)
        ])
    
    # 本地模型
    for model_name, model_info in local_models.items():
        models.append(
            ModelInfo(name=model_name, provider=ModelProvider.LOCAL, 
                     type=ModelType.CHAT if model_info["type"] == "chat" else ModelType.CLASSIFICATION,
                     description=f"Local {model_info['type']} model", max_tokens=1024, cost_per_token=0.0)
        )
    
    # 嵌入模型
    for model_name in embedding_models.keys():
        models.append(
            ModelInfo(name=model_name, provider=ModelProvider.LOCAL, type=ModelType.EMBEDDING,
                     description=f"Local embedding model", max_tokens=512, cost_per_token=0.0)
        )
    
    return {"models": models}

@app.post("/chat/completions")
async def chat_completions(request: ChatRequest):
    """聊天完成"""
    return await LLMManager.chat_completion(request)

@app.post("/embeddings")
async def create_embeddings(request: EmbeddingRequest):
    """生成嵌入向量"""
    return await LLMManager.generate_embedding(request)

@app.post("/classify")
async def classify_text(request: ClassificationRequest):
    """文本分类"""
    return await LLMManager.classify_text(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
