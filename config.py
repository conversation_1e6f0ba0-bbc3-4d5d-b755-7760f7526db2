"""
工业智能体平台配置文件
"""

import os
from typing import Optional

class Config:
    """应用配置类"""
    
    # 基础配置
    APP_NAME = "工业智能体平台"
    VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # API配置
    API_HOST = os.getenv("API_HOST", "127.0.0.1")
    API_PORT = int(os.getenv("API_PORT", "8888"))
    
    # 大语言模型配置
    LLM_API_URL = os.getenv("LLM_API_URL", "https://api.siliconflow.cn/v1/chat/completions")
    LLM_API_KEY = os.getenv("LLM_API_KEY", "")  # 请设置环境变量
    LLM_MODEL = os.getenv("LLM_MODEL", "Qwen/QwQ-32B")
    LLM_TIMEOUT = int(os.getenv("LLM_TIMEOUT", "30"))
    LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "1000"))
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.7"))
    
    # 数据库配置
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./industrial_ai.db")
    
    # Redis配置
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "logs/app.log")
    
    # 安全配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRE_MINUTES = int(os.getenv("JWT_EXPIRE_MINUTES", "1440"))  # 24小时
    
    # CORS配置
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:3001").split(",")
    
    # 文件上传配置
    UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            ("LLM_API_KEY", cls.LLM_API_KEY),
        ]
        
        missing_configs = []
        for config_name, config_value in required_configs:
            if not config_value:
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"⚠️  警告：以下配置项未设置: {', '.join(missing_configs)}")
            print("请设置环境变量或在config.py中直接配置")
            return False
        
        return True
    
    @classmethod
    def get_llm_headers(cls) -> dict:
        """获取LLM API请求头"""
        return {
            "Authorization": f"Bearer {cls.LLM_API_KEY}",
            "Content-Type": "application/json"
        }
    
    @classmethod
    def get_llm_payload_template(cls) -> dict:
        """获取LLM API请求模板"""
        return {
            "model": cls.LLM_MODEL,
            "temperature": cls.LLM_TEMPERATURE,
            "max_tokens": cls.LLM_MAX_TOKENS
        }

# 开发环境配置
class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = "DEBUG"

# 生产环境配置
class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = "WARNING"

# 测试环境配置
class TestingConfig(Config):
    DEBUG = True
    DATABASE_URL = "sqlite:///./test.db"

# 根据环境变量选择配置
def get_config() -> Config:
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    elif env == "testing":
        return TestingConfig()
    else:
        return DevelopmentConfig()

# 全局配置实例
config = get_config()

# 验证配置
if not config.validate_config():
    print("🔧 配置验证失败，某些功能可能无法正常工作")
else:
    print("✅ 配置验证通过")
