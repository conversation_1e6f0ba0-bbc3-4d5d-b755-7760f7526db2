version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15
    container_name: industry-postgres
    environment:
      POSTGRES_DB: industry_platform
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data/schemas:/docker-entrypoint-initdb.d
    networks:
      - industry-network

  # 时序数据库
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: industry-timescaledb
    environment:
      POSTGRES_DB: timeseries_data
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
    ports:
      - "5433:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
    networks:
      - industry-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: industry-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - industry-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:6
    container_name: industry-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - industry-network

  # Kafka消息队列
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: industry-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - industry-network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: industry-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - industry-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: industry-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - industry-network

  # Kibana可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: industry-kibana
    depends_on:
      - elasticsearch
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    networks:
      - industry-network

  # 向量数据库 (Weaviate)
  weaviate:
    image: semitechnologies/weaviate:1.20.0
    container_name: industry-weaviate
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - industry-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: industry-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - industry-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: industry-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana:/etc/grafana/provisioning
    networks:
      - industry-network

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: industry-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - industry-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: industry-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - industry-network

volumes:
  postgres_data:
  timescale_data:
  redis_data:
  mongodb_data:
  kafka_data:
  elasticsearch_data:
  weaviate_data:
  prometheus_data:
  grafana_data:
  minio_data:

networks:
  industry-network:
    driver: bridge
