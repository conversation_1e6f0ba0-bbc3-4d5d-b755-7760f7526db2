"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/timeline/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ToolOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ArrowUpOutlined.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @ant-design/charts */ \"(app-pages-browser)/../node_modules/@ant-design/plots/es/components/line/index.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @ant-design/charts */ \"(app-pages-browser)/../node_modules/@ant-design/plots/es/components/gauge/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! styled-components */ \"(app-pages-browser)/../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_countup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-countup */ \"(app-pages-browser)/../node_modules/react-countup/build/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useApi */ \"(app-pages-browser)/./src/hooks/useApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction _templateObject() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  min-height: 100vh;\\n  background: var(--bg-primary);\\n\"\n    ]);\n    _templateObject = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-secondary);\\n  border-bottom: 1px solid var(--border-primary);\\n  padding: 0 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: relative;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    right: 0;\\n    height: 2px;\\n    background: linear-gradient(90deg,\\n      transparent 0%,\\n      var(--color-accent-blue) 50%,\\n      transparent 100%);\\n    opacity: 0.6;\\n  }\\n\"\n    ]);\n    _templateObject1 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-secondary);\\n  border-right: 1px solid var(--border-primary);\\n\\n  .ant-layout-sider-trigger {\\n    background: var(--bg-primary);\\n    border-top: 1px solid var(--border-primary);\\n    color: var(--text-secondary);\\n\\n    &:hover {\\n      background: var(--color-accent-blue);\\n      color: white;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-primary);\\n  padding: 24px;\\n  overflow-y: auto;\\n\"\n    ]);\n    _templateObject3 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n\\n  .logo-icon {\\n    width: 32px;\\n    height: 32px;\\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\\n    border-radius: 6px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    color: white;\\n    font-size: 16px;\\n  }\\n\"\n    ]);\n    _templateObject4 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: \",\n        \";\\n  box-shadow: 0 0 10px \",\n        \";\\n  animation: pulse 2s infinite;\\n\"\n    ]);\n    _templateObject5 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-card);\\n  border: 1px solid var(--border-primary);\\n  border-radius: 8px;\\n  box-shadow: var(--shadow-card);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 3px;\\n    background: linear-gradient(90deg,\\n      var(--color-accent-blue),\\n      var(--color-accent-green));\\n    opacity: 0;\\n    transition: opacity 0.3s ease;\\n  }\\n\\n  &:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-hover);\\n\\n    &::before {\\n      opacity: 1;\\n    }\\n  }\\n\\n  .ant-card-head {\\n    background: var(--bg-secondary);\\n    border-bottom: 1px solid var(--border-primary);\\n    color: var(--text-primary);\\n  }\\n\\n  .ant-card-body {\\n    background: var(--bg-card);\\n  }\\n\"\n    ]);\n    _templateObject6 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  text-align: center;\\n\\n  .ant-statistic-title {\\n    color: var(--text-secondary);\\n    font-size: 14px;\\n    margin-bottom: 8px;\\n  }\\n\\n  .ant-statistic-content {\\n    color: var(--text-primary);\\n    font-size: 32px;\\n    font-weight: 700;\\n  }\\n\"\n    ]);\n    _templateObject7 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  color: var(--text-primary);\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n\\n  &::before {\\n    content: '';\\n    width: 4px;\\n    height: 20px;\\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\\n    border-radius: 2px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nvar Header = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Header, Sider = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Sider, Content = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Content;\n// 工业风格样式组件\nvar IndustrialLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_templateObject());\n_c = IndustrialLayout;\nvar IndustrialHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Header)(_templateObject1());\n_c1 = IndustrialHeader;\nvar IndustrialSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Sider)(_templateObject2());\n_c2 = IndustrialSider;\nvar IndustrialContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Content)(_templateObject3());\n_c3 = IndustrialContent;\nvar Logo = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div(_templateObject4());\n_c4 = Logo;\nvar StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div(_templateObject5(), function(props) {\n    return props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)';\n}, function(props) {\n    return props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)';\n});\n_c5 = StatusIndicator;\nvar DashboardCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_templateObject6());\nvar MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(DashboardCard)(_templateObject7());\n_c6 = MetricCard;\nvar SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].h2(_templateObject8());\n_c7 = SectionTitle;\n// 模拟数据\nvar mockData = {\n    overview: {\n        totalEquipment: 156,\n        runningEquipment: 142,\n        maintenanceEquipment: 8,\n        errorEquipment: 6,\n        oeeRate: 0.85,\n        qualityRate: 0.96,\n        productionOutput: 2847,\n        energyConsumption: 1234.5\n    },\n    productionTrend: [\n        {\n            time: '00:00',\n            output: 120,\n            target: 130\n        },\n        {\n            time: '02:00',\n            output: 132,\n            target: 130\n        },\n        {\n            time: '04:00',\n            output: 101,\n            target: 130\n        },\n        {\n            time: '06:00',\n            output: 134,\n            target: 130\n        },\n        {\n            time: '08:00',\n            output: 90,\n            target: 130\n        },\n        {\n            time: '10:00',\n            output: 230,\n            target: 130\n        },\n        {\n            time: '12:00',\n            output: 210,\n            target: 130\n        },\n        {\n            time: '14:00',\n            output: 220,\n            target: 130\n        },\n        {\n            time: '16:00',\n            output: 200,\n            target: 130\n        },\n        {\n            time: '18:00',\n            output: 180,\n            target: 130\n        },\n        {\n            time: '20:00',\n            output: 160,\n            target: 130\n        },\n        {\n            time: '22:00',\n            output: 140,\n            target: 130\n        }\n    ],\n    equipmentStatus: [\n        {\n            name: 'CNC-001',\n            status: 'running',\n            utilization: 85,\n            temperature: 45\n        },\n        {\n            name: 'CNC-002',\n            status: 'running',\n            utilization: 92,\n            temperature: 48\n        },\n        {\n            name: 'Robot-001',\n            status: 'idle',\n            utilization: 0,\n            temperature: 25\n        },\n        {\n            name: 'Press-001',\n            status: 'maintenance',\n            utilization: 0,\n            temperature: 30\n        },\n        {\n            name: 'Grinder-001',\n            status: 'error',\n            utilization: 0,\n            temperature: 65\n        }\n    ],\n    recentAlerts: [\n        {\n            id: 1,\n            type: 'warning',\n            message: 'CNC-001温度异常',\n            time: '2分钟前'\n        },\n        {\n            id: 2,\n            type: 'error',\n            message: 'Grinder-001故障停机',\n            time: '15分钟前'\n        },\n        {\n            id: 3,\n            type: 'info',\n            message: 'Robot-001完成维护',\n            time: '1小时前'\n        },\n        {\n            id: 4,\n            type: 'success',\n            message: '生产线A达成日产目标',\n            time: '2小时前'\n        }\n    ],\n    agentTasks: [\n        {\n            id: 1,\n            name: '生产排产优化',\n            agent: '排产智能体',\n            status: 'running',\n            progress: 75\n        },\n        {\n            id: 2,\n            name: '设备预测维护',\n            agent: '维护智能体',\n            status: 'completed',\n            progress: 100\n        },\n        {\n            id: 3,\n            name: '质量异常分析',\n            agent: '质量智能体',\n            status: 'pending',\n            progress: 0\n        },\n        {\n            id: 4,\n            name: '供应链风险评估',\n            agent: '供应链智能体',\n            status: 'running',\n            progress: 45\n        }\n    ]\n};\nfunction HomePage() {\n    var _this = this;\n    var _dashboardData_overview, _dashboardData_overview1, _dashboardData_overview2, _dashboardData_quality_metrics, _dashboardData_quality_metrics1, _dashboardData_inventory_alerts;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), collapsed = _useState[0], setCollapsed = _useState[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // 获取仪表板数据\n    var _useDashboard = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useDashboard)(), dashboardData = _useDashboard.data, loading = _useDashboard.loading, error = _useDashboard.error, refetch = _useDashboard.refetch;\n    // 获取系统健康状态\n    var isHealthy = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useSystemHealth)().isHealthy;\n    // 根据系统健康状态确定状态指示器\n    var systemStatus = isHealthy ? 'online' : 'offline';\n    // 使用真实数据或回退到模拟数据\n    var displayData = dashboardData || {\n        overview: {\n            total_production_lines: mockData.overview.totalEquipment,\n            active_lines: mockData.overview.runningEquipment,\n            average_efficiency: mockData.overview.oeeRate * 100,\n            total_equipment: mockData.overview.totalEquipment,\n            operational_equipment: mockData.overview.runningEquipment\n        },\n        production_lines: [],\n        equipment: [],\n        quality_metrics: {\n            defect_rate: 1 - mockData.overview.qualityRate,\n            first_pass_yield: mockData.overview.qualityRate,\n            customer_complaints: 2,\n            quality_score: mockData.overview.qualityRate * 100\n        },\n        inventory_alerts: []\n    };\n    // 路径映射\n    var pathMapping = {\n        dashboard: '/',\n        agents: '/agents',\n        'agent-chat': '/agents/chat',\n        'agent-workflow': '/agents/orchestration',\n        'agent-tasks': '/agents/tasks',\n        production: '/production',\n        'production-planning': '/production',\n        'production-monitoring': '/production',\n        'production-analysis': '/production',\n        quality: '/quality',\n        'quality-inspection': '/quality',\n        'quality-analysis': '/quality',\n        'quality-standards': '/quality',\n        maintenance: '/maintenance',\n        'equipment-monitoring': '/maintenance',\n        'predictive-maintenance': '/maintenance',\n        'maintenance-planning': '/maintenance',\n        'supply-chain': '/supply-chain',\n        'inventory-management': '/supply-chain',\n        'supplier-management': '/supply-chain',\n        procurement: '/supply-chain',\n        knowledge: '/knowledge',\n        'knowledge-search': '/knowledge',\n        'knowledge-graph': '/knowledge',\n        'document-management': '/knowledge',\n        monitoring: '/monitoring'\n    };\n    // 菜单项\n    var menuItems = [\n        {\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 13\n            }, this),\n            label: '总览'\n        },\n        {\n            key: 'agents',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 13\n            }, this),\n            label: '智能体',\n            children: [\n                {\n                    key: 'agent-chat',\n                    label: '智能对话'\n                },\n                {\n                    key: 'agent-workflow',\n                    label: '工作流'\n                },\n                {\n                    key: 'agent-tasks',\n                    label: '任务管理'\n                }\n            ]\n        },\n        {\n            key: 'production',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, this),\n            label: '生产管理',\n            children: [\n                {\n                    key: 'production-planning',\n                    label: '生产计划'\n                },\n                {\n                    key: 'production-monitoring',\n                    label: '生产监控'\n                },\n                {\n                    key: 'production-analysis',\n                    label: '生产分析'\n                }\n            ]\n        },\n        {\n            key: 'quality',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 345,\n                columnNumber: 13\n            }, this),\n            label: '质量管理',\n            children: [\n                {\n                    key: 'quality-inspection',\n                    label: '质量检验'\n                },\n                {\n                    key: 'quality-analysis',\n                    label: '质量分析'\n                },\n                {\n                    key: 'quality-standards',\n                    label: '质量标准'\n                }\n            ]\n        },\n        {\n            key: 'maintenance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, this),\n            label: '设备维护',\n            children: [\n                {\n                    key: 'equipment-monitoring',\n                    label: '设备监控'\n                },\n                {\n                    key: 'predictive-maintenance',\n                    label: '预测维护'\n                },\n                {\n                    key: 'maintenance-planning',\n                    label: '维护计划'\n                }\n            ]\n        },\n        {\n            key: 'supply-chain',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 13\n            }, this),\n            label: '供应链',\n            children: [\n                {\n                    key: 'inventory-management',\n                    label: '库存管理'\n                },\n                {\n                    key: 'supplier-management',\n                    label: '供应商管理'\n                },\n                {\n                    key: 'procurement',\n                    label: '采购管理'\n                }\n            ]\n        },\n        {\n            key: 'knowledge',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 13\n            }, this),\n            label: '知识库',\n            children: [\n                {\n                    key: 'knowledge-search',\n                    label: '知识搜索'\n                },\n                {\n                    key: 'knowledge-graph',\n                    label: '知识图谱'\n                },\n                {\n                    key: 'document-management',\n                    label: '文档管理'\n                }\n            ]\n        }\n    ];\n    // 用户菜单\n    var userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 13\n            }, this),\n            label: '个人资料'\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 13\n            }, this),\n            label: '设置'\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 13\n            }, this),\n            label: '退出登录'\n        }\n    ];\n    // 生产趋势图配置\n    var productionTrendConfig = {\n        data: mockData.productionTrend,\n        xField: 'time',\n        yField: 'output',\n        seriesField: 'type',\n        smooth: true,\n        animation: {\n            appear: {\n                animation: 'path-in',\n                duration: 1000\n            }\n        }\n    };\n    // OEE仪表盘配置\n    var oeeGaugeConfig = {\n        percent: mockData.overview.oeeRate,\n        range: {\n            color: [\n                '#F4664A',\n                '#FAAD14',\n                '#30BF78'\n            ]\n        },\n        indicator: {\n            pointer: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            },\n            pin: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            }\n        },\n        statistic: {\n            content: {\n                style: {\n                    fontSize: '36px',\n                    lineHeight: '36px'\n                }\n            }\n        }\n    };\n    // 设备状态表格列\n    var equipmentColumns = [\n        {\n            title: '设备名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: function(status) {\n                var statusMap = {\n                    running: {\n                        color: 'green',\n                        text: '运行中'\n                    },\n                    idle: {\n                        color: 'orange',\n                        text: '空闲'\n                    },\n                    maintenance: {\n                        color: 'blue',\n                        text: '维护中'\n                    },\n                    error: {\n                        color: 'red',\n                        text: '故障'\n                    }\n                };\n                var statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    color: statusInfo.color,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 16\n                }, _this);\n            }\n        },\n        {\n            title: '利用率',\n            dataIndex: 'utilization',\n            key: 'utilization',\n            render: function(utilization) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    percent: utilization,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, _this);\n            }\n        },\n        {\n            title: '温度',\n            dataIndex: 'temperature',\n            key: 'temperature',\n            render: function(temperature) {\n                return \"\".concat(temperature, \"\\xb0C\");\n            }\n        }\n    ];\n    // 智能体任务表格列\n    var taskColumns = [\n        {\n            title: '任务名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '智能体',\n            dataIndex: 'agent',\n            key: 'agent'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: function(status) {\n                var statusMap = {\n                    running: {\n                        color: 'blue',\n                        text: '执行中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 56\n                        }, _this)\n                    },\n                    completed: {\n                        color: 'green',\n                        text: '已完成',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 59\n                        }, _this)\n                    },\n                    pending: {\n                        color: 'orange',\n                        text: '等待中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 58\n                        }, _this)\n                    },\n                    error: {\n                        color: 'red',\n                        text: '失败',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 52\n                        }, _this)\n                    }\n                };\n                var statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    color: statusInfo.color,\n                    icon: statusInfo.icon,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, _this);\n            }\n        },\n        {\n            title: '进度',\n            dataIndex: 'progress',\n            key: 'progress',\n            render: function(progress) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    percent: progress,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, _this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialSider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 240,\n                collapsedWidth: 80,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        style: {\n                            padding: '16px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Logo, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"logo-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.AnimatePresence, {\n                                    children: !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            width: 'auto'\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"工业智能体\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        defaultSelectedKeys: [\n                            'dashboard'\n                        ],\n                        items: menuItems,\n                        style: {\n                            border: 'none'\n                        },\n                        onClick: function(param) {\n                            var key = param.key;\n                            var targetPath = pathMapping[key];\n                            if (targetPath && targetPath !== '/') {\n                                router.push(targetPath);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: function() {\n                                            return setCollapsed(!collapsed);\n                                        },\n                                        style: {\n                                            fontSize: '16px',\n                                            width: 40,\n                                            height: 40,\n                                            color: 'var(--text-secondary)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(StatusIndicator, {\n                                                status: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)',\n                                                    fontSize: '14px'\n                                                },\n                                                children: [\n                                                    \"系统状态: \",\n                                                    systemStatus === 'online' ? '正常' : systemStatus === 'warning' ? '警告' : '离线'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                color: 'var(--text-secondary)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        marginRight: 8,\n                                                        background: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: 'var(--text-primary)'\n                                                    },\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: '24px',\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SectionTitle, {\n                                            children: \"总览仪表板\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            onClick: refetch,\n                                            loading: loading,\n                                            className: \"metal-button\",\n                                            children: \"刷新数据\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        24,\n                                        24\n                                    ],\n                                    style: {\n                                        marginBottom: 32\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"生产线总数\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview = dashboardData.overview) === null || _dashboardData_overview === void 0 ? void 0 : _dashboardData_overview.total_production_lines) || displayData.overview.total_production_lines,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-blue)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--text-muted)'\n                                                            },\n                                                            children: [\n                                                                \"运行中: \",\n                                                                (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview1 = dashboardData.overview) === null || _dashboardData_overview1 === void 0 ? void 0 : _dashboardData_overview1.active_lines) || displayData.overview.active_lines,\n                                                                \" 条\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"平均效率\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview2 = dashboardData.overview) === null || _dashboardData_overview2 === void 0 ? void 0 : _dashboardData_overview2.average_efficiency) || displayData.overview.average_efficiency,\n                                                            precision: 1,\n                                                            suffix: \"%\",\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-success)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: \"较昨日 +2.3%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"质量评分\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quality_metrics = dashboardData.quality_metrics) === null || _dashboardData_quality_metrics === void 0 ? void 0 : _dashboardData_quality_metrics.quality_score) || displayData.quality_metrics.quality_score,\n                                                            precision: 1,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-green)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: [\n                                                                \"缺陷率: \",\n                                                                (((dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quality_metrics1 = dashboardData.quality_metrics) === null || _dashboardData_quality_metrics1 === void 0 ? void 0 : _dashboardData_quality_metrics1.defect_rate) || displayData.quality_metrics.defect_rate) * 100).toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"库存预警\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_inventory_alerts = dashboardData.inventory_alerts) === null || _dashboardData_inventory_alerts === void 0 ? void 0 : _dashboardData_inventory_alerts.length) || displayData.inventory_alerts.length,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-warning)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-warning)'\n                                                            },\n                                                            children: \"需要立即处理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: 24\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 16,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"生产趋势\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/production/monitoring\",\n                                                    children: \"查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_42__[\"default\"], (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__._)({}, productionTrendConfig), void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"OEE指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_44__[\"default\"], (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__._)({}, oeeGaugeConfig), void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"设备状态\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/maintenance/equipment-monitoring\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                    dataSource: mockData.equipmentStatus,\n                                                    columns: equipmentColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"智能体任务\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/agents/tasks\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 42\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                    dataSource: mockData.agentTasks,\n                                                    columns: taskColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    style: {\n                                        marginTop: 16\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                        span: 24,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            title: \"最近告警\",\n                                            extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/alerts\",\n                                                children: \"查看全部\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 41\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__[\"default\"], {\n                                                children: mockData.recentAlerts.map(function(alert) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__[\"default\"].Item, {\n                                                        color: alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'orange' : alert.type === 'success' ? 'green' : 'blue',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                justifyContent: 'space-between'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                                    children: alert.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#999',\n                                                                        fontSize: '12px'\n                                                                    },\n                                                                    children: alert.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, alert.id, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 530,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"+aBeP5mqJK+UiI55WWmzT90u3Ew=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useDashboard,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useSystemHealth\n    ];\n});\n_c8 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"IndustrialLayout\");\n$RefreshReg$(_c1, \"IndustrialHeader\");\n$RefreshReg$(_c2, \"IndustrialSider\");\n$RefreshReg$(_c3, \"IndustrialContent\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"StatusIndicator\");\n$RefreshReg$(_c6, \"MetricCard\");\n$RefreshReg$(_c7, \"SectionTitle\");\n$RefreshReg$(_c8, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});