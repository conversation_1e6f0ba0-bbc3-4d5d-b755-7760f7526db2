{"version": 3, "sources": ["../../src/production.ts"], "sourcesContent": ["'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools = Devtools.ReactQueryDevtools\nexport const ReactQueryDevtoolsPanel = DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,4BAAAA;AAAA,EAAA,+BAAAC;AAAA;AAAA;AAEA,eAA0B;AAC1B,oBAA+B;AAExB,IAAMD,sBAA8B;AACpC,IAAMC,2BAAwC;", "names": ["ReactQueryDevtools", "ReactQueryDevtoolsPanel"]}