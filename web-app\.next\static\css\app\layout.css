/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工业风格主题色彩 */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  
  /* 工业色彩系统 */
  --primary-blue: #1890ff;
  --success-green: #52c41a;
  --warning-orange: #faad14;
  --error-red: #ff4d4f;
  --info-cyan: #13c2c2;
  
  /* 设备状态色彩 */
  --status-running: #52c41a;
  --status-idle: #faad14;
  --status-maintenance: #722ed1;
  --status-error: #ff4d4f;
  --status-offline: #8c8c8c;
  
  /* 质量等级色彩 */
  --quality-excellent: #52c41a;
  --quality-good: #73d13d;
  --quality-fair: #faad14;
  --quality-poor: #ff7a45;
  --quality-bad: #ff4d4f;
  
  /* 深色模式变量 */
  --dark-bg-primary: #141414;
  --dark-bg-secondary: #1f1f1f;
  --dark-bg-tertiary: #262626;
  --dark-text-primary: rgba(255, 255, 255, 0.85);
  --dark-text-secondary: rgba(255, 255, 255, 0.65);
  --dark-border: #434343;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* 工业仪表盘样式 */
.dashboard-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  @apply shadow-md;
  transform: translateY(-2px);
}

.dashboard-card.dark {
  @apply bg-gray-800 border-gray-700;
}

/* 状态指示器 */
.status-indicator {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-indicator.running {
  @apply bg-green-100 text-green-800;
}

.status-indicator.idle {
  @apply bg-yellow-100 text-yellow-800;
}

.status-indicator.maintenance {
  @apply bg-purple-100 text-purple-800;
}

.status-indicator.error {
  @apply bg-red-100 text-red-800;
}

.status-indicator.offline {
  @apply bg-gray-100 text-gray-800;
}

/* 数据可视化容器 */
.chart-container {
  @apply w-full h-64 md:h-80 lg:h-96;
}

.chart-container.small {
  @apply h-32 md:h-40;
}

.chart-container.large {
  @apply h-80 md:h-96 lg:h-[32rem];
}

/* 3D场景容器 */
.scene-container {
  @apply w-full h-full relative overflow-hidden rounded-lg;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 聊天界面样式 */
.chat-container {
  @apply flex flex-col h-full bg-white rounded-lg shadow-sm;
}

.chat-messages {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
}

.chat-message {
  @apply flex items-start space-x-3;
}

.chat-message.user {
  @apply flex-row-reverse space-x-reverse;
}

.chat-message.assistant {
  @apply flex-row;
}

.chat-bubble {
  @apply max-w-xs lg:max-w-md px-4 py-2 rounded-lg;
}

.chat-bubble.user {
  @apply bg-blue-500 text-white;
}

.chat-bubble.assistant {
  @apply bg-gray-100 text-gray-900;
}

.chat-input {
  @apply border-t border-gray-200 p-4;
}

/* 工作流编辑器样式 */
.workflow-canvas {
  @apply w-full h-full bg-gray-50 relative;
}

.workflow-node {
  @apply bg-white border-2 border-gray-300 rounded-lg p-4 shadow-sm cursor-pointer;
  transition: all 0.2s ease;
}

.workflow-node:hover {
  @apply border-blue-400 shadow-md;
}

.workflow-node.selected {
  @apply border-blue-500 shadow-lg;
}

.workflow-node.error {
  @apply border-red-500 bg-red-50;
}

.workflow-node.success {
  @apply border-green-500 bg-green-50;
}

/* 设备监控样式 */
.equipment-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.equipment-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow;
}

.equipment-status {
  @apply flex items-center justify-between mb-4;
}

.equipment-metrics {
  @apply space-y-3;
}

.metric-item {
  @apply flex justify-between items-center;
}

.metric-label {
  @apply text-sm text-gray-600;
}

.metric-value {
  @apply text-lg font-semibold;
}

/* 质量检测样式 */
.quality-result {
  @apply p-4 rounded-lg border;
}

.quality-result.pass {
  @apply bg-green-50 border-green-200 text-green-800;
}

.quality-result.fail {
  @apply bg-red-50 border-red-200 text-red-800;
}

.quality-result.warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

/* 响应式工具类 */
.container-responsive {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}

