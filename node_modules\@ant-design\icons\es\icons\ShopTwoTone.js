function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ShopTwoToneSvg from "@ant-design/icons-svg/es/asn/ShopTwoTone";
import AntdIcon from "../components/AntdIcon";
const ShopTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ShopTwoToneSvg
}));

/**![shop](data:image/svg+xml;base64,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) */
const RefIcon = /*#__PURE__*/React.forwardRef(ShopTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShopTwoTone';
}
export default RefIcon;