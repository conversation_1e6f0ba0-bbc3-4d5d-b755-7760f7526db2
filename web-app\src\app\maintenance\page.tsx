'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, DatePicker, message, Alert, Timeline } from 'antd';
import {
  ToolOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  AlertOutlined,
  CalendarOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Gauge, Column } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useEquipmentStatus } from '@/hooks/useApi';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

// 样式组件
const MaintenanceContainer = styled.div`
  .maintenance-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .equipment-status {
    &.operational {
      color: var(--status-success);
      background: rgba(0, 212, 170, 0.1);
      border: 1px solid rgba(0, 212, 170, 0.3);
    }
    
    &.maintenance {
      color: var(--status-warning);
      background: rgba(255, 140, 66, 0.1);
      border: 1px solid rgba(255, 140, 66, 0.3);
    }
    
    &.fault {
      color: var(--status-error);
      background: rgba(255, 71, 87, 0.1);
      border: 1px solid rgba(255, 71, 87, 0.3);
    }
    
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
  
  .health-score {
    &.excellent { color: var(--status-success); }
    &.good { color: var(--color-accent-blue); }
    &.warning { color: var(--status-warning); }
    &.critical { color: var(--status-error); }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const MaintenancePage: React.FC = () => {
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null);
  const [form] = Form.useForm();
  
  // 获取设备数据
  const { data: equipmentData, loading: equipmentLoading, refetch: refetchEquipment } = useEquipmentStatus();

  // 模拟设备数据
  const mockEquipment = [
    { 
      id: 'eq_001', 
      name: '数控机床01', 
      type: 'CNC', 
      status: 'operational', 
      utilization: 87.5,
      health_score: 92,
      last_maintenance: '2024-12-15',
      next_maintenance: '2025-01-15'
    },
    { 
      id: 'eq_002', 
      name: '冲压机02', 
      type: 'Press', 
      status: 'operational', 
      utilization: 92.3,
      health_score: 88,
      last_maintenance: '2024-12-10',
      next_maintenance: '2025-01-10'
    },
    { 
      id: 'eq_003', 
      name: '焊接机器人03', 
      type: 'Robot', 
      status: 'maintenance', 
      utilization: 0,
      health_score: 65,
      last_maintenance: '2024-12-28',
      next_maintenance: '2025-01-28'
    },
    { 
      id: 'eq_004', 
      name: '检测设备04', 
      type: 'Inspection', 
      status: 'operational', 
      utilization: 76.8,
      health_score: 95,
      last_maintenance: '2024-12-20',
      next_maintenance: '2025-01-20'
    }
  ];

  // 维护任务数据
  const mockMaintenanceTasks = [
    {
      id: 'task_001',
      equipment_name: '焊接机器人03',
      task_type: '故障维修',
      description: '焊接臂关节故障，需要更换伺服电机',
      priority: 'high',
      status: 'in_progress',
      assigned_to: '张工程师',
      scheduled_date: '2024-12-28',
      estimated_duration: 8
    },
    {
      id: 'task_002',
      equipment_name: '数控机床01',
      task_type: '预防性维护',
      description: '定期保养，更换切削液和检查刀具',
      priority: 'normal',
      status: 'scheduled',
      assigned_to: '李技师',
      scheduled_date: '2025-01-15',
      estimated_duration: 4
    },
    {
      id: 'task_003',
      equipment_name: '冲压机02',
      task_type: '预防性维护',
      description: '液压系统检查和油品更换',
      priority: 'normal',
      status: 'scheduled',
      assigned_to: '王技师',
      scheduled_date: '2025-01-10',
      estimated_duration: 6
    }
  ];

  // 设备表格列
  const equipmentColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{record.type}</div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          operational: { text: '正常运行', class: 'operational', icon: <CheckCircleOutlined /> },
          maintenance: { text: '维护中', class: 'maintenance', icon: <ToolOutlined /> },
          fault: { text: '故障', class: 'fault', icon: <WarningOutlined /> }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <span className={`equipment-status ${config.class}`}>
            {config.icon}
            {config.text}
          </span>
        );
      }
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (utilization: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Progress
            percent={utilization}
            size="small"
            strokeColor={{
              '0%': '#ff4757',
              '50%': '#ff8c42',
              '100%': '#00d4aa'
            }}
            style={{ flex: 1, maxWidth: '100px' }}
          />
          <span style={{ color: 'var(--text-primary)', fontWeight: 500, minWidth: '50px' }}>
            {utilization.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      title: '健康评分',
      dataIndex: 'health_score',
      key: 'health_score',
      render: (score: number) => {
        const getScoreClass = (score: number) => {
          if (score >= 90) return 'excellent';
          if (score >= 80) return 'good';
          if (score >= 70) return 'warning';
          return 'critical';
        };
        
        return (
          <span className={`health-score ${getScoreClass(score)}`} style={{ fontWeight: 600 }}>
            {score}分
          </span>
        );
      }
    },
    {
      title: '下次维护',
      dataIndex: 'next_maintenance',
      key: 'next_maintenance',
      render: (date: string) => {
        const isOverdue = dayjs(date).isBefore(dayjs());
        const isUpcoming = dayjs(date).diff(dayjs(), 'days') <= 7;
        
        return (
          <div>
            <div style={{ 
              color: isOverdue ? 'var(--status-error)' : isUpcoming ? 'var(--status-warning)' : 'var(--text-primary)' 
            }}>
              {dayjs(date).format('YYYY-MM-DD')}
            </div>
            {isOverdue && <div style={{ fontSize: '12px', color: 'var(--status-error)' }}>已逾期</div>}
            {isUpcoming && !isOverdue && <div style={{ fontSize: '12px', color: 'var(--status-warning)' }}>即将到期</div>}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button 
            size="small" 
            icon={<SettingOutlined />}
            onClick={() => {
              setSelectedEquipment(record.id);
              setTaskModalVisible(true);
            }}
          >
            安排维护
          </Button>
          <Button size="small" icon={<AlertOutlined />}>
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  // 维护任务表格列
  const taskColumns = [
    {
      title: '设备名称',
      dataIndex: 'equipment_name',
      key: 'equipment_name'
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (type: string) => (
        <Tag color={type === '故障维修' ? 'red' : 'blue'}>{type}</Tag>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const colors = { high: 'red', normal: 'blue', low: 'green' };
        const texts = { high: '高', normal: '普通', low: '低' };
        return <Tag color={colors[priority as keyof typeof colors]}>{texts[priority as keyof typeof texts]}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          scheduled: { text: '已安排', color: 'blue' },
          in_progress: { text: '进行中', color: 'orange' },
          completed: { text: '已完成', color: 'green' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '负责人',
      dataIndex: 'assigned_to',
      key: 'assigned_to'
    },
    {
      title: '计划时间',
      dataIndex: 'scheduled_date',
      key: 'scheduled_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '预计时长',
      dataIndex: 'estimated_duration',
      key: 'estimated_duration',
      render: (duration: number) => `${duration}小时`
    }
  ];

  // 创建维护任务
  const handleCreateTask = async (values: any) => {
    try {
      // 这里调用API创建维护任务
      message.success('维护任务创建成功！');
      setTaskModalVisible(false);
      form.resetFields();
      setSelectedEquipment(null);
    } catch (error) {
      message.error('创建维护任务失败');
    }
  };

  // 设备健康评分分布数据
  const healthScoreData = [
    { range: '90-100', count: 2, color: '#00d4aa' },
    { range: '80-89', count: 1, color: '#4a90e2' },
    { range: '70-79', count: 1, color: '#ff8c42' },
    { range: '60-69', count: 0, color: '#ff4757' }
  ];

  return (
    <MainLayout>
      <MaintenanceContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>设备维护管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setTaskModalVisible(true)}
              >
                新建维护任务
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refetchEquipment}
                loading={equipmentLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 预警信息 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            <Col span={24}>
              <Alert
                message="维护提醒"
                description="焊接机器人03正在维护中，预计今日完成。数控机床01和冲压机02即将到达维护周期。"
                type="warning"
                icon={<WarningOutlined />}
                showIcon
                closable
              />
            </Col>
          </Row>

          {/* 概览统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="maintenance-card">
                  <Statistic
                    title="设备总数"
                    value={mockEquipment.length}
                    prefix={<ToolOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="maintenance-card">
                  <Statistic
                    title="正常运行"
                    value={mockEquipment.filter(eq => eq.status === 'operational').length}
                    prefix={<CheckCircleOutlined style={{ color: 'var(--status-success)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="maintenance-card">
                  <Statistic
                    title="维护中"
                    value={mockEquipment.filter(eq => eq.status === 'maintenance').length}
                    prefix={<SettingOutlined style={{ color: 'var(--status-warning)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="maintenance-card">
                  <Statistic
                    title="平均健康评分"
                    value={mockEquipment.reduce((acc, eq) => acc + eq.health_score, 0) / mockEquipment.length}
                    precision={1}
                    prefix={<ThunderboltOutlined style={{ color: 'var(--color-accent-green)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="maintenance-card" title="设备健康评分分布" style={{ height: '400px' }}>
                  <Column
                    data={healthScoreData}
                    xField="range"
                    yField="count"
                    color={healthScoreData.map(item => item.color)}
                    columnStyle={{
                      radius: [4, 4, 0, 0]
                    }}
                  />
                </Card>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="maintenance-card" title="维护计划时间线" style={{ height: '400px' }}>
                  <Timeline
                    items={[
                      {
                        color: 'red',
                        children: (
                          <div>
                            <div style={{ fontWeight: 500 }}>焊接机器人03 - 故障维修</div>
                            <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>2024-12-28 (进行中)</div>
                          </div>
                        )
                      },
                      {
                        color: 'blue',
                        children: (
                          <div>
                            <div style={{ fontWeight: 500 }}>冲压机02 - 预防性维护</div>
                            <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>2025-01-10 (已安排)</div>
                          </div>
                        )
                      },
                      {
                        color: 'blue',
                        children: (
                          <div>
                            <div style={{ fontWeight: 500 }}>数控机床01 - 预防性维护</div>
                            <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>2025-01-15 (已安排)</div>
                          </div>
                        )
                      },
                      {
                        color: 'green',
                        children: (
                          <div>
                            <div style={{ fontWeight: 500 }}>检测设备04 - 预防性维护</div>
                            <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>2025-01-20 (已安排)</div>
                          </div>
                        )
                      }
                    ]}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 设备列表 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col span={24}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <Card className="maintenance-card" title="设备状态">
                  <Table
                    dataSource={mockEquipment}
                    columns={equipmentColumns}
                    rowKey="id"
                    pagination={false}
                    loading={equipmentLoading}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 维护任务列表 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <Card className="maintenance-card" title="维护任务">
              <Table
                dataSource={mockMaintenanceTasks}
                columns={taskColumns}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </motion.div>

          {/* 创建维护任务模态框 */}
          <Modal
            title="创建维护任务"
            open={taskModalVisible}
            onCancel={() => {
              setTaskModalVisible(false);
              setSelectedEquipment(null);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            okText="创建"
            cancelText="取消"
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreateTask}
              initialValues={{
                equipment_id: selectedEquipment
              }}
            >
              <Form.Item
                name="equipment_id"
                label="设备"
                rules={[{ required: true, message: '请选择设备' }]}
              >
                <Select placeholder="请选择设备">
                  {mockEquipment.map(eq => (
                    <Option key={eq.id} value={eq.id}>{eq.name}</Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                name="task_type"
                label="任务类型"
                rules={[{ required: true, message: '请选择任务类型' }]}
              >
                <Select placeholder="请选择任务类型">
                  <Option value="preventive">预防性维护</Option>
                  <Option value="corrective">故障维修</Option>
                  <Option value="inspection">设备检查</Option>
                  <Option value="upgrade">设备升级</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="normal">普通</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="description"
                label="任务描述"
                rules={[{ required: true, message: '请输入任务描述' }]}
              >
                <TextArea rows={4} placeholder="请详细描述维护任务内容" />
              </Form.Item>
              
              <Form.Item
                name="scheduled_date"
                label="计划时间"
                rules={[{ required: true, message: '请选择计划时间' }]}
              >
                <DatePicker 
                  showTime 
                  style={{ width: '100%' }}
                  placeholder="请选择计划时间"
                />
              </Form.Item>
              
              <Form.Item
                name="estimated_duration"
                label="预计时长(小时)"
                rules={[{ required: true, message: '请输入预计时长' }]}
              >
                <Input type="number" placeholder="请输入预计时长" />
              </Form.Item>
              
              <Form.Item
                name="assigned_to"
                label="负责人"
                rules={[{ required: true, message: '请输入负责人' }]}
              >
                <Input placeholder="请输入负责人姓名" />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </MaintenanceContainer>
    </MainLayout>
  );
};

export default MaintenancePage;
