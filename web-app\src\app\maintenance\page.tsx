'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, DatePicker, message, Alert } from 'antd';
import {
  ToolOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  AlertOutlined,
  ThunderboltOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useEquipmentStatus } from '@/hooks/useApi';
import dayjs from 'dayjs';
import CountUp from 'react-countup';

const { Option } = Select;
const { TextArea } = Input;

// 样式组件
const MaintenanceContainer = styled.div`
  .maintenance-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const MaintenancePage: React.FC = () => {
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  // 获取设备数据
  const { data: equipmentData, loading: equipmentLoading, refetch: refetchEquipment } = useEquipmentStatus();

  // 模拟设备数据
  const mockEquipment = [
    { 
      id: 'eq_001', 
      name: '数控机床01', 
      type: 'CNC', 
      status: 'operational', 
      utilization: 87.5,
      health_score: 92,
      last_maintenance: '2024-12-15',
      next_maintenance: '2025-01-15'
    },
    { 
      id: 'eq_002', 
      name: '冲压机02', 
      type: 'Press', 
      status: 'operational', 
      utilization: 92.3,
      health_score: 88,
      last_maintenance: '2024-12-10',
      next_maintenance: '2025-01-10'
    },
    { 
      id: 'eq_003', 
      name: '焊接机器人03', 
      type: 'Robot', 
      status: 'maintenance', 
      utilization: 0,
      health_score: 65,
      last_maintenance: '2024-12-28',
      next_maintenance: '2025-01-28'
    }
  ];

  // 设备表格列
  const equipmentColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{record.type}</div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = { operational: 'green', maintenance: 'orange', fault: 'red' };
        const texts = { operational: '正常运行', maintenance: '维护中', fault: '故障' };
        return <Tag color={colors[status as keyof typeof colors]}>{texts[status as keyof typeof texts]}</Tag>;
      }
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (utilization: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Progress
            percent={utilization}
            size="small"
            strokeColor={{
              '0%': '#ff4757',
              '50%': '#ff8c42',
              '100%': '#00d4aa'
            }}
            style={{ flex: 1, maxWidth: '100px' }}
          />
          <span style={{ color: 'var(--text-primary)', fontWeight: 500, minWidth: '50px' }}>
            {utilization.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      title: '健康评分',
      dataIndex: 'health_score',
      key: 'health_score',
      render: (score: number) => (
        <span style={{ 
          color: score >= 90 ? 'var(--status-success)' : score >= 70 ? 'var(--status-warning)' : 'var(--status-error)',
          fontWeight: 600 
        }}>
          {score}分
        </span>
      )
    },
    {
      title: '下次维护',
      dataIndex: 'next_maintenance',
      key: 'next_maintenance',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button 
            size="small" 
            icon={<SettingOutlined />}
            onClick={() => setTaskModalVisible(true)}
          >
            安排维护
          </Button>
          <Button size="small" icon={<AlertOutlined />}>
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  // 创建维护任务
  const handleCreateTask = async (values: any) => {
    try {
      message.success('维护任务创建成功！');
      setTaskModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建维护任务失败');
    }
  };

  return (
    <MainLayout>
      <MaintenanceContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>设备维护管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setTaskModalVisible(true)}
              >
                新建维护任务
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refetchEquipment}
                loading={equipmentLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 预警信息 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            <Col span={24}>
              <Alert
                message="维护提醒"
                description="焊接机器人03正在维护中，预计今日完成。数控机床01和冲压机02即将到达维护周期。"
                type="warning"
                icon={<WarningOutlined />}
                showIcon
                closable
              />
            </Col>
          </Row>

          {/* 概览统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="maintenance-card">
                <Statistic
                  title="设备总数"
                  value={mockEquipment.length}
                  prefix={<ToolOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                  formatter={(value) => <CountUp end={value as number} duration={2} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="maintenance-card">
                <Statistic
                  title="正常运行"
                  value={mockEquipment.filter(eq => eq.status === 'operational').length}
                  prefix={<CheckCircleOutlined style={{ color: 'var(--status-success)' }} />}
                  formatter={(value) => <CountUp end={value as number} duration={2} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="maintenance-card">
                <Statistic
                  title="维护中"
                  value={mockEquipment.filter(eq => eq.status === 'maintenance').length}
                  prefix={<SettingOutlined style={{ color: 'var(--status-warning)' }} />}
                  formatter={(value) => <CountUp end={value as number} duration={2} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="maintenance-card">
                <Statistic
                  title="平均健康评分"
                  value={mockEquipment.reduce((acc, eq) => acc + eq.health_score, 0) / mockEquipment.length}
                  precision={1}
                  prefix={<ThunderboltOutlined style={{ color: 'var(--color-accent-green)' }} />}
                  formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                />
              </Card>
            </Col>
          </Row>

          {/* 设备列表 */}
          <Card className="maintenance-card" title="设备状态">
            <Table
              dataSource={mockEquipment}
              columns={equipmentColumns}
              rowKey="id"
              pagination={false}
              loading={equipmentLoading}
            />
          </Card>

          {/* 创建维护任务模态框 */}
          <Modal
            title="创建维护任务"
            open={taskModalVisible}
            onCancel={() => {
              setTaskModalVisible(false);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            okText="创建"
            cancelText="取消"
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreateTask}
            >
              <Form.Item
                name="equipment_id"
                label="设备"
                rules={[{ required: true, message: '请选择设备' }]}
              >
                <Select placeholder="请选择设备">
                  {mockEquipment.map(eq => (
                    <Option key={eq.id} value={eq.id}>{eq.name}</Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                name="task_type"
                label="任务类型"
                rules={[{ required: true, message: '请选择任务类型' }]}
              >
                <Select placeholder="请选择任务类型">
                  <Option value="preventive">预防性维护</Option>
                  <Option value="corrective">故障维修</Option>
                  <Option value="inspection">设备检查</Option>
                  <Option value="upgrade">设备升级</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="description"
                label="任务描述"
                rules={[{ required: true, message: '请输入任务描述' }]}
              >
                <TextArea rows={4} placeholder="请详细描述维护任务内容" />
              </Form.Item>
              
              <Form.Item
                name="scheduled_date"
                label="计划时间"
                rules={[{ required: true, message: '请选择计划时间' }]}
              >
                <DatePicker 
                  showTime 
                  style={{ width: '100%' }}
                  placeholder="请选择计划时间"
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </MaintenanceContainer>
    </MainLayout>
  );
};

export default MaintenancePage;
