'use client';

import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Alert, Tabs, Badge } from 'antd';
import {
  MonitorOutlined,
  ServerOutlined,
  DatabaseOutlined,
  CloudOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  BellOutlined,
  LineChartOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Line, Gauge, Area } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useSystemHealth } from '@/hooks/useApi';
import dayjs from 'dayjs';
import CountUp from 'react-countup';

const { TabPane } = Tabs;

// 样式组件
const MonitoringContainer = styled.div`
  .monitoring-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .service-status {
    &.healthy {
      color: var(--status-success);
      background: rgba(0, 212, 170, 0.1);
      border: 1px solid rgba(0, 212, 170, 0.3);
    }
    
    &.warning {
      color: var(--status-warning);
      background: rgba(255, 140, 66, 0.1);
      border: 1px solid rgba(255, 140, 66, 0.3);
    }
    
    &.critical {
      color: var(--status-error);
      background: rgba(255, 71, 87, 0.1);
      border: 1px solid rgba(255, 71, 87, 0.3);
    }
    
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
  
  .metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
  }
  
  .metric-trend {
    &.up { color: var(--status-success); }
    &.down { color: var(--status-error); }
    &.stable { color: var(--text-muted); }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const MonitoringPage: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { isHealthy, lastCheck } = useSystemHealth();

  // 模拟系统指标数据
  const [systemMetrics, setSystemMetrics] = useState({
    cpu_usage: 45.2,
    memory_usage: 68.5,
    disk_usage: 32.1,
    network_io: 125.6,
    active_connections: 1247,
    response_time: 156,
    throughput: 2340,
    error_rate: 0.12
  });

  // 服务状态数据
  const mockServices = [
    { 
      name: 'API Gateway', 
      status: 'healthy', 
      uptime: '99.98%', 
      response_time: 45, 
      last_check: '2024-12-28 12:00:00',
      port: 8000
    },
    { 
      name: 'Auth Service', 
      status: 'healthy', 
      uptime: '99.95%', 
      response_time: 32, 
      last_check: '2024-12-28 12:00:00',
      port: 8001
    },
    { 
      name: 'LLM Service', 
      status: 'healthy', 
      uptime: '99.92%', 
      response_time: 1250, 
      last_check: '2024-12-28 12:00:00',
      port: 8002
    },
    { 
      name: 'Production Service', 
      status: 'warning', 
      uptime: '98.85%', 
      response_time: 89, 
      last_check: '2024-12-28 12:00:00',
      port: 8004
    },
    { 
      name: 'Quality Service', 
      status: 'healthy', 
      uptime: '99.99%', 
      response_time: 67, 
      last_check: '2024-12-28 12:00:00',
      port: 8006
    },
    { 
      name: 'PostgreSQL', 
      status: 'healthy', 
      uptime: '99.99%', 
      response_time: 12, 
      last_check: '2024-12-28 12:00:00',
      port: 5432
    },
    { 
      name: 'Redis', 
      status: 'healthy', 
      uptime: '100%', 
      response_time: 3, 
      last_check: '2024-12-28 12:00:00',
      port: 6379
    },
    { 
      name: 'MongoDB', 
      status: 'healthy', 
      uptime: '99.97%', 
      response_time: 18, 
      last_check: '2024-12-28 12:00:00',
      port: 27017
    }
  ];

  // 告警数据
  const mockAlerts = [
    {
      id: 'alert_001',
      level: 'warning',
      service: 'Production Service',
      message: '响应时间超过阈值',
      timestamp: '2024-12-28 11:45:00',
      status: 'active'
    },
    {
      id: 'alert_002',
      level: 'info',
      service: 'System',
      message: '内存使用率达到70%',
      timestamp: '2024-12-28 11:30:00',
      status: 'resolved'
    },
    {
      id: 'alert_003',
      level: 'critical',
      service: 'Database',
      message: '连接池接近满载',
      timestamp: '2024-12-28 10:15:00',
      status: 'resolved'
    }
  ];

  // CPU使用率趋势数据
  const cpuTrendData = [
    { time: '11:00', cpu: 42.1 },
    { time: '11:15', cpu: 45.3 },
    { time: '11:30', cpu: 48.7 },
    { time: '11:45', cpu: 46.2 },
    { time: '12:00', cpu: 45.2 }
  ];

  // 内存使用率趋势数据
  const memoryTrendData = [
    { time: '11:00', memory: 65.2 },
    { time: '11:15', memory: 67.1 },
    { time: '11:30', memory: 69.8 },
    { time: '11:45', memory: 68.9 },
    { time: '12:00', memory: 68.5 }
  ];

  // 服务表格列
  const serviceColumns = [
    {
      title: '服务名称',
      key: 'service',
      render: (_, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            端口: {record.port}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          healthy: { text: '健康', class: 'healthy', icon: <CheckCircleOutlined /> },
          warning: { text: '警告', class: 'warning', icon: <WarningOutlined /> },
          critical: { text: '严重', class: 'critical', icon: <ExclamationCircleOutlined /> }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <span className={`service-status ${config.class}`}>
            {config.icon}
            {config.text}
          </span>
        );
      }
    },
    {
      title: '可用性',
      dataIndex: 'uptime',
      key: 'uptime',
      render: (uptime: string) => (
        <span style={{ color: 'var(--status-success)', fontWeight: 500 }}>{uptime}</span>
      )
    },
    {
      title: '响应时间',
      dataIndex: 'response_time',
      key: 'response_time',
      render: (time: number) => (
        <span style={{ 
          color: time > 1000 ? 'var(--status-error)' : time > 100 ? 'var(--status-warning)' : 'var(--status-success)',
          fontWeight: 500 
        }}>
          {time}ms
        </span>
      )
    },
    {
      title: '最后检查',
      dataIndex: 'last_check',
      key: 'last_check',
      render: (time: string) => dayjs(time).format('HH:mm:ss')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button size="small" icon={<LineChartOutlined />}>
            详情
          </Button>
          <Button size="small" icon={<SettingOutlined />}>
            配置
          </Button>
        </Space>
      )
    }
  ];

  // 告警表格列
  const alertColumns = [
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => {
        const colors = {
          critical: 'red',
          warning: 'orange',
          info: 'blue'
        };
        const texts = {
          critical: '严重',
          warning: '警告',
          info: '信息'
        };
        return <Tag color={colors[level as keyof typeof colors]}>{texts[level as keyof typeof texts]}</Tag>;
      }
    },
    {
      title: '服务',
      dataIndex: 'service',
      key: 'service'
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm')
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = { active: 'red', resolved: 'green' };
        const texts = { active: '活跃', resolved: '已解决' };
        return <Tag color={colors[status as keyof typeof colors]}>{texts[status as keyof typeof texts]}</Tag>;
      }
    }
  ];

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    // 模拟数据刷新
    setTimeout(() => {
      setSystemMetrics({
        ...systemMetrics,
        cpu_usage: systemMetrics.cpu_usage + (Math.random() - 0.5) * 10,
        memory_usage: systemMetrics.memory_usage + (Math.random() - 0.5) * 5,
        response_time: systemMetrics.response_time + (Math.random() - 0.5) * 50
      });
      setRefreshing(false);
    }, 1000);
  };

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemMetrics(prev => ({
        ...prev,
        cpu_usage: Math.max(0, Math.min(100, prev.cpu_usage + (Math.random() - 0.5) * 5)),
        memory_usage: Math.max(0, Math.min(100, prev.memory_usage + (Math.random() - 0.5) * 3)),
        network_io: Math.max(0, prev.network_io + (Math.random() - 0.5) * 20),
        active_connections: Math.max(0, prev.active_connections + Math.floor((Math.random() - 0.5) * 100))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <MainLayout>
      <MonitoringContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>系统监控</SectionTitle>
            <Space>
              <Badge count={mockAlerts.filter(alert => alert.status === 'active').length}>
                <Button icon={<BellOutlined />}>
                  告警中心
                </Button>
              </Badge>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 系统状态概览 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            <Col span={24}>
              <Alert
                message="系统状态"
                description={`系统运行正常，最后检查时间: ${dayjs(lastCheck).format('YYYY-MM-DD HH:mm:ss')}。Production Service响应时间略高，建议关注。`}
                type={isHealthy ? "success" : "warning"}
                icon={isHealthy ? <CheckCircleOutlined /> : <WarningOutlined />}
                showIcon
                closable
              />
            </Col>
          </Row>

          {/* 关键指标 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="monitoring-card">
                  <Statistic
                    title="CPU使用率"
                    value={systemMetrics.cpu_usage}
                    precision={1}
                    suffix="%"
                    prefix={<DashboardOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                  />
                  <Progress 
                    percent={systemMetrics.cpu_usage} 
                    size="small" 
                    strokeColor={{
                      '0%': '#00d4aa',
                      '70%': '#ff8c42',
                      '90%': '#ff4757'
                    }}
                    style={{ marginTop: '8px' }}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="monitoring-card">
                  <Statistic
                    title="内存使用率"
                    value={systemMetrics.memory_usage}
                    precision={1}
                    suffix="%"
                    prefix={<ServerOutlined style={{ color: 'var(--color-accent-green)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                  />
                  <Progress 
                    percent={systemMetrics.memory_usage} 
                    size="small" 
                    strokeColor={{
                      '0%': '#00d4aa',
                      '70%': '#ff8c42',
                      '90%': '#ff4757'
                    }}
                    style={{ marginTop: '8px' }}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="monitoring-card">
                  <Statistic
                    title="响应时间"
                    value={systemMetrics.response_time}
                    suffix="ms"
                    prefix={<CloudOutlined style={{ color: 'var(--status-warning)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--text-muted)' }}>
                    目标: &lt;200ms
                  </div>
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="monitoring-card">
                  <Statistic
                    title="活跃连接"
                    value={systemMetrics.active_connections}
                    prefix={<DatabaseOutlined style={{ color: 'var(--status-success)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--text-muted)' }}>
                    峰值: 2000
                  </div>
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="monitoring-card" title="CPU使用率趋势" style={{ height: '400px' }}>
                  <Line
                    data={cpuTrendData}
                    xField="time"
                    yField="cpu"
                    smooth
                    color="var(--color-accent-blue)"
                    point={{
                      size: 4,
                      shape: 'circle'
                    }}
                    yAxis={{
                      min: 0,
                      max: 100
                    }}
                  />
                </Card>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="monitoring-card" title="内存使用率趋势" style={{ height: '400px' }}>
                  <Area
                    data={memoryTrendData}
                    xField="time"
                    yField="memory"
                    smooth
                    color="var(--color-accent-green)"
                    areaStyle={{
                      fill: 'l(270) 0:rgba(0, 212, 170, 0.1) 1:rgba(0, 212, 170, 0.3)'
                    }}
                    yAxis={{
                      min: 0,
                      max: 100
                    }}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 详细监控数据 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Card className="monitoring-card">
              <Tabs defaultActiveKey="services">
                <TabPane tab="服务状态" key="services">
                  <Table
                    dataSource={mockServices}
                    columns={serviceColumns}
                    rowKey="name"
                    pagination={false}
                  />
                </TabPane>
                
                <TabPane tab="系统告警" key="alerts">
                  <Table
                    dataSource={mockAlerts}
                    columns={alertColumns}
                    rowKey="id"
                    pagination={false}
                  />
                </TabPane>
              </Tabs>
            </Card>
          </motion.div>
        </div>
      </MonitoringContainer>
    </MainLayout>
  );
};

export default MonitoringPage;
