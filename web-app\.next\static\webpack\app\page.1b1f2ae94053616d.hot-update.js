"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/timeline/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ToolOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/../node_modules/@ant-design/icons/es/icons/ArrowUpOutlined.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @ant-design/charts */ \"(app-pages-browser)/../node_modules/@ant-design/plots/es/components/line/index.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @ant-design/charts */ \"(app-pages-browser)/../node_modules/@ant-design/plots/es/components/gauge/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! styled-components */ \"(app-pages-browser)/../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_countup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-countup */ \"(app-pages-browser)/../node_modules/react-countup/build/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useApi */ \"(app-pages-browser)/./src/hooks/useApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction _templateObject() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  min-height: 100vh;\\n  background: var(--bg-primary);\\n\"\n    ]);\n    _templateObject = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-secondary);\\n  border-bottom: 1px solid var(--border-primary);\\n  padding: 0 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: relative;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    right: 0;\\n    height: 2px;\\n    background: linear-gradient(90deg,\\n      transparent 0%,\\n      var(--color-accent-blue) 50%,\\n      transparent 100%);\\n    opacity: 0.6;\\n  }\\n\"\n    ]);\n    _templateObject1 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-secondary);\\n  border-right: 1px solid var(--border-primary);\\n\\n  .ant-layout-sider-trigger {\\n    background: var(--bg-primary);\\n    border-top: 1px solid var(--border-primary);\\n    color: var(--text-secondary);\\n\\n    &:hover {\\n      background: var(--color-accent-blue);\\n      color: white;\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-primary);\\n  padding: 24px;\\n  overflow-y: auto;\\n\"\n    ]);\n    _templateObject3 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n\\n  .logo-icon {\\n    width: 32px;\\n    height: 32px;\\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\\n    border-radius: 6px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    color: white;\\n    font-size: 16px;\\n  }\\n\"\n    ]);\n    _templateObject4 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: \",\n        \";\\n  box-shadow: 0 0 10px \",\n        \";\\n  animation: pulse 2s infinite;\\n\"\n    ]);\n    _templateObject5 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  background: var(--bg-card);\\n  border: 1px solid var(--border-primary);\\n  border-radius: 8px;\\n  box-shadow: var(--shadow-card);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 3px;\\n    background: linear-gradient(90deg,\\n      var(--color-accent-blue),\\n      var(--color-accent-green));\\n    opacity: 0;\\n    transition: opacity 0.3s ease;\\n  }\\n\\n  &:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-hover);\\n\\n    &::before {\\n      opacity: 1;\\n    }\\n  }\\n\\n  .ant-card-head {\\n    background: var(--bg-secondary);\\n    border-bottom: 1px solid var(--border-primary);\\n    color: var(--text-primary);\\n  }\\n\\n  .ant-card-body {\\n    background: var(--bg-card);\\n  }\\n\"\n    ]);\n    _templateObject6 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  text-align: center;\\n\\n  .ant-statistic-title {\\n    color: var(--text-secondary);\\n    font-size: 14px;\\n    margin-bottom: 8px;\\n  }\\n\\n  .ant-statistic-content {\\n    color: var(--text-primary);\\n    font-size: 32px;\\n    font-weight: 700;\\n  }\\n\"\n    ]);\n    _templateObject7 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    var data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  color: var(--text-primary);\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n\\n  &::before {\\n    content: '';\\n    width: 4px;\\n    height: 20px;\\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\\n    border-radius: 2px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function _templateObject() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nvar Header = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Header, Sider = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Sider, Content = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Content;\n// 工业风格样式组件\nvar IndustrialLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_templateObject());\n_c = IndustrialLayout;\nvar IndustrialHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Header)(_templateObject1());\n_c1 = IndustrialHeader;\nvar IndustrialSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Sider)(_templateObject2());\n_c2 = IndustrialSider;\nvar IndustrialContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Content)(_templateObject3());\n_c3 = IndustrialContent;\nvar Logo = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div(_templateObject4());\n_c4 = Logo;\nvar StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div(_templateObject5(), function(props) {\n    return props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)';\n}, function(props) {\n    return props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)';\n});\n_c5 = StatusIndicator;\nvar DashboardCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_templateObject6());\nvar MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(DashboardCard)(_templateObject7());\n_c6 = MetricCard;\nvar SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].h2(_templateObject8());\n_c7 = SectionTitle;\n// 模拟数据\nvar mockData = {\n    overview: {\n        totalEquipment: 156,\n        runningEquipment: 142,\n        maintenanceEquipment: 8,\n        errorEquipment: 6,\n        oeeRate: 0.85,\n        qualityRate: 0.96,\n        productionOutput: 2847,\n        energyConsumption: 1234.5\n    },\n    productionTrend: [\n        {\n            time: '00:00',\n            output: 120,\n            target: 130\n        },\n        {\n            time: '02:00',\n            output: 132,\n            target: 130\n        },\n        {\n            time: '04:00',\n            output: 101,\n            target: 130\n        },\n        {\n            time: '06:00',\n            output: 134,\n            target: 130\n        },\n        {\n            time: '08:00',\n            output: 90,\n            target: 130\n        },\n        {\n            time: '10:00',\n            output: 230,\n            target: 130\n        },\n        {\n            time: '12:00',\n            output: 210,\n            target: 130\n        },\n        {\n            time: '14:00',\n            output: 220,\n            target: 130\n        },\n        {\n            time: '16:00',\n            output: 200,\n            target: 130\n        },\n        {\n            time: '18:00',\n            output: 180,\n            target: 130\n        },\n        {\n            time: '20:00',\n            output: 160,\n            target: 130\n        },\n        {\n            time: '22:00',\n            output: 140,\n            target: 130\n        }\n    ],\n    equipmentStatus: [\n        {\n            name: 'CNC-001',\n            status: 'running',\n            utilization: 85,\n            temperature: 45\n        },\n        {\n            name: 'CNC-002',\n            status: 'running',\n            utilization: 92,\n            temperature: 48\n        },\n        {\n            name: 'Robot-001',\n            status: 'idle',\n            utilization: 0,\n            temperature: 25\n        },\n        {\n            name: 'Press-001',\n            status: 'maintenance',\n            utilization: 0,\n            temperature: 30\n        },\n        {\n            name: 'Grinder-001',\n            status: 'error',\n            utilization: 0,\n            temperature: 65\n        }\n    ],\n    recentAlerts: [\n        {\n            id: 1,\n            type: 'warning',\n            message: 'CNC-001温度异常',\n            time: '2分钟前'\n        },\n        {\n            id: 2,\n            type: 'error',\n            message: 'Grinder-001故障停机',\n            time: '15分钟前'\n        },\n        {\n            id: 3,\n            type: 'info',\n            message: 'Robot-001完成维护',\n            time: '1小时前'\n        },\n        {\n            id: 4,\n            type: 'success',\n            message: '生产线A达成日产目标',\n            time: '2小时前'\n        }\n    ],\n    agentTasks: [\n        {\n            id: 1,\n            name: '生产排产优化',\n            agent: '排产智能体',\n            status: 'running',\n            progress: 75\n        },\n        {\n            id: 2,\n            name: '设备预测维护',\n            agent: '维护智能体',\n            status: 'completed',\n            progress: 100\n        },\n        {\n            id: 3,\n            name: '质量异常分析',\n            agent: '质量智能体',\n            status: 'pending',\n            progress: 0\n        },\n        {\n            id: 4,\n            name: '供应链风险评估',\n            agent: '供应链智能体',\n            status: 'running',\n            progress: 45\n        }\n    ]\n};\nfunction HomePage() {\n    var _this = this;\n    var _dashboardData_overview, _dashboardData_overview1, _dashboardData_overview2, _dashboardData_quality_metrics, _dashboardData_quality_metrics1, _dashboardData_inventory_alerts;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), collapsed = _useState[0], setCollapsed = _useState[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // 获取仪表板数据\n    var _useDashboard = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useDashboard)(), dashboardData = _useDashboard.data, loading = _useDashboard.loading, error = _useDashboard.error, refetch = _useDashboard.refetch;\n    // 获取系统健康状态\n    var isHealthy = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useSystemHealth)().isHealthy;\n    // 根据系统健康状态确定状态指示器\n    var systemStatus = isHealthy ? 'online' : 'offline';\n    // 使用真实数据或回退到模拟数据\n    var displayData = dashboardData || {\n        overview: {\n            total_production_lines: mockData.overview.totalEquipment,\n            active_lines: mockData.overview.runningEquipment,\n            average_efficiency: mockData.overview.oeeRate * 100,\n            total_equipment: mockData.overview.totalEquipment,\n            operational_equipment: mockData.overview.runningEquipment\n        },\n        production_lines: [],\n        equipment: [],\n        quality_metrics: {\n            defect_rate: 1 - mockData.overview.qualityRate,\n            first_pass_yield: mockData.overview.qualityRate,\n            customer_complaints: 2,\n            quality_score: mockData.overview.qualityRate * 100\n        },\n        inventory_alerts: []\n    };\n    // 路径映射\n    var pathMapping = {\n        dashboard: '/',\n        agents: '/agents',\n        'agent-chat': '/agents/chat',\n        'agent-workflow': '/agents/orchestration',\n        'agent-tasks': '/agents/tasks',\n        production: '/production',\n        'production-planning': '/production',\n        'production-monitoring': '/production',\n        'production-analysis': '/production',\n        quality: '/quality',\n        'quality-inspection': '/quality',\n        'quality-analysis': '/quality',\n        'quality-standards': '/quality',\n        maintenance: '/maintenance',\n        'equipment-monitoring': '/maintenance',\n        'predictive-maintenance': '/maintenance',\n        'maintenance-planning': '/maintenance',\n        'supply-chain': '/supply-chain',\n        'inventory-management': '/supply-chain',\n        'supplier-management': '/supply-chain',\n        procurement: '/supply-chain',\n        knowledge: '/knowledge',\n        'knowledge-search': '/knowledge',\n        'knowledge-graph': '/knowledge',\n        'document-management': '/knowledge',\n        monitoring: '/monitoring'\n    };\n    // 菜单项\n    var menuItems = [\n        {\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 13\n            }, this),\n            label: '总览'\n        },\n        {\n            key: 'agents',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 13\n            }, this),\n            label: '智能体',\n            children: [\n                {\n                    key: 'agent-chat',\n                    label: '智能对话'\n                },\n                {\n                    key: 'agent-workflow',\n                    label: '工作流'\n                },\n                {\n                    key: 'agent-tasks',\n                    label: '任务管理'\n                }\n            ]\n        },\n        {\n            key: 'production',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, this),\n            label: '生产管理',\n            children: [\n                {\n                    key: 'production-planning',\n                    label: '生产计划'\n                },\n                {\n                    key: 'production-monitoring',\n                    label: '生产监控'\n                },\n                {\n                    key: 'production-analysis',\n                    label: '生产分析'\n                }\n            ]\n        },\n        {\n            key: 'quality',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 345,\n                columnNumber: 13\n            }, this),\n            label: '质量管理',\n            children: [\n                {\n                    key: 'quality-inspection',\n                    label: '质量检验'\n                },\n                {\n                    key: 'quality-analysis',\n                    label: '质量分析'\n                },\n                {\n                    key: 'quality-standards',\n                    label: '质量标准'\n                }\n            ]\n        },\n        {\n            key: 'maintenance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, this),\n            label: '设备维护',\n            children: [\n                {\n                    key: 'equipment-monitoring',\n                    label: '设备监控'\n                },\n                {\n                    key: 'predictive-maintenance',\n                    label: '预测维护'\n                },\n                {\n                    key: 'maintenance-planning',\n                    label: '维护计划'\n                }\n            ]\n        },\n        {\n            key: 'supply-chain',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 13\n            }, this),\n            label: '供应链',\n            children: [\n                {\n                    key: 'inventory-management',\n                    label: '库存管理'\n                },\n                {\n                    key: 'supplier-management',\n                    label: '供应商管理'\n                },\n                {\n                    key: 'procurement',\n                    label: '采购管理'\n                }\n            ]\n        },\n        {\n            key: 'knowledge',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 13\n            }, this),\n            label: '知识库',\n            children: [\n                {\n                    key: 'knowledge-search',\n                    label: '知识搜索'\n                },\n                {\n                    key: 'knowledge-graph',\n                    label: '知识图谱'\n                },\n                {\n                    key: 'document-management',\n                    label: '文档管理'\n                }\n            ]\n        }\n    ];\n    // 用户菜单\n    var userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 13\n            }, this),\n            label: '个人资料'\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 13\n            }, this),\n            label: '设置'\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 13\n            }, this),\n            label: '退出登录'\n        }\n    ];\n    // 生产趋势图配置\n    var productionTrendConfig = {\n        data: mockData.productionTrend,\n        xField: 'time',\n        yField: 'output',\n        seriesField: 'type',\n        smooth: true,\n        animation: {\n            appear: {\n                animation: 'path-in',\n                duration: 1000\n            }\n        }\n    };\n    // OEE仪表盘配置\n    var oeeGaugeConfig = {\n        percent: mockData.overview.oeeRate,\n        range: {\n            color: [\n                '#F4664A',\n                '#FAAD14',\n                '#30BF78'\n            ]\n        },\n        indicator: {\n            pointer: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            },\n            pin: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            }\n        },\n        statistic: {\n            content: {\n                style: {\n                    fontSize: '36px',\n                    lineHeight: '36px'\n                }\n            }\n        }\n    };\n    // 设备状态表格列\n    var equipmentColumns = [\n        {\n            title: '设备名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: function(status) {\n                var statusMap = {\n                    running: {\n                        color: 'green',\n                        text: '运行中'\n                    },\n                    idle: {\n                        color: 'orange',\n                        text: '空闲'\n                    },\n                    maintenance: {\n                        color: 'blue',\n                        text: '维护中'\n                    },\n                    error: {\n                        color: 'red',\n                        text: '故障'\n                    }\n                };\n                var statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    color: statusInfo.color,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 16\n                }, _this);\n            }\n        },\n        {\n            title: '利用率',\n            dataIndex: 'utilization',\n            key: 'utilization',\n            render: function(utilization) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    percent: utilization,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, _this);\n            }\n        },\n        {\n            title: '温度',\n            dataIndex: 'temperature',\n            key: 'temperature',\n            render: function(temperature) {\n                return \"\".concat(temperature, \"\\xb0C\");\n            }\n        }\n    ];\n    // 智能体任务表格列\n    var taskColumns = [\n        {\n            title: '任务名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '智能体',\n            dataIndex: 'agent',\n            key: 'agent'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: function(status) {\n                var statusMap = {\n                    running: {\n                        color: 'blue',\n                        text: '执行中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 56\n                        }, _this)\n                    },\n                    completed: {\n                        color: 'green',\n                        text: '已完成',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 59\n                        }, _this)\n                    },\n                    pending: {\n                        color: 'orange',\n                        text: '等待中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 58\n                        }, _this)\n                    },\n                    error: {\n                        color: 'red',\n                        text: '失败',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 52\n                        }, _this)\n                    }\n                };\n                var statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    color: statusInfo.color,\n                    icon: statusInfo.icon,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, _this);\n            }\n        },\n        {\n            title: '进度',\n            dataIndex: 'progress',\n            key: 'progress',\n            render: function(progress) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    percent: progress,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, _this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialSider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 240,\n                collapsedWidth: 80,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        style: {\n                            padding: '16px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Logo, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    className: \"logo-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_28__.AnimatePresence, {\n                                    children: !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            width: 'auto'\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"工业智能体\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        defaultSelectedKeys: [\n                            'dashboard'\n                        ],\n                        items: menuItems,\n                        style: {\n                            border: 'none'\n                        },\n                        onClick: function(param) {\n                            var key = param.key;\n                            if (key !== 'dashboard') {\n                                router.push(\"/\".concat(key));\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: function() {\n                                            return setCollapsed(!collapsed);\n                                        },\n                                        style: {\n                                            fontSize: '16px',\n                                            width: 40,\n                                            height: 40,\n                                            color: 'var(--text-secondary)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(StatusIndicator, {\n                                                status: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)',\n                                                    fontSize: '14px'\n                                                },\n                                                children: [\n                                                    \"系统状态: \",\n                                                    systemStatus === 'online' ? '正常' : systemStatus === 'warning' ? '警告' : '离线'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                color: 'var(--text-secondary)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        marginRight: 8,\n                                                        background: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: 'var(--text-primary)'\n                                                    },\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(IndustrialContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: '24px',\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SectionTitle, {\n                                            children: \"总览仪表板\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            onClick: refetch,\n                                            loading: loading,\n                                            className: \"metal-button\",\n                                            children: \"刷新数据\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        24,\n                                        24\n                                    ],\n                                    style: {\n                                        marginBottom: 32\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"生产线总数\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview = dashboardData.overview) === null || _dashboardData_overview === void 0 ? void 0 : _dashboardData_overview.total_production_lines) || displayData.overview.total_production_lines,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-blue)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--text-muted)'\n                                                            },\n                                                            children: [\n                                                                \"运行中: \",\n                                                                (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview1 = dashboardData.overview) === null || _dashboardData_overview1 === void 0 ? void 0 : _dashboardData_overview1.active_lines) || displayData.overview.active_lines,\n                                                                \" 条\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"平均效率\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview2 = dashboardData.overview) === null || _dashboardData_overview2 === void 0 ? void 0 : _dashboardData_overview2.average_efficiency) || displayData.overview.average_efficiency,\n                                                            precision: 1,\n                                                            suffix: \"%\",\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-success)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: \"较昨日 +2.3%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"质量评分\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quality_metrics = dashboardData.quality_metrics) === null || _dashboardData_quality_metrics === void 0 ? void 0 : _dashboardData_quality_metrics.quality_score) || displayData.quality_metrics.quality_score,\n                                                            precision: 1,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-green)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: [\n                                                                \"缺陷率: \",\n                                                                (((dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quality_metrics1 = dashboardData.quality_metrics) === null || _dashboardData_quality_metrics1 === void 0 ? void 0 : _dashboardData_quality_metrics1.defect_rate) || displayData.quality_metrics.defect_rate) * 100).toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_27__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                            title: \"库存预警\",\n                                                            value: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_inventory_alerts = dashboardData.inventory_alerts) === null || _dashboardData_inventory_alerts === void 0 ? void 0 : _dashboardData_inventory_alerts.length) || displayData.inventory_alerts.length,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-warning)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: function(value) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-warning)'\n                                                            },\n                                                            children: \"需要立即处理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: 24\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 16,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"生产趋势\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/production/monitoring\",\n                                                    children: \"查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_42__[\"default\"], (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__._)({}, productionTrendConfig), void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"OEE指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_44__[\"default\"], (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_43__._)({}, oeeGaugeConfig), void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"设备状态\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/maintenance/equipment-monitoring\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                    dataSource: mockData.equipmentStatus,\n                                                    columns: equipmentColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"智能体任务\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/agents/tasks\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 42\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_45__[\"default\"], {\n                                                    dataSource: mockData.agentTasks,\n                                                    columns: taskColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    style: {\n                                        marginTop: 16\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                        span: 24,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            title: \"最近告警\",\n                                            extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/alerts\",\n                                                children: \"查看全部\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 41\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__[\"default\"], {\n                                                children: mockData.recentAlerts.map(function(alert) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_46__[\"default\"].Item, {\n                                                        color: alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'orange' : alert.type === 'success' ? 'green' : 'blue',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                justifyContent: 'space-between'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                                    children: alert.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#999',\n                                                                        fontSize: '12px'\n                                                                    },\n                                                                    children: alert.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, alert.id, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 530,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"+aBeP5mqJK+UiI55WWmzT90u3Ew=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useDashboard,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useSystemHealth\n    ];\n});\n_c8 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"IndustrialLayout\");\n$RefreshReg$(_c1, \"IndustrialHeader\");\n$RefreshReg$(_c2, \"IndustrialSider\");\n$RefreshReg$(_c3, \"IndustrialContent\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"StatusIndicator\");\n$RefreshReg$(_c6, \"MetricCard\");\n$RefreshReg$(_c7, \"SectionTitle\");\n$RefreshReg$(_c8, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});