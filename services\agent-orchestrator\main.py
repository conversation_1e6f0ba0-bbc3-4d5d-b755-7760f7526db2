"""
工业智能体平台 - 智能体编排服务
负责多智能体协调、工作流管理、事件驱动处理
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union, Callable
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import uuid
from enum import Enum
import httpx
from dataclasses import dataclass, asdict
import networkx as nx

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
agent_registry = {}
workflow_engine = None

class AgentType(str, Enum):
    PRODUCTION = "production"
    QUALITY = "quality"
    MAINTENANCE = "maintenance"
    SUPPLY_CHAIN = "supply_chain"
    KNOWLEDGE = "knowledge"
    COORDINATOR = "coordinator"

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class EventType(str, Enum):
    TASK_CREATED = "task_created"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    AGENT_REGISTERED = "agent_registered"
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_COMPLETED = "workflow_completed"
    ALERT_TRIGGERED = "alert_triggered"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer, workflow_engine
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化工作流引擎
    workflow_engine = WorkflowEngine()
    await workflow_engine.initialize()
    
    # 启动事件消费者
    asyncio.create_task(start_event_consumer())
    
    logger.info("Agent orchestrator service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Agent orchestrator service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="智能体编排服务",
    description="多智能体协调、工作流管理、事件驱动处理",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class AgentInfo(BaseModel):
    id: str
    name: str
    type: AgentType
    capabilities: List[str]
    endpoint: str
    status: str = "active"
    metadata: Dict[str, Any] = {}

class Task(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    agent_type: AgentType
    input_data: Dict[str, Any]
    priority: int = 5  # 1-10, 1最高
    timeout: int = 300  # 秒
    retry_count: int = 0
    max_retries: int = 3
    dependencies: List[str] = []
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class WorkflowDefinition(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    tasks: List[Task]
    trigger_conditions: Dict[str, Any] = {}
    is_active: bool = True

class Event(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: EventType
    source: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)

@dataclass
class Agent:
    """智能体基类"""
    id: str
    name: str
    type: AgentType
    capabilities: List[str]
    endpoint: str
    
    async def execute_task(self, task: Task) -> Dict[str, Any]:
        """执行任务"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.endpoint}/execute",
                    json={
                        "task_id": task.id,
                        "task_name": task.name,
                        "input_data": task.input_data
                    },
                    timeout=task.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise Exception(f"Agent execution failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error executing task {task.id} on agent {self.id}: {e}")
            raise

class AgentRegistry:
    """智能体注册表"""
    
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self.agent_types: Dict[AgentType, List[str]] = {}
    
    def register_agent(self, agent_info: AgentInfo) -> str:
        """注册智能体"""
        agent = Agent(
            id=agent_info.id,
            name=agent_info.name,
            type=agent_info.type,
            capabilities=agent_info.capabilities,
            endpoint=agent_info.endpoint
        )
        
        self.agents[agent.id] = agent
        
        # 按类型分组
        if agent.type not in self.agent_types:
            self.agent_types[agent.type] = []
        self.agent_types[agent.type].append(agent.id)
        
        logger.info(f"Registered agent: {agent.name} ({agent.type})")
        return agent.id
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            del self.agents[agent_id]
            
            if agent.type in self.agent_types:
                self.agent_types[agent.type].remove(agent_id)
                if not self.agent_types[agent.type]:
                    del self.agent_types[agent.type]
            
            logger.info(f"Unregistered agent: {agent_id}")
    
    def get_agent(self, agent_id: str) -> Optional[Agent]:
        """获取智能体"""
        return self.agents.get(agent_id)
    
    def get_agents_by_type(self, agent_type: AgentType) -> List[Agent]:
        """按类型获取智能体"""
        agent_ids = self.agent_types.get(agent_type, [])
        return [self.agents[agent_id] for agent_id in agent_ids if agent_id in self.agents]
    
    def find_suitable_agent(self, task: Task) -> Optional[Agent]:
        """找到适合的智能体"""
        suitable_agents = self.get_agents_by_type(task.agent_type)
        
        if not suitable_agents:
            return None
        
        # 简单的负载均衡：选择第一个可用的
        return suitable_agents[0]

class TaskManager:
    """任务管理器"""
    
    def __init__(self, agent_registry: AgentRegistry):
        self.agent_registry = agent_registry
        self.running_tasks: Dict[str, Task] = {}
        self.task_queue: List[Task] = []
    
    async def submit_task(self, task: Task) -> str:
        """提交任务"""
        # 保存到数据库
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO tasks (id, name, description, agent_type, input_data, priority, status, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                task.id, task.name, task.description, task.agent_type.value,
                json.dumps(task.input_data), task.priority, task.status.value, task.created_at
            )
        
        # 添加到队列
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda t: t.priority)  # 按优先级排序
        
        # 发送事件
        await self.publish_event(Event(
            type=EventType.TASK_CREATED,
            source="task_manager",
            data={"task_id": task.id, "task_name": task.name}
        ))
        
        logger.info(f"Task submitted: {task.id}")
        return task.id
    
    async def execute_next_task(self):
        """执行下一个任务"""
        if not self.task_queue:
            return
        
        task = self.task_queue.pop(0)
        
        # 检查依赖
        if not await self.check_dependencies(task):
            # 依赖未满足，重新加入队列
            self.task_queue.append(task)
            return
        
        # 找到合适的智能体
        agent = self.agent_registry.find_suitable_agent(task)
        if not agent:
            logger.warning(f"No suitable agent found for task {task.id}")
            task.status = TaskStatus.FAILED
            task.error = "No suitable agent available"
            await self.update_task_status(task)
            return
        
        # 执行任务
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.utcnow()
        self.running_tasks[task.id] = task
        
        await self.update_task_status(task)
        
        try:
            result = await agent.execute_task(task)
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.result = result
            
            await self.publish_event(Event(
                type=EventType.TASK_COMPLETED,
                source="task_manager",
                data={"task_id": task.id, "result": result}
            ))
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.retry_count += 1
            
            await self.publish_event(Event(
                type=EventType.TASK_FAILED,
                source="task_manager",
                data={"task_id": task.id, "error": str(e)}
            ))
            
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.status = TaskStatus.PENDING
                self.task_queue.append(task)
                logger.info(f"Task {task.id} will be retried ({task.retry_count}/{task.max_retries})")
        
        finally:
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            await self.update_task_status(task)
    
    async def check_dependencies(self, task: Task) -> bool:
        """检查任务依赖"""
        if not task.dependencies:
            return True
        
        async with db_pool.acquire() as conn:
            for dep_id in task.dependencies:
                result = await conn.fetchrow(
                    "SELECT status FROM tasks WHERE id = $1", dep_id
                )
                if not result or result['status'] != TaskStatus.COMPLETED.value:
                    return False
        
        return True
    
    async def update_task_status(self, task: Task):
        """更新任务状态"""
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                UPDATE tasks SET status = $1, started_at = $2, completed_at = $3, 
                       result = $4, error = $5, retry_count = $6
                WHERE id = $7
                """,
                task.status.value, task.started_at, task.completed_at,
                json.dumps(task.result) if task.result else None,
                task.error, task.retry_count, task.id
            )
    
    async def publish_event(self, event: Event):
        """发布事件"""
        await kafka_producer.send("agent_events", asdict(event))

class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self):
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.running_workflows: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self):
        """初始化工作流引擎"""
        # 创建工作流表
        async with db_pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS workflows (
                    id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    definition JSONB NOT NULL,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS workflow_executions (
                    id VARCHAR(50) PRIMARY KEY,
                    workflow_id VARCHAR(50) NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    input_data JSONB,
                    output_data JSONB,
                    started_at TIMESTAMP WITH TIME ZONE,
                    completed_at TIMESTAMP WITH TIME ZONE,
                    error TEXT
                )
            """)
    
    async def register_workflow(self, workflow: WorkflowDefinition) -> str:
        """注册工作流"""
        self.workflows[workflow.id] = workflow
        
        # 保存到数据库
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO workflows (id, name, description, definition, is_active)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    definition = EXCLUDED.definition,
                    is_active = EXCLUDED.is_active
                """,
                workflow.id, workflow.name, workflow.description,
                json.dumps([task.dict() for task in workflow.tasks]),
                workflow.is_active
            )
        
        logger.info(f"Registered workflow: {workflow.name}")
        return workflow.id
    
    async def execute_workflow(self, workflow_id: str, input_data: Dict[str, Any] = None) -> str:
        """执行工作流"""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        execution_id = str(uuid.uuid4())
        
        # 创建执行记录
        execution = {
            "id": execution_id,
            "workflow_id": workflow_id,
            "status": "running",
            "input_data": input_data or {},
            "started_at": datetime.utcnow(),
            "tasks": {task.id: "pending" for task in workflow.tasks}
        }
        
        self.running_workflows[execution_id] = execution
        
        # 保存到数据库
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO workflow_executions (id, workflow_id, status, input_data, started_at)
                VALUES ($1, $2, $3, $4, $5)
                """,
                execution_id, workflow_id, "running",
                json.dumps(input_data), execution["started_at"]
            )
        
        # 发送事件
        await kafka_producer.send("agent_events", {
            "type": EventType.WORKFLOW_STARTED.value,
            "source": "workflow_engine",
            "data": {"execution_id": execution_id, "workflow_id": workflow_id},
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # 异步执行工作流
        asyncio.create_task(self._execute_workflow_tasks(execution_id))
        
        return execution_id
    
    async def _execute_workflow_tasks(self, execution_id: str):
        """执行工作流任务"""
        execution = self.running_workflows[execution_id]
        workflow = self.workflows[execution["workflow_id"]]
        
        # 构建任务依赖图
        task_graph = nx.DiGraph()
        for task in workflow.tasks:
            task_graph.add_node(task.id, task=task)
            for dep in task.dependencies:
                task_graph.add_edge(dep, task.id)
        
        # 拓扑排序执行任务
        try:
            for task_id in nx.topological_sort(task_graph):
                task = task_graph.nodes[task_id]["task"]
                
                # 等待依赖完成
                for dep_id in task.dependencies:
                    while execution["tasks"][dep_id] not in ["completed", "failed"]:
                        await asyncio.sleep(1)
                    
                    if execution["tasks"][dep_id] == "failed":
                        execution["tasks"][task_id] = "cancelled"
                        continue
                
                # 执行任务
                execution["tasks"][task_id] = "running"
                
                # 这里应该调用TaskManager来执行任务
                # 为了简化，我们模拟任务执行
                await asyncio.sleep(2)  # 模拟任务执行时间
                execution["tasks"][task_id] = "completed"
            
            execution["status"] = "completed"
            execution["completed_at"] = datetime.utcnow()
            
        except Exception as e:
            execution["status"] = "failed"
            execution["error"] = str(e)
            execution["completed_at"] = datetime.utcnow()
        
        finally:
            # 更新数据库
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    UPDATE workflow_executions 
                    SET status = $1, completed_at = $2, error = $3
                    WHERE id = $4
                    """,
                    execution["status"], execution.get("completed_at"),
                    execution.get("error"), execution_id
                )
            
            # 发送完成事件
            await kafka_producer.send("agent_events", {
                "type": EventType.WORKFLOW_COMPLETED.value,
                "source": "workflow_engine",
                "data": {"execution_id": execution_id, "status": execution["status"]},
                "timestamp": datetime.utcnow().isoformat()
            })

# 全局实例
agent_registry = AgentRegistry()
task_manager = TaskManager(agent_registry)

async def start_event_consumer():
    """启动事件消费者"""
    consumer = AIOKafkaConsumer(
        "agent_events",
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        group_id="agent_orchestrator",
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))
    )
    
    await consumer.start()
    
    try:
        async for message in consumer:
            event_data = message.value
            logger.info(f"Received event: {event_data['type']}")
            
            # 处理事件
            await handle_event(event_data)
    
    finally:
        await consumer.stop()

async def handle_event(event_data: Dict[str, Any]):
    """处理事件"""
    event_type = event_data.get("type")
    
    if event_type == EventType.TASK_COMPLETED.value:
        # 任务完成后，检查是否有等待的任务
        await task_manager.execute_next_task()
    
    elif event_type == EventType.ALERT_TRIGGERED.value:
        # 处理告警事件
        await handle_alert(event_data["data"])

async def handle_alert(alert_data: Dict[str, Any]):
    """处理告警"""
    # 根据告警类型创建相应的任务
    alert_type = alert_data.get("type")
    
    if alert_type == "equipment_failure":
        # 创建维护任务
        task = Task(
            name="Equipment Failure Response",
            description=f"Handle equipment failure: {alert_data.get('equipment_id')}",
            agent_type=AgentType.MAINTENANCE,
            input_data=alert_data,
            priority=1  # 高优先级
        )
        await task_manager.submit_task(task)

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "agent-orchestrator"}

@app.post("/agents/register")
async def register_agent(agent_info: AgentInfo):
    """注册智能体"""
    agent_id = agent_registry.register_agent(agent_info)
    return {"message": "Agent registered successfully", "agent_id": agent_id}

@app.delete("/agents/{agent_id}")
async def unregister_agent(agent_id: str):
    """注销智能体"""
    agent_registry.unregister_agent(agent_id)
    return {"message": "Agent unregistered successfully"}

@app.get("/agents")
async def list_agents():
    """获取智能体列表"""
    agents = []
    for agent in agent_registry.agents.values():
        agents.append({
            "id": agent.id,
            "name": agent.name,
            "type": agent.type,
            "capabilities": agent.capabilities,
            "endpoint": agent.endpoint
        })
    return {"agents": agents}

@app.post("/tasks")
async def submit_task(task: Task):
    """提交任务"""
    task_id = await task_manager.submit_task(task)
    return {"message": "Task submitted successfully", "task_id": task_id}

@app.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    async with db_pool.acquire() as conn:
        row = await conn.fetchrow("SELECT * FROM tasks WHERE id = $1", task_id)
        if row:
            return dict(row)
        else:
            raise HTTPException(status_code=404, detail="Task not found")

@app.post("/workflows")
async def create_workflow(workflow: WorkflowDefinition):
    """创建工作流"""
    workflow_id = await workflow_engine.register_workflow(workflow)
    return {"message": "Workflow created successfully", "workflow_id": workflow_id}

@app.post("/workflows/{workflow_id}/execute")
async def execute_workflow(workflow_id: str, input_data: Dict[str, Any] = None):
    """执行工作流"""
    execution_id = await workflow_engine.execute_workflow(workflow_id, input_data)
    return {"message": "Workflow execution started", "execution_id": execution_id}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
