{"name": "d3-geo-projection", "version": "4.0.0", "description": "Extended geographic projections for d3-geo.", "homepage": "https://d3js.org/d3-geo-projection/", "repository": {"type": "git", "url": "https://github.com/d3/d3-geo-projection.git"}, "keywords": ["d3", "d3-module", "cartography", "projection"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "contributors": [{"name": "<PERSON>", "url": "http://www.jasondavies.com"}, {"name": "<PERSON>", "url": "https://visionscarto.net"}], "type": "module", "files": ["bin/*.js", "dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-geo-projection.min.js", "unpkg": "dist/d3-geo-projection.min.js", "exports": {"umd": "./dist/d3-geo-projection.min.js", "default": "./src/index.js"}, "sideEffects": false, "dependencies": {"commander": "7", "d3-array": "1 - 3", "d3-geo": "1.12.0 - 3"}, "devDependencies": {"canvas": "2", "d3-format": "1 - 3", "eslint": "7", "mocha": "9", "pixelmatch": "5", "pngjs": "4", "rollup": "2", "rollup-plugin-terser": "7", "topojson-client": "3", "us-atlas": "1", "world-atlas": "1"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test && rollup -c && git push", "postpublish": "git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd -"}, "engines": {"node": ">=12"}, "bin": {"geo2svg": "bin/geo2svg.js", "geograticule": "bin/geograticule.js", "geoproject": "bin/geoproject.js", "geoquantize": "bin/geoquantize.js", "geostitch": "bin/geostitch.js"}}