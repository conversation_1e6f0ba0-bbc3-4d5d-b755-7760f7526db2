# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic[email]==2.5.0

# 数据库
asyncpg==0.29.0
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1

# Redis
redis==5.0.1
aioredis==2.0.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 消息队列
aiokafka==0.8.11
celery==5.3.4

# 机器学习和AI
torch==2.1.1
transformers==4.36.0
langchain==0.0.350
openai==1.3.7
anthropic==0.7.8
sentence-transformers==2.2.2
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.4

# 向量数据库
weaviate-client==3.25.3
pinecone-client==2.2.4
chromadb==0.4.18

# 图像处理
opencv-python==********
Pillow==10.1.0

# 数据处理
pydantic==2.5.0
marshmallow==3.20.1

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk==1.38.0

# 配置管理
python-dotenv==1.0.0
pydantic-settings==2.1.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 部署和容器
gunicorn==21.2.0
docker==6.1.3

# 工具库
click==8.1.7
rich==13.7.0
typer==0.9.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 加密和哈希
cryptography==41.0.8
bcrypt==4.1.2

# JSON和序列化
orjson==3.9.10
msgpack==1.0.7

# 网络和协议
websockets==12.0
grpcio==1.59.3
grpcio-tools==1.59.3

# 文件处理
openpyxl==3.1.2
python-docx==1.1.0
PyPDF2==3.0.1

# 图形和可视化
matplotlib==3.8.2
plotly==5.17.0
seaborn==0.13.0

# 工作流和任务调度
celery==5.3.4
dramatiq==1.15.0

# 缓存
cachetools==5.3.2

# 验证和解析
validators==0.22.0
python-magic==0.4.27

# 异步支持
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# 配置和环境
environs==10.0.0

# API文档
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# 国际化
babel==2.13.1

# 工业协议支持
pymodbus==3.5.2
opcua==0.98.13

# 数据科学
scipy==1.11.4
statsmodels==0.14.0

# 图数据库
neo4j==5.14.1

# 搜索引擎
elasticsearch==8.11.0

# 对象存储
boto3==1.34.0
minio==7.2.0

# 消息传递
pika==1.3.2
nats-py==2.6.0

# 分布式计算
dask==2023.11.0
ray==2.8.1

# 模型服务
mlflow==2.8.1
bentoml==1.1.10

# 数据验证
great-expectations==0.18.5

# 工作流编排
prefect==2.14.11
airflow==2.7.3

# 容器编排
kubernetes==28.1.0

# 服务网格
istio==1.19.4

# 监控
jaeger-client==4.8.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# 负载测试
locust==2.17.0
