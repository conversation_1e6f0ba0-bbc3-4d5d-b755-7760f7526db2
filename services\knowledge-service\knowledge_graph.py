"""
知识图谱管理模块
构建和管理工业领域的知识图谱
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import networkx as nx
import numpy as np
from neo4j import AsyncGraphDatabase
import os

logger = logging.getLogger(__name__)

class KnowledgeGraph:
    """知识图谱管理器"""
    
    def __init__(self):
        self.driver = None
        self.graph = nx.DiGraph()  # 内存图结构
        self.entity_types = {
            "Product": {"color": "#FF6B6B", "icon": "🔧"},
            "Process": {"color": "#4ECDC4", "icon": "⚙️"},
            "Equipment": {"color": "#45B7D1", "icon": "🏭"},
            "Material": {"color": "#96CEB4", "icon": "🧱"},
            "Quality": {"color": "#FFEAA7", "icon": "✅"},
            "Supplier": {"color": "#DDA0DD", "icon": "🏢"},
            "Standard": {"color": "#98D8C8", "icon": "📋"},
            "Defect": {"color": "#F7DC6F", "icon": "⚠️"},
            "Parameter": {"color": "#BB8FCE", "icon": "📊"}
        }
        
        self.relationship_types = {
            "REQUIRES": {"description": "需要", "weight": 1.0},
            "PRODUCES": {"description": "生产", "weight": 1.0},
            "USES": {"description": "使用", "weight": 0.8},
            "MADE_OF": {"description": "由...制成", "weight": 0.9},
            "TESTED_BY": {"description": "测试方法", "weight": 0.7},
            "SUPPLIED_BY": {"description": "供应商", "weight": 0.6},
            "COMPLIES_WITH": {"description": "符合标准", "weight": 0.8},
            "CAUSES": {"description": "导致", "weight": 0.9},
            "PREVENTS": {"description": "预防", "weight": 0.8},
            "SIMILAR_TO": {"description": "相似", "weight": 0.5},
            "PART_OF": {"description": "组成部分", "weight": 0.9},
            "DEPENDS_ON": {"description": "依赖", "weight": 0.7}
        }
    
    async def initialize(self):
        """初始化知识图谱"""
        # 连接Neo4j（如果配置了）
        neo4j_uri = os.getenv("NEO4J_URI")
        if neo4j_uri:
            try:
                self.driver = AsyncGraphDatabase.driver(
                    neo4j_uri,
                    auth=(
                        os.getenv("NEO4J_USER", "neo4j"),
                        os.getenv("NEO4J_PASSWORD", "password")
                    )
                )
                await self.create_constraints()
                logger.info("Connected to Neo4j database")
            except Exception as e:
                logger.warning(f"Failed to connect to Neo4j: {e}")
        
        # 加载基础知识图谱
        await self.load_base_knowledge()
    
    async def create_constraints(self):
        """创建Neo4j约束"""
        if not self.driver:
            return
        
        constraints = [
            "CREATE CONSTRAINT entity_id IF NOT EXISTS FOR (e:Entity) REQUIRE e.id IS UNIQUE",
            "CREATE CONSTRAINT product_code IF NOT EXISTS FOR (p:Product) REQUIRE p.code IS UNIQUE",
            "CREATE CONSTRAINT equipment_id IF NOT EXISTS FOR (eq:Equipment) REQUIRE eq.id IS UNIQUE",
            "CREATE CONSTRAINT process_id IF NOT EXISTS FOR (pr:Process) REQUIRE pr.id IS UNIQUE"
        ]
        
        async with self.driver.session() as session:
            for constraint in constraints:
                try:
                    await session.run(constraint)
                except Exception as e:
                    logger.debug(f"Constraint creation result: {e}")
    
    async def load_base_knowledge(self):
        """加载基础知识"""
        # 添加基础实体
        base_entities = [
            # 产品类别
            {"id": "automotive_parts", "type": "Product", "name": "汽车零部件", "category": "category"},
            {"id": "engine_parts", "type": "Product", "name": "发动机零件", "category": "subcategory"},
            {"id": "transmission_parts", "type": "Product", "name": "变速箱零件", "category": "subcategory"},
            {"id": "brake_parts", "type": "Product", "name": "制动系统零件", "category": "subcategory"},
            
            # 工艺类型
            {"id": "machining", "type": "Process", "name": "机械加工", "category": "manufacturing"},
            {"id": "cnc_milling", "type": "Process", "name": "CNC铣削", "category": "machining"},
            {"id": "cnc_turning", "type": "Process", "name": "CNC车削", "category": "machining"},
            {"id": "grinding", "type": "Process", "name": "磨削", "category": "finishing"},
            {"id": "heat_treatment", "type": "Process", "name": "热处理", "category": "treatment"},
            {"id": "surface_coating", "type": "Process", "name": "表面涂层", "category": "finishing"},
            
            # 设备类型
            {"id": "cnc_machine", "type": "Equipment", "name": "CNC机床", "category": "machine_tool"},
            {"id": "grinding_machine", "type": "Equipment", "name": "磨床", "category": "machine_tool"},
            {"id": "heat_treatment_furnace", "type": "Equipment", "name": "热处理炉", "category": "furnace"},
            {"id": "coordinate_measuring_machine", "type": "Equipment", "name": "三坐标测量机", "category": "measurement"},
            
            # 材料类型
            {"id": "aluminum_alloy", "type": "Material", "name": "铝合金", "category": "metal"},
            {"id": "steel", "type": "Material", "name": "钢", "category": "metal"},
            {"id": "cast_iron", "type": "Material", "name": "铸铁", "category": "metal"},
            {"id": "titanium_alloy", "type": "Material", "name": "钛合金", "category": "metal"},
            
            # 质量标准
            {"id": "iso_9001", "type": "Standard", "name": "ISO 9001", "category": "quality_management"},
            {"id": "ts_16949", "type": "Standard", "name": "TS 16949", "category": "automotive"},
            {"id": "iso_14001", "type": "Standard", "name": "ISO 14001", "category": "environmental"},
            
            # 质量参数
            {"id": "dimensional_accuracy", "type": "Parameter", "name": "尺寸精度", "category": "quality"},
            {"id": "surface_roughness", "type": "Parameter", "name": "表面粗糙度", "category": "quality"},
            {"id": "hardness", "type": "Parameter", "name": "硬度", "category": "material_property"},
            {"id": "tensile_strength", "type": "Parameter", "name": "抗拉强度", "category": "material_property"},
            
            # 常见缺陷
            {"id": "dimensional_deviation", "type": "Defect", "name": "尺寸偏差", "category": "dimensional"},
            {"id": "surface_defect", "type": "Defect", "name": "表面缺陷", "category": "surface"},
            {"id": "crack", "type": "Defect", "name": "裂纹", "category": "structural"},
            {"id": "porosity", "type": "Defect", "name": "气孔", "category": "internal"}
        ]
        
        for entity in base_entities:
            await self.add_entity(**entity)
        
        # 添加基础关系
        base_relationships = [
            # 产品层次关系
            ("engine_parts", "automotive_parts", "PART_OF"),
            ("transmission_parts", "automotive_parts", "PART_OF"),
            ("brake_parts", "automotive_parts", "PART_OF"),
            
            # 工艺关系
            ("cnc_milling", "machining", "PART_OF"),
            ("cnc_turning", "machining", "PART_OF"),
            ("automotive_parts", "machining", "REQUIRES"),
            ("machining", "cnc_machine", "USES"),
            ("grinding", "grinding_machine", "USES"),
            ("heat_treatment", "heat_treatment_furnace", "USES"),
            
            # 材料关系
            ("automotive_parts", "aluminum_alloy", "MADE_OF"),
            ("automotive_parts", "steel", "MADE_OF"),
            ("engine_parts", "cast_iron", "MADE_OF"),
            
            # 质量关系
            ("automotive_parts", "ts_16949", "COMPLIES_WITH"),
            ("machining", "iso_9001", "COMPLIES_WITH"),
            ("dimensional_accuracy", "coordinate_measuring_machine", "TESTED_BY"),
            ("surface_roughness", "coordinate_measuring_machine", "TESTED_BY"),
            
            # 缺陷关系
            ("machining", "dimensional_deviation", "CAUSES"),
            ("surface_coating", "surface_defect", "PREVENTS"),
            ("heat_treatment", "crack", "PREVENTS"),
            
            # 参数关系
            ("aluminum_alloy", "tensile_strength", "DEPENDS_ON"),
            ("steel", "hardness", "DEPENDS_ON")
        ]
        
        for from_id, to_id, rel_type in base_relationships:
            await self.add_relationship(from_id, to_id, rel_type)
        
        logger.info(f"Loaded base knowledge graph with {len(base_entities)} entities and {len(base_relationships)} relationships")
    
    async def add_entity(self, id: str, type: str, name: str, **properties):
        """添加实体"""
        entity_data = {
            "id": id,
            "type": type,
            "name": name,
            "created_at": datetime.utcnow().isoformat(),
            **properties
        }
        
        # 添加到内存图
        self.graph.add_node(id, **entity_data)
        
        # 添加到Neo4j（如果可用）
        if self.driver:
            async with self.driver.session() as session:
                await session.run(
                    f"MERGE (e:{type} {{id: $id}}) SET e += $properties",
                    id=id,
                    properties=entity_data
                )
        
        logger.debug(f"Added entity: {id} ({type})")
    
    async def add_relationship(self, from_id: str, to_id: str, rel_type: str, **properties):
        """添加关系"""
        if from_id not in self.graph.nodes or to_id not in self.graph.nodes:
            logger.warning(f"Cannot add relationship {from_id} -> {to_id}: missing entities")
            return
        
        rel_data = {
            "type": rel_type,
            "weight": self.relationship_types.get(rel_type, {}).get("weight", 1.0),
            "created_at": datetime.utcnow().isoformat(),
            **properties
        }
        
        # 添加到内存图
        self.graph.add_edge(from_id, to_id, **rel_data)
        
        # 添加到Neo4j（如果可用）
        if self.driver:
            async with self.driver.session() as session:
                await session.run(
                    f"MATCH (a {{id: $from_id}}), (b {{id: $to_id}}) "
                    f"MERGE (a)-[r:{rel_type}]->(b) SET r += $properties",
                    from_id=from_id,
                    to_id=to_id,
                    properties=rel_data
                )
        
        logger.debug(f"Added relationship: {from_id} -[{rel_type}]-> {to_id}")
    
    async def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """获取实体"""
        if entity_id in self.graph.nodes:
            return dict(self.graph.nodes[entity_id])
        return None
    
    async def get_related_entities(self, entity_id: str, rel_types: List[str] = None, max_depth: int = 2) -> List[Dict[str, Any]]:
        """获取相关实体"""
        if entity_id not in self.graph.nodes:
            return []
        
        related = []
        visited = set()
        queue = [(entity_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if current_id in visited or depth > max_depth:
                continue
            
            visited.add(current_id)
            
            # 获取邻居节点
            for neighbor in self.graph.neighbors(current_id):
                edge_data = self.graph.edges[current_id, neighbor]
                
                if rel_types and edge_data.get("type") not in rel_types:
                    continue
                
                if neighbor not in visited:
                    neighbor_data = dict(self.graph.nodes[neighbor])
                    neighbor_data["relationship"] = edge_data
                    neighbor_data["depth"] = depth + 1
                    related.append(neighbor_data)
                    
                    if depth + 1 < max_depth:
                        queue.append((neighbor, depth + 1))
        
        return related
    
    async def find_path(self, from_id: str, to_id: str, max_length: int = 5) -> List[List[str]]:
        """查找实体间的路径"""
        if from_id not in self.graph.nodes or to_id not in self.graph.nodes:
            return []
        
        try:
            # 使用NetworkX查找所有简单路径
            paths = list(nx.all_simple_paths(
                self.graph, 
                from_id, 
                to_id, 
                cutoff=max_length
            ))
            return paths[:10]  # 限制返回路径数量
        except nx.NetworkXNoPath:
            return []
    
    async def get_graph_statistics(self) -> Dict[str, Any]:
        """获取图统计信息"""
        stats = {
            "total_entities": self.graph.number_of_nodes(),
            "total_relationships": self.graph.number_of_edges(),
            "entity_types": {},
            "relationship_types": {},
            "density": nx.density(self.graph),
            "is_connected": nx.is_weakly_connected(self.graph)
        }
        
        # 统计实体类型
        for node_id, node_data in self.graph.nodes(data=True):
            entity_type = node_data.get("type", "Unknown")
            stats["entity_types"][entity_type] = stats["entity_types"].get(entity_type, 0) + 1
        
        # 统计关系类型
        for _, _, edge_data in self.graph.edges(data=True):
            rel_type = edge_data.get("type", "Unknown")
            stats["relationship_types"][rel_type] = stats["relationship_types"].get(rel_type, 0) + 1
        
        return stats
    
    async def search_entities(self, query: str, entity_types: List[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索实体"""
        results = []
        query_lower = query.lower()
        
        for node_id, node_data in self.graph.nodes(data=True):
            # 类型过滤
            if entity_types and node_data.get("type") not in entity_types:
                continue
            
            # 名称匹配
            name = node_data.get("name", "").lower()
            if query_lower in name or query_lower in node_id.lower():
                score = 1.0
                if name == query_lower:
                    score = 2.0
                elif name.startswith(query_lower):
                    score = 1.5
                
                result = dict(node_data)
                result["score"] = score
                results.append(result)
        
        # 按相关性排序
        results.sort(key=lambda x: x["score"], reverse=True)
        return results[:limit]
    
    async def recommend_entities(self, entity_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """推荐相关实体"""
        if entity_id not in self.graph.nodes:
            return []
        
        # 基于图结构的推荐算法
        recommendations = []
        
        # 获取直接邻居
        direct_neighbors = set(self.graph.neighbors(entity_id))
        
        # 获取二度邻居
        second_degree = set()
        for neighbor in direct_neighbors:
            second_degree.update(self.graph.neighbors(neighbor))
        
        # 移除自身和直接邻居
        second_degree.discard(entity_id)
        second_degree -= direct_neighbors
        
        # 计算推荐分数
        for candidate in second_degree:
            # 计算共同邻居数量
            common_neighbors = len(direct_neighbors & set(self.graph.neighbors(candidate)))
            
            # 计算节点重要性（度中心性）
            importance = self.graph.degree(candidate)
            
            score = common_neighbors * 0.7 + importance * 0.3
            
            candidate_data = dict(self.graph.nodes[candidate])
            candidate_data["recommendation_score"] = score
            recommendations.append(candidate_data)
        
        # 排序并返回
        recommendations.sort(key=lambda x: x["recommendation_score"], reverse=True)
        return recommendations[:limit]
    
    async def export_graph(self, format: str = "json") -> Dict[str, Any]:
        """导出图数据"""
        if format == "json":
            return {
                "entities": [
                    {"id": node_id, **node_data}
                    for node_id, node_data in self.graph.nodes(data=True)
                ],
                "relationships": [
                    {"from": from_id, "to": to_id, **edge_data}
                    for from_id, to_id, edge_data in self.graph.edges(data=True)
                ]
            }
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    async def close(self):
        """关闭连接"""
        if self.driver:
            await self.driver.close()

# 全局知识图谱实例
knowledge_graph = KnowledgeGraph()
