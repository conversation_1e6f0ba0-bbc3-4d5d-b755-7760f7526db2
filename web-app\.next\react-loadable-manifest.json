{"..\\..\\node_modules\\@antv\\g6-extension-react\\esm\\react-node\\render.js -> react-dom/client": {"id": "..\\..\\node_modules\\@antv\\g6-extension-react\\esm\\react-node\\render.js -> react-dom/client", "files": []}, "..\\..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/HH7B3BHX.js": {"id": "..\\..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/HH7B3BHX.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_js.js"]}, "..\\..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/JZI2RDCT.js": {"id": "..\\..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/JZI2RDCT.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_js.js"]}, "..\\..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}}