#!/usr/bin/env python3
"""
工业智能体平台开发环境启动脚本
用于启动所有必要的服务和基础设施
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path
import psutil
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
import requests

console = Console()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
os.chdir(PROJECT_ROOT)

# 服务配置
SERVICES = {
    "infrastructure": {
        "command": ["docker-compose", "up", "-d"],
        "description": "启动基础设施服务 (数据库、消息队列等)",
        "health_check": None,
        "process": None
    },
    "api-gateway": {
        "command": ["python", "services/api-gateway/main.py"],
        "description": "API网关服务",
        "health_check": "http://localhost:8000/health",
        "process": None,
        "port": 8000
    },
    "auth-service": {
        "command": ["python", "services/auth-service/main.py"],
        "description": "认证服务",
        "health_check": "http://localhost:8001/health",
        "process": None,
        "port": 8001
    }
}

# 全局变量存储进程
processes = []

def signal_handler(sig, frame):
    """信号处理器，用于优雅关闭"""
    console.print("\n[yellow]正在关闭所有服务...[/yellow]")
    cleanup()
    sys.exit(0)

def cleanup():
    """清理所有进程"""
    for service_name, config in SERVICES.items():
        if config["process"]:
            try:
                # 终止进程组
                if hasattr(config["process"], "pid"):
                    parent = psutil.Process(config["process"].pid)
                    children = parent.children(recursive=True)
                    for child in children:
                        child.terminate()
                    parent.terminate()
                    
                    # 等待进程结束
                    gone, still_alive = psutil.wait_procs(children + [parent], timeout=5)
                    for p in still_alive:
                        p.kill()
                        
                console.print(f"[green]✓[/green] {service_name} 已停止")
            except Exception as e:
                console.print(f"[red]✗[/red] 停止 {service_name} 时出错: {e}")

def check_port(port):
    """检查端口是否被占用"""
    for conn in psutil.net_connections():
        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
            return True
    return False

def wait_for_service(url, timeout=60):
    """等待服务启动"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(2)
    return False

def start_infrastructure():
    """启动基础设施"""
    console.print("[blue]启动基础设施服务...[/blue]")
    
    # 检查Docker是否运行
    try:
        subprocess.run(["docker", "version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        console.print("[red]错误: Docker未运行或未安装[/red]")
        return False
    
    # 启动Docker Compose
    try:
        process = subprocess.Popen(
            ["docker-compose", "up", "-d"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            console.print("[green]✓[/green] 基础设施服务启动成功")
            
            # 等待服务就绪
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("等待数据库就绪...", total=None)
                time.sleep(10)  # 等待数据库启动
                progress.update(task, description="数据库已就绪")
            
            return True
        else:
            console.print(f"[red]✗[/red] 基础设施启动失败: {stderr.decode()}")
            return False
            
    except Exception as e:
        console.print(f"[red]✗[/red] 启动基础设施时出错: {e}")
        return False

def start_service(service_name, config):
    """启动单个服务"""
    if service_name == "infrastructure":
        return start_infrastructure()
    
    # 检查端口是否被占用
    if "port" in config and check_port(config["port"]):
        console.print(f"[yellow]警告: 端口 {config['port']} 已被占用，跳过 {service_name}[/yellow]")
        return False
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env["PYTHONPATH"] = str(PROJECT_ROOT)
        
        # 启动进程
        process = subprocess.Popen(
            config["command"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
            preexec_fn=os.setsid if os.name != 'nt' else None
        )
        
        config["process"] = process
        
        # 等待服务启动
        if config.get("health_check"):
            console.print(f"[blue]等待 {service_name} 启动...[/blue]")
            if wait_for_service(config["health_check"]):
                console.print(f"[green]✓[/green] {service_name} 启动成功")
                return True
            else:
                console.print(f"[red]✗[/red] {service_name} 启动超时")
                return False
        else:
            time.sleep(2)  # 给服务一些启动时间
            if process.poll() is None:  # 进程仍在运行
                console.print(f"[green]✓[/green] {service_name} 启动成功")
                return True
            else:
                console.print(f"[red]✗[/red] {service_name} 启动失败")
                return False
                
    except Exception as e:
        console.print(f"[red]✗[/red] 启动 {service_name} 时出错: {e}")
        return False

def show_status():
    """显示服务状态"""
    table = Table(title="服务状态")
    table.add_column("服务名称", style="cyan")
    table.add_column("状态", style="green")
    table.add_column("端口", style="yellow")
    table.add_column("描述", style="white")
    
    for service_name, config in SERVICES.items():
        if service_name == "infrastructure":
            # 检查Docker容器状态
            try:
                result = subprocess.run(
                    ["docker-compose", "ps", "--services", "--filter", "status=running"],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0 and result.stdout.strip():
                    status = "🟢 运行中"
                else:
                    status = "🔴 已停止"
            except:
                status = "🔴 已停止"
            port = "多个端口"
        else:
            if config["process"] and config["process"].poll() is None:
                status = "🟢 运行中"
            else:
                status = "🔴 已停止"
            port = str(config.get("port", "N/A"))
        
        table.add_row(service_name, status, port, config["description"])
    
    console.print(table)

@click.group()
def cli():
    """工业智能体平台开发环境管理工具"""
    pass

@cli.command()
@click.option("--services", "-s", multiple=True, help="指定要启动的服务")
@click.option("--skip-infrastructure", is_flag=True, help="跳过基础设施启动")
def start(services, skip_infrastructure):
    """启动开发环境"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    console.print(Panel.fit(
        "[bold blue]工业智能体平台开发环境[/bold blue]\n"
        "正在启动服务...",
        border_style="blue"
    ))
    
    # 确定要启动的服务
    if services:
        services_to_start = {name: SERVICES[name] for name in services if name in SERVICES}
    else:
        services_to_start = SERVICES.copy()
        if skip_infrastructure:
            services_to_start.pop("infrastructure", None)
    
    # 按顺序启动服务
    success_count = 0
    total_count = len(services_to_start)
    
    for service_name, config in services_to_start.items():
        console.print(f"\n[blue]启动 {service_name}...[/blue]")
        if start_service(service_name, config):
            success_count += 1
        else:
            console.print(f"[red]服务 {service_name} 启动失败[/red]")
    
    # 显示启动结果
    console.print(f"\n[green]成功启动 {success_count}/{total_count} 个服务[/green]")
    
    if success_count > 0:
        console.print("\n[blue]服务访问地址:[/blue]")
        for service_name, config in services_to_start.items():
            if "port" in config:
                console.print(f"  • {service_name}: http://localhost:{config['port']}")
        
        console.print("\n[yellow]按 Ctrl+C 停止所有服务[/yellow]")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

@cli.command()
def stop():
    """停止所有服务"""
    console.print("[yellow]正在停止所有服务...[/yellow]")
    
    # 停止Docker容器
    try:
        subprocess.run(["docker-compose", "down"], check=True)
        console.print("[green]✓[/green] 基础设施服务已停止")
    except subprocess.CalledProcessError:
        console.print("[red]✗[/red] 停止基础设施服务失败")
    
    cleanup()
    console.print("[green]所有服务已停止[/green]")

@cli.command()
def status():
    """显示服务状态"""
    show_status()

@cli.command()
def logs():
    """查看服务日志"""
    try:
        subprocess.run(["docker-compose", "logs", "-f"], check=True)
    except KeyboardInterrupt:
        console.print("\n[yellow]日志查看已停止[/yellow]")

@cli.command()
def reset():
    """重置开发环境（清除所有数据）"""
    if click.confirm("这将删除所有数据，确定要继续吗？"):
        console.print("[yellow]正在重置开发环境...[/yellow]")
        
        # 停止并删除容器和卷
        try:
            subprocess.run(["docker-compose", "down", "-v"], check=True)
            console.print("[green]✓[/green] 已清除所有数据")
        except subprocess.CalledProcessError:
            console.print("[red]✗[/red] 重置失败")

if __name__ == "__main__":
    cli()
