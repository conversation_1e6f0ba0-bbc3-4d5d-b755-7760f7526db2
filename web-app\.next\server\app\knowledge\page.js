/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/knowledge/page";
exports.ids = ["app/knowledge/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fknowledge%2Fpage&page=%2Fknowledge%2Fpage&appPaths=%2Fknowledge%2Fpage&pagePath=private-next-app-dir%2Fknowledge%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fknowledge%2Fpage&page=%2Fknowledge%2Fpage&appPaths=%2Fknowledge%2Fpage&pagePath=private-next-app-dir%2Fknowledge%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/knowledge/page.tsx */ \"(rsc)/./src/app/knowledge/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'knowledge',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/knowledge/page\",\n        pathname: \"/knowledge\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fknowledge%2Fpage&page=%2Fknowledge%2Fpage&appPaths=%2Fknowledge%2Fpage&pagePath=private-next-app-dir%2Fknowledge%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/knowledge/page.tsx */ \"(rsc)/./src/app/knowledge/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tub3dsZWRnZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBOEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGtub3dsZWRnZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/knowledge/page.tsx":
/*!************************************!*\
  !*** ./src/app/knowledge/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\knowledge\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/knowledge/page.tsx */ \"(ssr)/./src/app/knowledge/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tub3dsZWRnZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBOEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGtub3dsZWRnZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cknowledge%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d15376eed6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXHdlYi1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBkMTUzNzZlZWQ2ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/knowledge/page.tsx":
/*!************************************!*\
  !*** ./src/app/knowledge/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/tabs/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/list/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/tree/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Form,Input,List,Modal,Row,Space,Table,Tabs,Tag,Tree,Upload,message!=!antd */ \"(ssr)/../node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/NodeIndexOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BulbOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/FilePdfOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/FileWordOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/FileExcelOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/FileTextOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/DownloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShareAltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=BulbOutlined,DownloadOutlined,EyeOutlined,FileExcelOutlined,FilePdfOutlined,FileTextOutlined,FileWordOutlined,NodeIndexOutlined,PlusOutlined,ReloadOutlined,SearchOutlined,ShareAltOutlined,UploadOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"(ssr)/../node_modules/styled-components/dist/styled-components.esm.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(ssr)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { TabPane } = _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { TextArea } = _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n// 样式组件\nconst KnowledgeContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  .knowledge-card {\n    background: var(--bg-card);\n    border: 1px solid var(--border-primary);\n    border-radius: 8px;\n    box-shadow: var(--shadow-card);\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: var(--shadow-hover);\n    }\n  }\n  \n  .file-icon {\n    font-size: 24px;\n    margin-right: 12px;\n    \n    &.pdf { color: #ff4757; }\n    &.word { color: #4a90e2; }\n    &.excel { color: #00d4aa; }\n    &.text { color: #ff8c42; }\n  }\n  \n  .knowledge-item {\n    padding: 16px;\n    border: 1px solid var(--border-primary);\n    border-radius: 8px;\n    margin-bottom: 12px;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      border-color: var(--color-accent-blue);\n      background: rgba(74, 144, 226, 0.05);\n    }\n  }\n`;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].h3`\n  color: var(--text-primary);\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &::before {\n    content: '';\n    width: 4px;\n    height: 16px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 2px;\n  }\n`;\nconst KnowledgePage = ()=>{\n    const [uploadModalVisible, setUploadModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [form] = _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    // 模拟文档数据\n    const mockDocuments = [\n        {\n            id: 'doc_001',\n            name: '生产工艺标准操作规程',\n            type: 'pdf',\n            category: '工艺文档',\n            size: '2.5MB',\n            author: '工艺工程师',\n            created_at: '2024-12-20',\n            updated_at: '2024-12-25',\n            downloads: 156,\n            tags: [\n                '工艺',\n                '标准',\n                'SOP'\n            ]\n        },\n        {\n            id: 'doc_002',\n            name: '设备维护手册',\n            type: 'word',\n            category: '维护文档',\n            size: '1.8MB',\n            author: '维护工程师',\n            created_at: '2024-12-18',\n            updated_at: '2024-12-22',\n            downloads: 89,\n            tags: [\n                '维护',\n                '设备',\n                '手册'\n            ]\n        },\n        {\n            id: 'doc_003',\n            name: '质量检验标准',\n            type: 'excel',\n            category: '质量文档',\n            size: '856KB',\n            author: '质量工程师',\n            created_at: '2024-12-15',\n            updated_at: '2024-12-20',\n            downloads: 234,\n            tags: [\n                '质量',\n                '检验',\n                '标准'\n            ]\n        },\n        {\n            id: 'doc_004',\n            name: '安全操作指南',\n            type: 'pdf',\n            category: '安全文档',\n            size: '3.2MB',\n            author: '安全工程师',\n            created_at: '2024-12-10',\n            updated_at: '2024-12-15',\n            downloads: 312,\n            tags: [\n                '安全',\n                '操作',\n                '指南'\n            ]\n        }\n    ];\n    // 知识图谱数据\n    const mockKnowledgeGraph = [\n        {\n            title: '生产工艺',\n            key: 'production',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 13\n            }, undefined),\n            children: [\n                {\n                    title: '冲压工艺',\n                    key: 'stamping',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 49\n                    }, undefined)\n                },\n                {\n                    title: '焊接工艺',\n                    key: 'welding',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 48\n                    }, undefined)\n                },\n                {\n                    title: '机加工艺',\n                    key: 'machining',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 50\n                    }, undefined)\n                },\n                {\n                    title: '装配工艺',\n                    key: 'assembly',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 49\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: '设备管理',\n            key: 'equipment',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 13\n            }, undefined),\n            children: [\n                {\n                    title: '数控机床',\n                    key: 'cnc',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 44\n                    }, undefined)\n                },\n                {\n                    title: '冲压设备',\n                    key: 'press',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 46\n                    }, undefined)\n                },\n                {\n                    title: '焊接设备',\n                    key: 'welding_eq',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 51\n                    }, undefined)\n                },\n                {\n                    title: '检测设备',\n                    key: 'inspection',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 51\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: '质量控制',\n            key: 'quality',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 13\n            }, undefined),\n            children: [\n                {\n                    title: '检验标准',\n                    key: 'standards',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 50\n                    }, undefined)\n                },\n                {\n                    title: '测量工具',\n                    key: 'tools',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 46\n                    }, undefined)\n                },\n                {\n                    title: '缺陷分析',\n                    key: 'defects',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 48\n                    }, undefined)\n                },\n                {\n                    title: '改进措施',\n                    key: 'improvements',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 53\n                    }, undefined)\n                }\n            ]\n        }\n    ];\n    // 智能搜索结果\n    const mockSearchResults = [\n        {\n            title: '如何提高冲压件质量？',\n            content: '冲压件质量控制需要从模具设计、材料选择、工艺参数设置等多个方面入手...',\n            type: '问答',\n            relevance: 95,\n            source: '质量管理手册'\n        },\n        {\n            title: '数控机床维护周期',\n            content: '数控机床的维护周期应根据使用频率、加工材料、环境条件等因素确定...',\n            type: '文档',\n            relevance: 88,\n            source: '设备维护规程'\n        },\n        {\n            title: '焊接缺陷预防措施',\n            content: '常见焊接缺陷包括气孔、夹渣、裂纹等，预防措施包括控制焊接参数...',\n            type: '案例',\n            relevance: 82,\n            source: '工艺改进案例集'\n        }\n    ];\n    // 文档表格列\n    const documentColumns = [\n        {\n            title: '文档名称',\n            key: 'document',\n            render: (_, record)=>{\n                const getFileIcon = (type)=>{\n                    const icons = {\n                        pdf: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"file-icon pdf\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 18\n                        }, undefined),\n                        word: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"file-icon word\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 19\n                        }, undefined),\n                        excel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"file-icon excel\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 20\n                        }, undefined),\n                        text: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"file-icon text\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 19\n                        }, undefined)\n                    };\n                    return icons[type] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"file-icon text\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 55\n                    }, undefined);\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        getFileIcon(record.type),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: 'var(--text-primary)',\n                                        fontWeight: 500\n                                    },\n                                    children: record.name\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '12px',\n                                        color: 'var(--text-muted)'\n                                    },\n                                    children: [\n                                        record.size,\n                                        \" | \",\n                                        record.category\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, undefined);\n            }\n        },\n        {\n            title: '标签',\n            dataIndex: 'tags',\n            key: 'tags',\n            render: (tags)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            color: \"blue\",\n                            style: {\n                                marginBottom: '2px'\n                            },\n                            children: tag\n                        }, tag, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '作者',\n            dataIndex: 'author',\n            key: 'author'\n        },\n        {\n            title: '更新时间',\n            dataIndex: 'updated_at',\n            key: 'updated_at',\n            render: (date)=>dayjs__WEBPACK_IMPORTED_MODULE_3___default()(date).format('MM-DD')\n        },\n        {\n            title: '下载次数',\n            dataIndex: 'downloads',\n            key: 'downloads',\n            render: (count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: 'var(--color-accent-blue)',\n                        fontWeight: 500\n                    },\n                    children: count\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 38\n                            }, void 0),\n                            type: \"primary\",\n                            children: \"查看\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 38\n                            }, void 0),\n                            children: \"下载\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 38\n                            }, void 0),\n                            children: \"分享\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 上传文档\n    const handleUploadDocument = async (values)=>{\n        try {\n            // 这里调用API上传文档\n            _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].success('文档上传成功！');\n            setUploadModalVisible(false);\n            form.resetFields();\n        } catch (error) {\n            _barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error('文档上传失败');\n        }\n    };\n    // 智能搜索\n    const handleSearch = (value)=>{\n        setSearchValue(value);\n        // 这里调用智能搜索API\n        console.log('搜索:', value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KnowledgeContainer, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '24px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '24px',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                children: \"知识管理\"\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        type: \"primary\",\n                                        onClick: ()=>setUploadModalVisible(true),\n                                        children: \"上传文档\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"刷新\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        gutter: [\n                            24,\n                            24\n                        ],\n                        style: {\n                            marginBottom: '32px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            span: 24,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"knowledge-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            marginBottom: '24px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'var(--text-primary)',\n                                                    marginBottom: '8px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        style: {\n                                                            marginRight: '8px',\n                                                            color: 'var(--color-accent-blue)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"智能知识搜索\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)'\n                                                },\n                                                children: \"支持自然语言查询，快速找到相关文档、工艺、案例等知识内容\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"请输入您要查找的内容，如：如何提高冲压件质量？\",\n                                        allowClear: true,\n                                        enterButton: \"智能搜索\",\n                                        size: \"large\",\n                                        onSearch: handleSearch,\n                                        style: {\n                                            maxWidth: '600px',\n                                            margin: '0 auto',\n                                            display: 'block'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    searchValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        gutter: [\n                            24,\n                            24\n                        ],\n                        style: {\n                            marginBottom: '32px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            span: 24,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"knowledge-card\",\n                                title: `搜索结果 - \"${searchValue}\"`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    dataSource: mockSearchResults,\n                                    renderItem: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Item, {\n                                            actions: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"link\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 64\n                                                    }, void 0),\n                                                    children: \"查看\"\n                                                }, \"view\", false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"link\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 65\n                                                    }, void 0),\n                                                    children: \"分享\"\n                                                }, \"share\", false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Item.Meta, {\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 49\n                                                    }, void 0),\n                                                    style: {\n                                                        background: 'var(--color-accent-green)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 35\n                                                }, void 0),\n                                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: 'var(--text-primary)',\n                                                                fontWeight: 500\n                                                            },\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            color: \"blue\",\n                                                            style: {\n                                                                marginLeft: '8px'\n                                                            },\n                                                            children: item.type\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: '12px',\n                                                                color: 'var(--color-accent-green)',\n                                                                marginLeft: '8px'\n                                                            },\n                                                            children: [\n                                                                \"相关度: \",\n                                                                item.relevance,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 31\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 29\n                                                }, void 0),\n                                                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                color: 'var(--text-secondary)',\n                                                                marginBottom: '4px'\n                                                            },\n                                                            children: item.content\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '12px',\n                                                                color: 'var(--text-muted)'\n                                                            },\n                                                            children: [\n                                                                \"来源: \",\n                                                                item.source\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 31\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 29\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 23\n                                        }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_29__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            className: \"knowledge-card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                defaultActiveKey: \"documents\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                                        tab: \"文档库\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            dataSource: mockDocuments,\n                                            columns: documentColumns,\n                                            rowKey: \"id\",\n                                            pagination: {\n                                                pageSize: 10,\n                                                showSizeChanger: true,\n                                                showQuickJumper: true,\n                                                showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, \"documents\", false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                                        tab: \"知识图谱\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            gutter: [\n                                                24,\n                                                24\n                                            ],\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    xs: 24,\n                                                    lg: 12,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            background: 'var(--bg-secondary)',\n                                                            padding: '16px',\n                                                            borderRadius: '8px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                style: {\n                                                                    color: 'var(--text-primary)',\n                                                                    marginBottom: '16px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        style: {\n                                                                            marginRight: '8px',\n                                                                            color: 'var(--color-accent-blue)'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"知识结构\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                showIcon: true,\n                                                                defaultExpandAll: true,\n                                                                treeData: mockKnowledgeGraph,\n                                                                style: {\n                                                                    background: 'transparent'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    xs: 24,\n                                                    lg: 12,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            background: 'var(--bg-secondary)',\n                                                            padding: '16px',\n                                                            borderRadius: '8px'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                style: {\n                                                                    color: 'var(--text-primary)',\n                                                                    marginBottom: '16px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        style: {\n                                                                            marginRight: '8px',\n                                                                            color: 'var(--color-accent-green)'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"知识关联\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"knowledge-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: 500,\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: \"冲压工艺 → 质量控制\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '14px',\n                                                                            color: 'var(--text-secondary)'\n                                                                        },\n                                                                        children: \"冲压工艺参数直接影响产品质量，需要严格控制压力、速度、温度等关键参数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"knowledge-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: 500,\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: \"设备维护 → 生产效率\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '14px',\n                                                                            color: 'var(--text-secondary)'\n                                                                        },\n                                                                        children: \"定期维护保养能够确保设备稳定运行，提高生产效率和产品质量\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"knowledge-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: 500,\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: \"检验标准 → 缺陷预防\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '14px',\n                                                                            color: 'var(--text-secondary)'\n                                                                        },\n                                                                        children: \"建立完善的检验标准体系，能够及早发现和预防质量缺陷\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, \"graph\", false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        title: \"上传文档\",\n                        open: uploadModalVisible,\n                        onCancel: ()=>{\n                            setUploadModalVisible(false);\n                            form.resetFields();\n                        },\n                        onOk: ()=>form.submit(),\n                        okText: \"上传\",\n                        cancelText: \"取消\",\n                        width: 600,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            form: form,\n                            layout: \"vertical\",\n                            onFinish: handleUploadDocument,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                    name: \"file\",\n                                    label: \"选择文件\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请选择要上传的文件'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"].Dragger, {\n                                        name: \"file\",\n                                        multiple: false,\n                                        beforeUpload: ()=>false,\n                                        style: {\n                                            background: 'var(--bg-secondary)'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"ant-upload-drag-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BulbOutlined_DownloadOutlined_EyeOutlined_FileExcelOutlined_FilePdfOutlined_FileTextOutlined_FileWordOutlined_NodeIndexOutlined_PlusOutlined_ReloadOutlined_SearchOutlined_ShareAltOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    style: {\n                                                        color: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"ant-upload-text\",\n                                                style: {\n                                                    color: 'var(--text-primary)'\n                                                },\n                                                children: \"点击或拖拽文件到此区域上传\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"ant-upload-hint\",\n                                                style: {\n                                                    color: 'var(--text-secondary)'\n                                                },\n                                                children: \"支持 PDF、Word、Excel、PowerPoint 等格式\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                    name: \"name\",\n                                    label: \"文档名称\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请输入文档名称'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        placeholder: \"请输入文档名称\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                    name: \"category\",\n                                    label: \"文档分类\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请选择文档分类'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        placeholder: \"请输入文档分类\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                    name: \"tags\",\n                                    label: \"标签\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        placeholder: \"请输入标签，用逗号分隔\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Form_Input_List_Modal_Row_Space_Table_Tabs_Tag_Tree_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                    name: \"description\",\n                                    label: \"文档描述\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                        rows: 4,\n                                        placeholder: \"请输入文档描述\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n            lineNumber: 313,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\knowledge\\\\page.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (KnowledgePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/knowledge/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/app/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/../node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(ssr)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/locale/zh-cn */ \"(ssr)/../node_modules/dayjs/locale/zh-cn.js\");\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/relativeTime */ \"(ssr)/../node_modules/dayjs/plugin/relativeTime.js\");\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/utc */ \"(ssr)/../node_modules/dayjs/plugin/utc.js\");\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/timezone */ \"(ssr)/../node_modules/dayjs/plugin/timezone.js\");\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// 配置dayjs\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().locale('zh-cn');\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default()));\n// 创建QueryClient实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 3,\n            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000\n        },\n        mutations: {\n            retry: 1\n        }\n    }\n});\n// Ant Design工业风格主题配置\nconst antdTheme = {\n    token: {\n        colorPrimary: '#4a90e2',\n        colorSuccess: '#00d4aa',\n        colorWarning: '#ff8c42',\n        colorError: '#ff4757',\n        colorInfo: '#4a90e2',\n        colorBgBase: '#0f0f0f',\n        colorBgContainer: '#1a1a1a',\n        colorBgElevated: '#242424',\n        colorBorder: '#333333',\n        colorText: '#ffffff',\n        colorTextSecondary: '#b0b0b0',\n        borderRadius: 8,\n        wireframe: false\n    },\n    components: {\n        Layout: {\n            headerBg: '#1a1a1a',\n            siderBg: '#1a1a1a',\n            bodyBg: '#0f0f0f'\n        },\n        Menu: {\n            darkItemBg: 'transparent',\n            darkSubMenuItemBg: '#242424',\n            darkItemSelectedBg: 'rgba(74, 144, 226, 0.1)',\n            darkItemColor: '#b0b0b0',\n            darkItemSelectedColor: '#4a90e2',\n            darkItemHoverColor: '#4a90e2'\n        },\n        Card: {\n            headerBg: '#242424',\n            colorBgContainer: '#242424'\n        },\n        Table: {\n            headerBg: '#1a1a1a',\n            colorBgContainer: '#242424'\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"基于大语言模型的工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                            theme: antdTheme,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ToolOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MonitorOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"(ssr)/../node_modules/styled-components/dist/styled-components.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n// 样式组件\nconst StyledLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"]))`\n  min-height: 100vh;\n  background: var(--bg-primary);\n`;\nconst StyledHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Header)`\n  background: var(--bg-secondary);\n  border-bottom: 1px solid var(--border-primary);\n  padding: 0 24px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: linear-gradient(90deg, \n      transparent 0%, \n      var(--color-accent-blue) 50%, \n      transparent 100%);\n    opacity: 0.6;\n  }\n`;\nconst StyledSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Sider)`\n  background: var(--bg-secondary);\n  border-right: 1px solid var(--border-primary);\n  \n  .ant-layout-sider-trigger {\n    background: var(--bg-primary);\n    border-top: 1px solid var(--border-primary);\n    color: var(--text-secondary);\n    \n    &:hover {\n      background: var(--color-accent-blue);\n      color: white;\n    }\n  }\n`;\nconst StyledContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Content)`\n  background: var(--bg-primary);\n  padding: 24px;\n  overflow-y: auto;\n`;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 20px;\n  font-weight: 700;\n  color: var(--text-primary);\n  \n  .logo-icon {\n    width: 32px;\n    height: 32px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 6px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: white;\n    font-size: 16px;\n  }\n`;\nconst StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  box-shadow: 0 0 10px ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  animation: pulse 2s infinite;\n`;\nconst HeaderActions = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n`;\n// 菜单配置\nconst menuItems = [\n    {\n        key: '/',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 130,\n            columnNumber: 11\n        }, undefined),\n        label: '总览仪表板',\n        path: '/'\n    },\n    {\n        key: '/production',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 136,\n            columnNumber: 11\n        }, undefined),\n        label: '生产管理',\n        path: '/production'\n    },\n    {\n        key: '/maintenance',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 142,\n            columnNumber: 11\n        }, undefined),\n        label: '设备维护',\n        path: '/maintenance'\n    },\n    {\n        key: '/quality',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 148,\n            columnNumber: 11\n        }, undefined),\n        label: '质量管理',\n        path: '/quality'\n    },\n    {\n        key: '/supply-chain',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 154,\n            columnNumber: 11\n        }, undefined),\n        label: '供应链',\n        path: '/supply-chain'\n    },\n    {\n        key: '/knowledge',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 160,\n            columnNumber: 11\n        }, undefined),\n        label: '知识管理',\n        path: '/knowledge'\n    },\n    {\n        key: '/agents',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 166,\n            columnNumber: 11\n        }, undefined),\n        label: '智能体',\n        path: '/agents',\n        children: [\n            {\n                key: '/agents/chat',\n                label: '智能对话',\n                path: '/agents/chat'\n            },\n            {\n                key: '/agents/orchestration',\n                label: '智能体编排',\n                path: '/agents/orchestration'\n            }\n        ]\n    },\n    {\n        key: '/monitoring',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 176,\n            columnNumber: 11\n        }, undefined),\n        label: '系统监控',\n        path: '/monitoring'\n    }\n];\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('online');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 模拟系统状态检查\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MainLayout.useEffect\": ()=>{\n            const checkSystemStatus = {\n                \"MainLayout.useEffect.checkSystemStatus\": ()=>{\n                    // 这里可以调用实际的健康检查API\n                    const statuses = [\n                        'online',\n                        'warning',\n                        'offline'\n                    ];\n                    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];\n                    setSystemStatus(randomStatus);\n                }\n            }[\"MainLayout.useEffect.checkSystemStatus\"];\n            const interval = setInterval(checkSystemStatus, 30000); // 30秒检查一次\n            return ({\n                \"MainLayout.useEffect\": ()=>clearInterval(interval)\n            })[\"MainLayout.useEffect\"];\n        }\n    }[\"MainLayout.useEffect\"], []);\n    // 用户菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined),\n            label: '个人资料'\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 214,\n                columnNumber: 13\n            }, undefined),\n            label: '系统设置'\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 222,\n                columnNumber: 13\n            }, undefined),\n            label: '退出登录',\n            danger: true\n        }\n    ];\n    // 构建菜单项\n    const buildMenuItems = (items)=>{\n        return items.map((item)=>({\n                key: item.key,\n                icon: item.icon,\n                label: item.children ? item.label : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.path,\n                    style: {\n                        color: 'inherit',\n                        textDecoration: 'none'\n                    },\n                    children: item.label\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined),\n                children: item.children?.map((child)=>({\n                        key: child.key,\n                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: child.path,\n                            style: {\n                                color: 'inherit',\n                                textDecoration: 'none'\n                            },\n                            children: child.label\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined)\n                    }))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledSider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 240,\n                collapsedWidth: 80,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        style: {\n                            padding: '16px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"logo-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                                    children: !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            width: 'auto'\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"工业智能体\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        selectedKeys: [\n                            pathname\n                        ],\n                        defaultOpenKeys: [\n                            '/production',\n                            '/maintenance',\n                            '/quality'\n                        ],\n                        items: buildMenuItems(menuItems),\n                        style: {\n                            border: 'none'\n                        }\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: ()=>setCollapsed(!collapsed),\n                                        style: {\n                                            fontSize: '16px',\n                                            width: 40,\n                                            height: 40,\n                                            color: 'var(--text-secondary)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIndicator, {\n                                                status: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)',\n                                                    fontSize: '14px'\n                                                },\n                                                children: [\n                                                    \"系统状态: \",\n                                                    systemStatus === 'online' ? '正常' : systemStatus === 'warning' ? '警告' : '离线'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderActions, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                color: 'var(--text-secondary)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        trigger: [\n                                            'click'\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            style: {\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        background: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: 'var(--text-primary)'\n                                                    },\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/rc-picker","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-util","vendor-chunks/@babel","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/dayjs","vendor-chunks/rc-dialog","vendor-chunks/rc-collapse","vendor-chunks/@emotion","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/rc-menu","vendor-chunks/styled-components","vendor-chunks/resize-observer-polyfill","vendor-chunks/stylis","vendor-chunks/rc-overflow","vendor-chunks/motion-utils","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/shallowequal","vendor-chunks/rc-tabs","vendor-chunks/rc-input","vendor-chunks/throttle-debounce","vendor-chunks/rc-table","vendor-chunks/rc-select","vendor-chunks/rc-tree","vendor-chunks/rc-virtual-list","vendor-chunks/rc-textarea","vendor-chunks/rc-progress","vendor-chunks/rc-checkbox","vendor-chunks/compute-scroll-into-view","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/rc-upload"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fknowledge%2Fpage&page=%2Fknowledge%2Fpage&appPaths=%2Fknowledge%2Fpage&pagePath=private-next-app-dir%2Fknowledge%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();