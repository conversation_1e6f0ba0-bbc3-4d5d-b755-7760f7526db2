# 工业智能体平台 - 快速开始指南

## 项目概述

本项目是一个基于大语言模型（LLM）的工业智能体平台，专为汽车零部件企业设计，提供智能排产、预测维护、质量检验、供应链优化等核心功能。

## 系统架构

### 五层架构设计

1. **数据层（Data Layer）** ✅ 已完成
   - 多源数据接入：MES、ERP、SCADA、PLC、CAD/CAE、检测设备
   - 数据预处理与清洗管道
   - 分布式数据湖/仓库（PostgreSQL + TimescaleDB + MongoDB + Redis）

2. **知识层（Knowledge Layer）** ✅ 已完成
   - 行业知识库管理
   - 产品数字孪生模型
   - 知识图谱系统（基于NetworkX和Neo4j）

3. **模型层（Model Layer）** ✅ 已完成
   - 基础大语言模型集成（OpenAI、Anthropic、本地模型）
   - 领域微调支持
   - 专项算法模块（排产优化、预测维护、质量检测）

4. **智能体编排层（Agent Orchestration）** ✅ 已完成
   - 多智能体框架
   - 工作流引擎
   - 事件驱动机制

5. **交互层（Interaction Layer）** ✅ 已完成
   - Web前端应用（Next.js + React + Ant Design）
   - API网关服务
   - 实时数据可视化

## 技术栈

### 后端服务
- **框架**: FastAPI (Python)
- **数据库**: PostgreSQL + TimescaleDB + Redis + MongoDB
- **消息队列**: Apache Kafka
- **搜索引擎**: Elasticsearch
- **向量数据库**: Weaviate
- **容器化**: Docker + Docker Compose

### 前端应用
- **框架**: Next.js 14 + React 18 + TypeScript
- **UI组件**: Ant Design 5.x
- **图表库**: @ant-design/charts + ECharts
- **状态管理**: Zustand + React Query

### AI/ML 技术栈
- **LLM**: OpenAI GPT / Anthropic Claude / 本地模型
- **ML框架**: PyTorch + Transformers + scikit-learn
- **嵌入模型**: Sentence Transformers

## 快速启动

### 环境要求

- Docker & Docker Compose
- Python 3.9+
- Node.js 18+
- 至少 8GB RAM
- 20GB 可用磁盘空间

### 1. 克隆项目

```bash
git clone <repository-url>
cd industry-ai-platform
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，设置必要的API密钥和数据库连接
nano .env
```

### 3. 启动基础设施

```bash
# 启动数据库、消息队列等基础服务
docker-compose up -d
```

### 4. 安装Python依赖

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 5. 启动后端服务

```bash
# 启动API网关
cd services/api-gateway
python main.py &

# 启动认证服务
cd ../auth-service
python main.py &

# 启动数据接入服务
cd ../data-ingestion
python main.py &

# 启动知识管理服务
cd ../knowledge-service
python main.py &

# 启动LLM服务
cd ../llm-service
python main.py &

# 启动智能体编排服务
cd ../agent-orchestrator
python main.py &
```

### 6. 启动前端应用

```bash
cd web-app

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 7. 使用开发脚本（推荐）

```bash
# 使用Python脚本一键启动所有服务
python scripts/start-dev.py start

# 查看服务状态
python scripts/start-dev.py status

# 停止所有服务
python scripts/start-dev.py stop
```

## 服务访问地址

启动成功后，可以通过以下地址访问各个服务：

- **Web前端**: http://localhost:3000
- **API网关**: http://localhost:8000
- **认证服务**: http://localhost:8001
- **LLM服务**: http://localhost:8002
- **智能体编排**: http://localhost:8003
- **生产计划服务**: http://localhost:8004
- **维护服务**: http://localhost:8005
- **质量服务**: http://localhost:8006
- **供应链服务**: http://localhost:8007
- **知识服务**: http://localhost:8008
- **数据接入服务**: http://localhost:8009

### 监控和管理界面

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Elasticsearch**: http://localhost:9200
- **Kibana**: http://localhost:5601
- **Weaviate**: http://localhost:8080
- **MinIO**: http://localhost:9001 (admin/admin123)

## 核心功能演示

### 1. 智能对话

访问 http://localhost:3000/agents/chat 体验与工业智能体的对话功能。

### 2. 生产监控

访问 http://localhost:3000/production/monitoring 查看实时生产数据和设备状态。

### 3. 预测维护

访问 http://localhost:3000/maintenance/predictive 查看设备健康状态和维护建议。

### 4. 质量检验

访问 http://localhost:3000/quality/inspection 进行质量检测和分析。

### 5. 知识图谱

访问 http://localhost:3000/knowledge/graph 浏览工业知识图谱。

## API文档

各个服务的API文档可以通过以下地址访问：

- API网关: http://localhost:8000/docs
- 认证服务: http://localhost:8001/docs
- LLM服务: http://localhost:8002/docs
- 智能体编排: http://localhost:8003/docs
- 知识服务: http://localhost:8008/docs
- 数据接入: http://localhost:8009/docs

## 开发指南

### 添加新的智能体

1. 在 `services/` 目录下创建新的服务
2. 实现智能体接口
3. 在智能体编排服务中注册
4. 更新前端界面

### 添加新的数据源

1. 在数据接入服务中添加新的连接器
2. 实现数据预处理逻辑
3. 配置数据映射规则
4. 测试数据接入流程

### 自定义工作流

1. 使用工作流编辑器创建新流程
2. 定义任务节点和依赖关系
3. 配置触发条件
4. 测试工作流执行

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认环境变量配置正确
   - 查看服务日志

2. **数据库连接失败**
   - 确认Docker容器正常运行
   - 检查数据库连接配置
   - 验证网络连通性

3. **前端页面无法加载**
   - 确认后端服务正常运行
   - 检查API网关配置
   - 查看浏览器控制台错误

### 日志查看

```bash
# 查看Docker容器日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f postgres
docker-compose logs -f kafka

# 查看Python服务日志
tail -f logs/api-gateway.log
tail -f logs/llm-service.log
```

## 下一步开发

当前平台已完成核心架构和基础功能，后续可以继续开发：

1. **核心功能模块实现** - 完善智能排产、预测维护等具体业务功能
2. **安全与运维系统** - 加强安全防护和运维监控能力
3. **移动端应用** - 开发移动端应用支持现场操作
4. **高级分析功能** - 增加更多数据分析和AI能力
5. **第三方系统集成** - 与更多工业系统进行集成

## 技术支持

如有问题，请查看：
- 项目文档: `docs/` 目录
- API文档: 各服务的 `/docs` 端点
- 架构设计: `docs/architecture.md`
- 常见问题: `docs/faq.md`

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
