"""
工业智能体平台 - 生产计划服务
实现智能排产、生产监控、产能分析等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import numpy as np
import pandas as pd
from dataclasses import dataclass
import uuid
from enum import Enum

# 导入算法模块
import sys
sys.path.append('../llm-service')
from algorithms import ProductionScheduler, ProductionOrder, Equipment, ScheduleItem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
scheduler = ProductionScheduler()

class OrderStatus(str, Enum):
    PLANNED = "planned"
    RELEASED = "released"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"

class EquipmentStatus(str, Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    MAINTENANCE = "maintenance"
    BREAKDOWN = "breakdown"
    OFFLINE = "offline"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化数据库表
    await initialize_database()
    
    logger.info("Production planning service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Production planning service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="生产计划服务",
    description="智能排产、生产监控、产能分析",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class ProductionOrderCreate(BaseModel):
    product_id: str
    quantity: int
    priority: int = 5
    due_date: datetime
    customer_order_id: Optional[str] = None
    notes: Optional[str] = None

class ProductionOrderUpdate(BaseModel):
    quantity: Optional[int] = None
    priority: Optional[int] = None
    due_date: Optional[datetime] = None
    status: Optional[OrderStatus] = None
    notes: Optional[str] = None

class SchedulingRequest(BaseModel):
    order_ids: List[str]
    start_time: Optional[datetime] = None
    constraints: Dict[str, Any] = {}
    optimization_objectives: List[str] = ["minimize_makespan", "minimize_tardiness"]

class CapacityAnalysisRequest(BaseModel):
    start_date: datetime
    end_date: datetime
    product_mix: Dict[str, int]  # product_id -> quantity
    equipment_availability: Optional[Dict[str, float]] = None

class ProductionMetrics(BaseModel):
    date: datetime
    total_output: int
    target_output: int
    efficiency: float
    oee: float
    quality_rate: float
    downtime_minutes: int

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 生产线表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS production_lines (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                description TEXT,
                capacity_per_hour INTEGER,
                efficiency DECIMAL(3,2) DEFAULT 0.85,
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 工作中心表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS work_centers (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                production_line_id VARCHAR(50) REFERENCES production_lines(id),
                equipment_ids TEXT[],
                capacity_per_hour INTEGER,
                setup_time_minutes INTEGER DEFAULT 30,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 排产结果表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS production_schedules (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                production_order_id UUID NOT NULL REFERENCES production_orders(id),
                work_center_id VARCHAR(50) REFERENCES work_centers(id),
                equipment_id VARCHAR(50),
                sequence_no INTEGER NOT NULL,
                planned_start_time TIMESTAMP WITH TIME ZONE,
                planned_end_time TIMESTAMP WITH TIME ZONE,
                actual_start_time TIMESTAMP WITH TIME ZONE,
                actual_end_time TIMESTAMP WITH TIME ZONE,
                status VARCHAR(20) DEFAULT 'scheduled',
                operator_id UUID REFERENCES users(id),
                setup_time_minutes INTEGER DEFAULT 0,
                processing_time_minutes INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 生产实绩表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS production_actuals (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                production_order_id UUID NOT NULL REFERENCES production_orders(id),
                work_center_id VARCHAR(50) REFERENCES work_centers(id),
                equipment_id VARCHAR(50),
                operator_id UUID REFERENCES users(id),
                start_time TIMESTAMP WITH TIME ZONE,
                end_time TIMESTAMP WITH TIME ZONE,
                quantity_produced INTEGER,
                quantity_good INTEGER,
                quantity_defective INTEGER,
                downtime_minutes INTEGER DEFAULT 0,
                downtime_reason TEXT,
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 产能分析表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS capacity_analysis (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                analysis_date DATE,
                work_center_id VARCHAR(50) REFERENCES work_centers(id),
                available_hours DECIMAL(5,2),
                planned_hours DECIMAL(5,2),
                actual_hours DECIMAL(5,2),
                utilization_rate DECIMAL(3,2),
                efficiency_rate DECIMAL(3,2),
                oee DECIMAL(3,2),
                bottleneck_score DECIMAL(3,2),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

class ProductionPlanningManager:
    """生产计划管理器"""
    
    @staticmethod
    async def create_production_order(order_data: ProductionOrderCreate) -> str:
        """创建生产订单"""
        try:
            order_id = str(uuid.uuid4())
            
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO production_orders 
                    (id, order_number, product_id, quantity, priority, status, 
                     planned_start_date, planned_end_date, customer_order_id, notes, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    """,
                    order_id,
                    f"PO{datetime.now().strftime('%Y%m%d')}{order_id[:8]}",
                    order_data.product_id,
                    order_data.quantity,
                    order_data.priority,
                    OrderStatus.PLANNED.value,
                    None,  # 排产后确定
                    order_data.due_date,
                    order_data.customer_order_id,
                    order_data.notes,
                    datetime.utcnow()
                )
            
            # 发送事件
            await kafka_producer.send("production_events", {
                "type": "order_created",
                "order_id": order_id,
                "product_id": order_data.product_id,
                "quantity": order_data.quantity,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Created production order: {order_id}")
            return order_id
            
        except Exception as e:
            logger.error(f"Error creating production order: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def schedule_orders(request: SchedulingRequest) -> Dict[str, Any]:
        """智能排产"""
        try:
            # 获取订单信息
            orders = []
            async with db_pool.acquire() as conn:
                for order_id in request.order_ids:
                    row = await conn.fetchrow(
                        """
                        SELECT po.*, p.product_name, p.specifications
                        FROM production_orders po
                        JOIN products p ON po.product_id = p.product_code
                        WHERE po.id = $1
                        """,
                        order_id
                    )
                    
                    if row:
                        # 估算加工时间
                        estimated_duration = await ProductionPlanningManager._estimate_processing_time(
                            row['product_id'], row['quantity']
                        )
                        
                        # 获取所需设备能力
                        required_equipment = await ProductionPlanningManager._get_required_equipment(
                            row['product_id']
                        )
                        
                        order = ProductionOrder(
                            id=str(row['id']),
                            product_id=row['product_id'],
                            quantity=row['quantity'],
                            priority=row['priority'],
                            due_date=row['planned_end_date'],
                            estimated_duration=estimated_duration,
                            required_equipment=required_equipment
                        )
                        orders.append(order)
            
            # 获取设备信息
            equipment_list = await ProductionPlanningManager._get_available_equipment()
            
            # 执行排产算法
            start_time = request.start_time or datetime.utcnow()
            schedule = scheduler.optimize(orders, equipment_list, start_time)
            
            # 保存排产结果
            schedule_id = await ProductionPlanningManager._save_schedule(schedule)
            
            # 更新订单状态
            for item in schedule:
                async with db_pool.acquire() as conn:
                    await conn.execute(
                        """
                        UPDATE production_orders 
                        SET status = $1, planned_start_date = $2, planned_end_date = $3
                        WHERE id = $4
                        """,
                        OrderStatus.RELEASED.value,
                        item.start_time,
                        item.end_time,
                        item.order_id
                    )
            
            # 计算排产指标
            metrics = ProductionPlanningManager._calculate_schedule_metrics(schedule, orders)
            
            # 发送事件
            await kafka_producer.send("production_events", {
                "type": "schedule_created",
                "schedule_id": schedule_id,
                "order_count": len(orders),
                "metrics": metrics,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            return {
                "schedule_id": schedule_id,
                "scheduled_orders": len(orders),
                "total_items": len(schedule),
                "metrics": metrics,
                "schedule": [
                    {
                        "order_id": item.order_id,
                        "equipment_id": item.equipment_id,
                        "start_time": item.start_time.isoformat(),
                        "end_time": item.end_time.isoformat(),
                        "setup_time": item.setup_time
                    }
                    for item in schedule
                ]
            }
            
        except Exception as e:
            logger.error(f"Error scheduling orders: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _estimate_processing_time(product_id: str, quantity: int) -> int:
        """估算加工时间（分钟）"""
        # 从工艺路线获取标准工时
        async with db_pool.acquire() as conn:
            result = await conn.fetchval(
                """
                SELECT SUM(p.standard_time) 
                FROM routing r
                JOIN processes p ON r.process_id = p.id
                WHERE r.product_id = $1
                """,
                product_id
            )
            
            if result:
                standard_time_per_unit = result  # 秒
                return int((standard_time_per_unit * quantity) / 60)  # 转换为分钟
            else:
                # 默认估算：每件10分钟
                return quantity * 10
    
    @staticmethod
    async def _get_required_equipment(product_id: str) -> List[str]:
        """获取产品所需设备能力"""
        async with db_pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT DISTINCT p.process_type
                FROM routing r
                JOIN processes p ON r.process_id = p.id
                WHERE r.product_id = $1
                ORDER BY r.sequence_no
                """,
                product_id
            )
            
            return [row['process_type'] for row in rows]
    
    @staticmethod
    async def _get_available_equipment() -> List[Equipment]:
        """获取可用设备列表"""
        equipment_list = []
        
        async with db_pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT id, equipment_name, equipment_type, status, specifications
                FROM equipment
                WHERE status IN ('active', 'idle')
                ORDER BY id
                """
            )
            
            for row in rows:
                # 解析设备能力
                specs = json.loads(row['specifications']) if row['specifications'] else {}
                capabilities = specs.get('capabilities', [row['equipment_type']])
                
                equipment = Equipment(
                    id=row['id'],
                    name=row['equipment_name'],
                    capabilities=capabilities,
                    efficiency=specs.get('efficiency', 0.85),
                    current_status="available"
                )
                equipment_list.append(equipment)
        
        return equipment_list
    
    @staticmethod
    async def _save_schedule(schedule: List[ScheduleItem]) -> str:
        """保存排产结果"""
        schedule_id = str(uuid.uuid4())
        
        async with db_pool.acquire() as conn:
            for i, item in enumerate(schedule):
                await conn.execute(
                    """
                    INSERT INTO production_schedules 
                    (production_order_id, equipment_id, sequence_no, 
                     planned_start_time, planned_end_time, setup_time_minutes, status)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    """,
                    item.order_id,
                    item.equipment_id,
                    i + 1,
                    item.start_time,
                    item.end_time,
                    item.setup_time,
                    "scheduled"
                )
        
        return schedule_id
    
    @staticmethod
    def _calculate_schedule_metrics(schedule: List[ScheduleItem], orders: List[ProductionOrder]) -> Dict[str, Any]:
        """计算排产指标"""
        if not schedule:
            return {}
        
        # 计算完工时间
        makespan = max(item.end_time for item in schedule) - min(item.start_time for item in schedule)
        
        # 计算延期情况
        order_dict = {order.id: order for order in orders}
        total_tardiness = 0
        late_orders = 0
        
        for item in schedule:
            order = order_dict.get(item.order_id)
            if order and item.end_time > order.due_date:
                tardiness = (item.end_time - order.due_date).total_seconds() / 3600
                total_tardiness += tardiness
                late_orders += 1
        
        # 计算设备利用率
        equipment_usage = {}
        total_time = makespan.total_seconds()
        
        for item in schedule:
            if item.equipment_id not in equipment_usage:
                equipment_usage[item.equipment_id] = 0
            equipment_usage[item.equipment_id] += (item.end_time - item.start_time).total_seconds()
        
        avg_utilization = sum(equipment_usage.values()) / (len(equipment_usage) * total_time) if equipment_usage else 0
        
        return {
            "makespan_hours": makespan.total_seconds() / 3600,
            "total_tardiness_hours": total_tardiness,
            "late_orders": late_orders,
            "on_time_rate": (len(orders) - late_orders) / len(orders) if orders else 0,
            "average_utilization": avg_utilization,
            "equipment_count": len(equipment_usage)
        }
    
    @staticmethod
    async def analyze_capacity(request: CapacityAnalysisRequest) -> Dict[str, Any]:
        """产能分析"""
        try:
            # 获取工作中心信息
            async with db_pool.acquire() as conn:
                work_centers = await conn.fetch(
                    "SELECT * FROM work_centers WHERE status = 'active'"
                )
            
            analysis_results = []
            total_capacity = 0
            total_demand = 0
            bottlenecks = []
            
            for wc in work_centers:
                # 计算可用产能
                days = (request.end_date - request.start_date).days
                available_hours = days * 24 * 0.85  # 假设85%可用率
                capacity_per_hour = wc['capacity_per_hour'] or 10
                available_capacity = available_hours * capacity_per_hour
                
                # 计算需求
                demand = 0
                for product_id, quantity in request.product_mix.items():
                    # 检查该工作中心是否需要处理此产品
                    processing_time = await ProductionPlanningManager._estimate_processing_time(
                        product_id, quantity
                    )
                    demand += processing_time / 60  # 转换为小时
                
                # 计算利用率
                utilization = min(demand / available_hours, 1.0) if available_hours > 0 else 0
                
                # 识别瓶颈
                if utilization > 0.9:
                    bottlenecks.append({
                        "work_center": wc['name'],
                        "utilization": utilization,
                        "capacity_shortage": demand - available_capacity
                    })
                
                analysis_results.append({
                    "work_center_id": wc['id'],
                    "work_center_name": wc['name'],
                    "available_capacity": available_capacity,
                    "demand": demand,
                    "utilization": utilization,
                    "is_bottleneck": utilization > 0.9
                })
                
                total_capacity += available_capacity
                total_demand += demand
            
            # 整体分析
            overall_utilization = total_demand / total_capacity if total_capacity > 0 else 0
            
            return {
                "analysis_period": {
                    "start_date": request.start_date.isoformat(),
                    "end_date": request.end_date.isoformat()
                },
                "overall_utilization": overall_utilization,
                "total_capacity": total_capacity,
                "total_demand": total_demand,
                "capacity_surplus": total_capacity - total_demand,
                "work_centers": analysis_results,
                "bottlenecks": bottlenecks,
                "recommendations": ProductionPlanningManager._generate_capacity_recommendations(
                    overall_utilization, bottlenecks
                )
            }
            
        except Exception as e:
            logger.error(f"Error analyzing capacity: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    def _generate_capacity_recommendations(utilization: float, bottlenecks: List[Dict]) -> List[str]:
        """生成产能优化建议"""
        recommendations = []
        
        if utilization > 0.95:
            recommendations.append("整体产能利用率过高，建议增加设备或延长工作时间")
        elif utilization < 0.6:
            recommendations.append("产能利用率较低，可以考虑接受更多订单或优化排产")
        
        if bottlenecks:
            recommendations.append(f"发现{len(bottlenecks)}个瓶颈工作中心，建议优先优化这些环节")
            for bottleneck in bottlenecks:
                recommendations.append(
                    f"工作中心'{bottleneck['work_center']}'利用率{bottleneck['utilization']:.1%}，"
                    f"建议增加产能或重新分配工作负荷"
                )
        
        return recommendations

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "production-planning"}

@app.post("/orders")
async def create_order(order_data: ProductionOrderCreate):
    """创建生产订单"""
    order_id = await ProductionPlanningManager.create_production_order(order_data)
    return {"message": "Production order created", "order_id": order_id}

@app.get("/orders")
async def list_orders(
    status: Optional[OrderStatus] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取生产订单列表"""
    async with db_pool.acquire() as conn:
        query = """
            SELECT po.*, p.product_name 
            FROM production_orders po
            LEFT JOIN products p ON po.product_id = p.product_code
        """
        params = []
        
        if status:
            query += " WHERE po.status = $1"
            params.append(status.value)
        
        query += " ORDER BY po.created_at DESC LIMIT $" + str(len(params) + 1) + " OFFSET $" + str(len(params) + 2)
        params.extend([limit, offset])
        
        rows = await conn.fetch(query, *params)
        return [dict(row) for row in rows]

@app.put("/orders/{order_id}")
async def update_order(order_id: str, update_data: ProductionOrderUpdate):
    """更新生产订单"""
    async with db_pool.acquire() as conn:
        # 构建更新字段
        update_fields = []
        params = []
        param_count = 1
        
        for field, value in update_data.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = ${param_count}")
                params.append(value.value if isinstance(value, Enum) else value)
                param_count += 1
        
        if update_fields:
            update_fields.append(f"updated_at = ${param_count}")
            params.append(datetime.utcnow())
            params.append(order_id)
            
            query = f"UPDATE production_orders SET {', '.join(update_fields)} WHERE id = ${param_count + 1}"
            await conn.execute(query, *params)
    
    return {"message": "Production order updated"}

@app.post("/schedule")
async def schedule_orders(request: SchedulingRequest):
    """执行智能排产"""
    result = await ProductionPlanningManager.schedule_orders(request)
    return result

@app.post("/capacity/analyze")
async def analyze_capacity(request: CapacityAnalysisRequest):
    """产能分析"""
    result = await ProductionPlanningManager.analyze_capacity(request)
    return result

@app.get("/metrics/daily")
async def get_daily_metrics(date: Optional[str] = None):
    """获取日生产指标"""
    target_date = datetime.fromisoformat(date) if date else datetime.now().date()
    
    async with db_pool.acquire() as conn:
        # 获取当日生产实绩
        row = await conn.fetchrow(
            """
            SELECT 
                COUNT(*) as orders_completed,
                SUM(quantity_produced) as total_output,
                SUM(quantity_good) as good_output,
                SUM(quantity_defective) as defective_output,
                SUM(downtime_minutes) as total_downtime,
                AVG(EXTRACT(EPOCH FROM (end_time - start_time))/3600) as avg_cycle_time
            FROM production_actuals
            WHERE DATE(start_time) = $1
            """,
            target_date
        )
        
        if row:
            quality_rate = row['good_output'] / row['total_output'] if row['total_output'] > 0 else 0
            efficiency = 0.85  # 模拟效率
            availability = 1 - (row['total_downtime'] / (24 * 60))  # 可用性
            oee = efficiency * availability * quality_rate
            
            return {
                "date": target_date.isoformat(),
                "orders_completed": row['orders_completed'],
                "total_output": row['total_output'] or 0,
                "good_output": row['good_output'] or 0,
                "defective_output": row['defective_output'] or 0,
                "quality_rate": quality_rate,
                "efficiency": efficiency,
                "availability": availability,
                "oee": oee,
                "total_downtime_minutes": row['total_downtime'] or 0,
                "average_cycle_time_hours": row['avg_cycle_time'] or 0
            }
        else:
            return {
                "date": target_date.isoformat(),
                "message": "No production data found for this date"
            }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
