# 工业智能体平台环境配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL主数据库
DB_HOST=localhost
DB_PORT=5432
DB_NAME=industry_platform
DB_USER=admin
DB_PASSWORD=admin123

# TimescaleDB时序数据库
TIMESCALE_HOST=localhost
TIMESCALE_PORT=5433
TIMESCALE_DB=timeseries_data
TIMESCALE_USER=admin
TIMESCALE_PASSWORD=admin123

# MongoDB文档数据库
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=industry_documents
MONGO_USER=admin
MONGO_PASSWORD=admin123

# Redis缓存
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# 消息队列配置
# =============================================================================
# Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=industry-platform
KAFKA_AUTO_OFFSET_RESET=earliest

# RabbitMQ (可选)
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# =============================================================================
# 搜索引擎配置
# =============================================================================
# Elasticsearch
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX_PREFIX=industry

# =============================================================================
# 向量数据库配置
# =============================================================================
# Weaviate
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_SCHEME=http

# Pinecone (云服务)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment

# Chroma
CHROMA_HOST=localhost
CHROMA_PORT=8000

# =============================================================================
# AI/ML服务配置
# =============================================================================
# SiliconFlow (推荐)
LLM_API_URL=https://api.siliconflow.cn/v1/chat/completions
LLM_API_KEY=your-siliconflow-api-key
LLM_MODEL=Qwen/QwQ-32B
LLM_TIMEOUT=30
LLM_MAX_TOKENS=1000
LLM_TEMPERATURE=0.7

# OpenAI (备用)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Anthropic Claude (备用)
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# 本地LLM服务
LOCAL_LLM_HOST=localhost
LOCAL_LLM_PORT=8080
LOCAL_LLM_MODEL=llama2-7b

# Hugging Face
HUGGINGFACE_API_KEY=your-huggingface-api-key
HUGGINGFACE_MODEL=sentence-transformers/all-MiniLM-L6-v2

# =============================================================================
# 微服务配置
# =============================================================================
# API网关
API_GATEWAY_HOST=localhost
API_GATEWAY_PORT=8000

# 认证服务
AUTH_SERVICE_HOST=localhost
AUTH_SERVICE_PORT=8001
AUTH_SERVICE_URL=http://localhost:8001

# LLM服务
LLM_SERVICE_HOST=localhost
LLM_SERVICE_PORT=8002
LLM_SERVICE_URL=http://localhost:8002

# 智能体编排服务
AGENT_SERVICE_HOST=localhost
AGENT_SERVICE_PORT=8003
AGENT_SERVICE_URL=http://localhost:8003

# 生产计划服务
PRODUCTION_SERVICE_HOST=localhost
PRODUCTION_SERVICE_PORT=8004
PRODUCTION_SERVICE_URL=http://localhost:8004

# 维护服务
MAINTENANCE_SERVICE_HOST=localhost
MAINTENANCE_SERVICE_PORT=8005
MAINTENANCE_SERVICE_URL=http://localhost:8005

# 质量服务
QUALITY_SERVICE_HOST=localhost
QUALITY_SERVICE_PORT=8006
QUALITY_SERVICE_URL=http://localhost:8006

# 供应链服务
SUPPLY_CHAIN_SERVICE_HOST=localhost
SUPPLY_CHAIN_SERVICE_PORT=8007
SUPPLY_CHAIN_SERVICE_URL=http://localhost:8007

# 知识服务
KNOWLEDGE_SERVICE_HOST=localhost
KNOWLEDGE_SERVICE_PORT=8008
KNOWLEDGE_SERVICE_URL=http://localhost:8008

# 数据接入服务
DATA_INGESTION_SERVICE_HOST=localhost
DATA_INGESTION_SERVICE_PORT=8009
DATA_INGESTION_SERVICE_URL=http://localhost:8009

# =============================================================================
# 安全配置
# =============================================================================
# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 加密配置
ENCRYPTION_KEY=your-encryption-key-32-characters
SALT_ROUNDS=12

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# 监控配置
# =============================================================================
# Prometheus
PROMETHEUS_HOST=localhost
PROMETHEUS_PORT=9090

# Grafana
GRAFANA_HOST=localhost
GRAFANA_PORT=3000
GRAFANA_ADMIN_PASSWORD=admin123

# Jaeger
JAEGER_HOST=localhost
JAEGER_PORT=16686
JAEGER_AGENT_PORT=6831

# Sentry (错误监控)
SENTRY_DSN=your-sentry-dsn

# =============================================================================
# 对象存储配置
# =============================================================================
# MinIO
MINIO_HOST=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=admin123
MINIO_BUCKET=industry-platform

# AWS S3 (可选)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# =============================================================================
# 外部系统集成配置
# =============================================================================
# MES系统
MES_API_URL=http://your-mes-system/api
MES_API_KEY=your-mes-api-key

# ERP系统
ERP_API_URL=http://your-erp-system/api
ERP_API_KEY=your-erp-api-key

# SCADA系统
SCADA_HOST=your-scada-host
SCADA_PORT=502
SCADA_UNIT_ID=1

# PLC连接
PLC_HOST=*************
PLC_PORT=502
PLC_SLAVE_ID=1

# =============================================================================
# 邮件配置
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_TLS=true

# =============================================================================
# 文件上传配置
# =============================================================================
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif
UPLOAD_PATH=./uploads

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# =============================================================================
# 限流配置
# =============================================================================
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=200

# =============================================================================
# 工作流配置
# =============================================================================
WORKFLOW_ENGINE=prefect
WORKFLOW_SERVER_URL=http://localhost:4200

# =============================================================================
# 开发配置
# =============================================================================
# 热重载
AUTO_RELOAD=true

# 调试模式
ENABLE_DEBUG_TOOLBAR=true

# 测试数据库
TEST_DB_NAME=industry_platform_test

# =============================================================================
# 生产环境特定配置
# =============================================================================
# 当ENVIRONMENT=production时使用以下配置

# 数据库连接池
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# 性能优化
ENABLE_GZIP=true
ENABLE_CACHING=true

# 安全增强
SECURE_COOKIES=true
HTTPS_ONLY=true

# 日志配置
LOG_FILE_PATH=/var/log/industry-platform/app.log
LOG_ROTATION_SIZE=100MB
LOG_RETENTION_DAYS=30
