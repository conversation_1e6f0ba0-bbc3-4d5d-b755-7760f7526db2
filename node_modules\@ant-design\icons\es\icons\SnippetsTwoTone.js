function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SnippetsTwoToneSvg from "@ant-design/icons-svg/es/asn/SnippetsTwoTone";
import AntdIcon from "../components/AntdIcon";
const SnippetsTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SnippetsTwoToneSvg
}));

/**![snippets](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ1MCA1MTBWMzM2SDIzMnY1NTJoNDMyVjU1MEg0OTBjLTIyLjEgMC00MC0xNy45LTQwLTQweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODMyIDExMkg3MjRWNzJjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djQwSDUwMFY3MmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NDBIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMjBoLTk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTEyYzE3LjcgMCAzMi0xNC4zIDMyLTMydi05Nmg5NmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTY2NCA4ODhIMjMyVjMzNmgyMTh2MTc0YzAgMjIuMSAxNy45IDQwIDQwIDQwaDE3NHYzMzh6bTAtNDAySDUxNFYzMzZoLjJMNjY0IDQ4NS44di4yem0xMjggMjc0aC01NlY0NTZMNTQ0IDI2NEgzNjB2LTgwaDY4djMyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTMyaDE1MnYzMmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di0zMmg2OHY1NzZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(SnippetsTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SnippetsTwoTone';
}
export default RefIcon;