global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'industry-platform-services'
    static_configs:
      - targets: 
        - 'host.docker.internal:8000'  # API Gateway
        - 'host.docker.internal:8001'  # Auth Service
        - 'host.docker.internal:8002'  # LLM Service
        - 'host.docker.internal:8003'  # Agent Orchestrator
        - 'host.docker.internal:8004'  # Production Planning
        - 'host.docker.internal:8005'  # Maintenance Service
        - 'host.docker.internal:8006'  # Quality Service
        - 'host.docker.internal:8007'  # Supply Chain
        - 'host.docker.internal:8008'  # Knowledge Service
        - 'host.docker.internal:8009'  # Data Ingestion
        - 'host.docker.internal:8010'  # Monitoring Service
        - 'host.docker.internal:8011'  # Security Service
        - 'host.docker.internal:8012'  # DevOps Service

  - job_name: 'demo-api'
    static_configs:
      - targets: ['host.docker.internal:8888']
