"""
工业智能体平台 - 供应链管理服务
实现库存管理、采购优化、供应商评估等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import numpy as np
import pandas as pd
from dataclasses import dataclass
import uuid
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None

class PurchaseOrderStatus(str, Enum):
    DRAFT = "draft"
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    SENT = "sent"
    CONFIRMED = "confirmed"
    PARTIALLY_RECEIVED = "partially_received"
    RECEIVED = "received"
    CANCELLED = "cancelled"

class InventoryTransactionType(str, Enum):
    RECEIPT = "receipt"
    ISSUE = "issue"
    TRANSFER = "transfer"
    ADJUSTMENT = "adjustment"
    RETURN = "return"

class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化数据库表
    await initialize_database()
    
    # 启动库存监控任务
    asyncio.create_task(start_inventory_monitoring())
    
    logger.info("Supply chain service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Supply chain service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="供应链管理服务",
    description="库存管理、采购优化、供应商评估",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class InventoryItem(BaseModel):
    material_code: str
    material_name: str
    category: str
    unit: str
    current_stock: float = 0
    reserved_stock: float = 0
    available_stock: float = 0
    minimum_stock: float = 0
    maximum_stock: float = 0
    reorder_point: float = 0
    safety_stock: float = 0
    unit_cost: float = 0
    location: Optional[str] = None

class PurchaseOrderCreate(BaseModel):
    supplier_id: str
    order_items: List[Dict[str, Any]]
    delivery_date: datetime
    payment_terms: Optional[str] = None
    notes: Optional[str] = None

class InventoryTransaction(BaseModel):
    material_code: str
    transaction_type: InventoryTransactionType
    quantity: float
    unit_cost: Optional[float] = None
    reference_number: Optional[str] = None
    location_from: Optional[str] = None
    location_to: Optional[str] = None
    notes: Optional[str] = None

class SupplierEvaluation(BaseModel):
    supplier_id: str
    evaluation_period_start: datetime
    evaluation_period_end: datetime
    quality_score: float
    delivery_score: float
    price_score: float
    service_score: float
    notes: Optional[str] = None

class DemandForecastRequest(BaseModel):
    material_codes: List[str]
    forecast_horizon_days: int = 90
    include_seasonality: bool = True
    confidence_level: float = 0.95

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 库存事务表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS inventory_transactions (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                material_code VARCHAR(50) NOT NULL REFERENCES materials(material_code),
                transaction_type VARCHAR(20) NOT NULL,
                quantity DECIMAL(15,3) NOT NULL,
                unit_cost DECIMAL(12,4),
                reference_number VARCHAR(100),
                location_from VARCHAR(100),
                location_to VARCHAR(100),
                transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                user_id UUID REFERENCES users(id),
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 采购订单表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                order_number VARCHAR(50) UNIQUE NOT NULL,
                supplier_id UUID NOT NULL REFERENCES suppliers(id),
                order_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                delivery_date TIMESTAMP WITH TIME ZONE,
                status VARCHAR(20) DEFAULT 'draft',
                total_amount DECIMAL(15,2),
                currency VARCHAR(3) DEFAULT 'CNY',
                payment_terms TEXT,
                notes TEXT,
                created_by UUID REFERENCES users(id),
                approved_by UUID REFERENCES users(id),
                approved_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 采购订单明细表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                purchase_order_id UUID NOT NULL REFERENCES purchase_orders(id),
                material_code VARCHAR(50) NOT NULL REFERENCES materials(material_code),
                quantity DECIMAL(15,3) NOT NULL,
                unit_price DECIMAL(12,4) NOT NULL,
                total_price DECIMAL(15,2) NOT NULL,
                received_quantity DECIMAL(15,3) DEFAULT 0,
                delivery_date TIMESTAMP WITH TIME ZONE,
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 需求预测表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS demand_forecasts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                material_code VARCHAR(50) NOT NULL REFERENCES materials(material_code),
                forecast_date DATE NOT NULL,
                forecast_quantity DECIMAL(15,3),
                confidence_interval_lower DECIMAL(15,3),
                confidence_interval_upper DECIMAL(15,3),
                forecast_method VARCHAR(50),
                accuracy_score DECIMAL(3,2),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(material_code, forecast_date)
            )
        """)
        
        # 供应商风险评估表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS supplier_risk_assessments (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                supplier_id UUID NOT NULL REFERENCES suppliers(id),
                assessment_date DATE DEFAULT CURRENT_DATE,
                financial_risk_score DECIMAL(3,2),
                operational_risk_score DECIMAL(3,2),
                geographic_risk_score DECIMAL(3,2),
                compliance_risk_score DECIMAL(3,2),
                overall_risk_score DECIMAL(3,2),
                risk_level VARCHAR(20),
                risk_factors TEXT[],
                mitigation_actions TEXT[],
                next_review_date DATE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 库存预警表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS inventory_alerts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                material_code VARCHAR(50) NOT NULL REFERENCES materials(material_code),
                alert_type VARCHAR(20) NOT NULL, -- low_stock, overstock, expired, slow_moving
                alert_level VARCHAR(20) NOT NULL, -- info, warning, critical
                message TEXT NOT NULL,
                current_stock DECIMAL(15,3),
                threshold_value DECIMAL(15,3),
                status VARCHAR(20) DEFAULT 'active', -- active, acknowledged, resolved
                acknowledged_by UUID REFERENCES users(id),
                acknowledged_at TIMESTAMP WITH TIME ZONE,
                resolved_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

async def start_inventory_monitoring():
    """启动库存监控任务"""
    while True:
        try:
            await asyncio.sleep(3600)  # 每小时检查一次
            await SupplyChainManager.check_inventory_levels()
        except Exception as e:
            logger.error(f"Error in inventory monitoring: {e}")
            await asyncio.sleep(300)  # 出错后5分钟重试

class SupplyChainManager:
    """供应链管理器"""
    
    @staticmethod
    async def create_purchase_order(order_data: PurchaseOrderCreate) -> str:
        """创建采购订单"""
        try:
            order_id = str(uuid.uuid4())
            order_number = f"PO{datetime.now().strftime('%Y%m%d')}{order_id[:8]}"
            
            # 计算订单总金额
            total_amount = sum(item['quantity'] * item['unit_price'] for item in order_data.order_items)
            
            async with db_pool.acquire() as conn:
                async with conn.transaction():
                    # 创建采购订单
                    await conn.execute(
                        """
                        INSERT INTO purchase_orders 
                        (id, order_number, supplier_id, delivery_date, total_amount, 
                         payment_terms, notes, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                        """,
                        order_id,
                        order_number,
                        order_data.supplier_id,
                        order_data.delivery_date,
                        total_amount,
                        order_data.payment_terms,
                        order_data.notes,
                        datetime.utcnow()
                    )
                    
                    # 创建订单明细
                    for item in order_data.order_items:
                        await conn.execute(
                            """
                            INSERT INTO purchase_order_items 
                            (purchase_order_id, material_code, quantity, unit_price, 
                             total_price, delivery_date)
                            VALUES ($1, $2, $3, $4, $5, $6)
                            """,
                            order_id,
                            item['material_code'],
                            item['quantity'],
                            item['unit_price'],
                            item['quantity'] * item['unit_price'],
                            item.get('delivery_date', order_data.delivery_date)
                        )
            
            # 发送事件
            await kafka_producer.send("supply_chain_events", {
                "type": "purchase_order_created",
                "order_id": order_id,
                "order_number": order_number,
                "supplier_id": order_data.supplier_id,
                "total_amount": total_amount,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Created purchase order: {order_number}")
            return order_id
            
        except Exception as e:
            logger.error(f"Error creating purchase order: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def process_inventory_transaction(transaction: InventoryTransaction) -> str:
        """处理库存事务"""
        try:
            transaction_id = str(uuid.uuid4())
            
            async with db_pool.acquire() as conn:
                async with conn.transaction():
                    # 记录库存事务
                    await conn.execute(
                        """
                        INSERT INTO inventory_transactions 
                        (id, material_code, transaction_type, quantity, unit_cost,
                         reference_number, location_from, location_to, notes)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                        """,
                        transaction_id,
                        transaction.material_code,
                        transaction.transaction_type.value,
                        transaction.quantity,
                        transaction.unit_cost,
                        transaction.reference_number,
                        transaction.location_from,
                        transaction.location_to,
                        transaction.notes
                    )
                    
                    # 更新库存数量
                    quantity_change = transaction.quantity
                    if transaction.transaction_type in [InventoryTransactionType.ISSUE, 
                                                       InventoryTransactionType.TRANSFER]:
                        quantity_change = -quantity_change
                    
                    await conn.execute(
                        """
                        UPDATE materials 
                        SET current_stock = current_stock + $1,
                            available_stock = current_stock - reserved_stock,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE material_code = $2
                        """,
                        quantity_change,
                        transaction.material_code
                    )
                    
                    # 检查库存水平
                    await SupplyChainManager._check_material_stock_level(
                        conn, transaction.material_code
                    )
            
            # 发送事件
            await kafka_producer.send("supply_chain_events", {
                "type": "inventory_transaction",
                "transaction_id": transaction_id,
                "material_code": transaction.material_code,
                "transaction_type": transaction.transaction_type.value,
                "quantity": transaction.quantity,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Processed inventory transaction: {transaction_id}")
            return transaction_id
            
        except Exception as e:
            logger.error(f"Error processing inventory transaction: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _check_material_stock_level(conn, material_code: str):
        """检查物料库存水平"""
        try:
            # 获取物料信息
            material = await conn.fetchrow(
                """
                SELECT current_stock, minimum_stock, reorder_point, material_name
                FROM materials 
                WHERE material_code = $1
                """,
                material_code
            )
            
            if not material:
                return
            
            current_stock = float(material['current_stock'])
            minimum_stock = float(material['minimum_stock'])
            reorder_point = float(material['reorder_point'])
            
            # 检查是否需要创建预警
            alerts_to_create = []
            
            if current_stock <= 0:
                alerts_to_create.append({
                    "alert_type": "stockout",
                    "alert_level": "critical",
                    "message": f"物料 {material['material_name']} 已缺货",
                    "threshold_value": 0
                })
            elif current_stock <= minimum_stock:
                alerts_to_create.append({
                    "alert_type": "low_stock",
                    "alert_level": "warning",
                    "message": f"物料 {material['material_name']} 库存不足",
                    "threshold_value": minimum_stock
                })
            elif current_stock <= reorder_point:
                alerts_to_create.append({
                    "alert_type": "reorder_needed",
                    "alert_level": "info",
                    "message": f"物料 {material['material_name']} 需要补货",
                    "threshold_value": reorder_point
                })
            
            # 创建预警
            for alert in alerts_to_create:
                await conn.execute(
                    """
                    INSERT INTO inventory_alerts 
                    (material_code, alert_type, alert_level, message, 
                     current_stock, threshold_value)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT DO NOTHING
                    """,
                    material_code,
                    alert["alert_type"],
                    alert["alert_level"],
                    alert["message"],
                    current_stock,
                    alert["threshold_value"]
                )
                
        except Exception as e:
            logger.error(f"Error checking stock level for {material_code}: {e}")
    
    @staticmethod
    async def check_inventory_levels():
        """检查所有物料的库存水平"""
        try:
            async with db_pool.acquire() as conn:
                # 获取所有活跃物料
                materials = await conn.fetch(
                    "SELECT material_code FROM materials WHERE is_active = true"
                )
                
                for material in materials:
                    await SupplyChainManager._check_material_stock_level(
                        conn, material['material_code']
                    )
                    
            logger.info("Completed inventory level check")
            
        except Exception as e:
            logger.error(f"Error checking inventory levels: {e}")
    
    @staticmethod
    async def generate_demand_forecast(request: DemandForecastRequest) -> Dict[str, Any]:
        """生成需求预测"""
        try:
            forecasts = {}
            
            for material_code in request.material_codes:
                # 获取历史消耗数据
                historical_data = await SupplyChainManager._get_historical_consumption(
                    material_code, days=365
                )
                
                if len(historical_data) < 30:  # 需要足够的历史数据
                    forecasts[material_code] = {
                        "error": "Insufficient historical data"
                    }
                    continue
                
                # 简单的移动平均预测
                df = pd.DataFrame(historical_data)
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date').sort_index()
                
                # 计算移动平均
                window_size = min(30, len(df) // 3)
                df['ma'] = df['consumption'].rolling(window=window_size).mean()
                
                # 生成预测
                last_ma = df['ma'].iloc[-1]
                forecast_dates = []
                forecast_values = []
                
                for i in range(request.forecast_horizon_days):
                    forecast_date = datetime.now().date() + timedelta(days=i+1)
                    forecast_dates.append(forecast_date)
                    
                    # 简单预测：使用最近的移动平均值
                    forecast_value = last_ma if not np.isnan(last_ma) else df['consumption'].mean()
                    
                    # 添加一些随机性
                    std_dev = df['consumption'].std()
                    forecast_value += np.random.normal(0, std_dev * 0.1)
                    forecast_value = max(0, forecast_value)  # 确保非负
                    
                    forecast_values.append(forecast_value)
                
                # 计算置信区间
                std_dev = df['consumption'].std()
                confidence_factor = 1.96 if request.confidence_level == 0.95 else 1.645
                
                forecasts[material_code] = {
                    "forecast_horizon_days": request.forecast_horizon_days,
                    "forecasts": [
                        {
                            "date": date.isoformat(),
                            "forecast_quantity": value,
                            "confidence_lower": max(0, value - confidence_factor * std_dev),
                            "confidence_upper": value + confidence_factor * std_dev
                        }
                        for date, value in zip(forecast_dates, forecast_values)
                    ],
                    "total_forecast": sum(forecast_values),
                    "average_daily_demand": np.mean(forecast_values),
                    "forecast_accuracy": 0.85  # 模拟准确率
                }
                
                # 保存预测结果到数据库
                await SupplyChainManager._save_demand_forecast(
                    material_code, forecast_dates, forecast_values, std_dev, confidence_factor
                )
            
            return {
                "forecast_date": datetime.now().isoformat(),
                "forecasts": forecasts
            }
            
        except Exception as e:
            logger.error(f"Error generating demand forecast: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _get_historical_consumption(material_code: str, days: int = 365) -> List[Dict[str, Any]]:
        """获取历史消耗数据"""
        try:
            async with db_pool.acquire() as conn:
                rows = await conn.fetch(
                    """
                    SELECT 
                        DATE(transaction_date) as date,
                        SUM(ABS(quantity)) as consumption
                    FROM inventory_transactions
                    WHERE material_code = $1 
                    AND transaction_type IN ('issue', 'transfer')
                    AND transaction_date >= CURRENT_DATE - INTERVAL '%s days'
                    GROUP BY DATE(transaction_date)
                    ORDER BY date
                    """,
                    material_code, days
                )
                
                return [
                    {
                        "date": row['date'],
                        "consumption": float(row['consumption'])
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"Error getting historical consumption: {e}")
            return []
    
    @staticmethod
    async def _save_demand_forecast(material_code: str, dates: List, values: List, 
                                  std_dev: float, confidence_factor: float):
        """保存需求预测结果"""
        try:
            async with db_pool.acquire() as conn:
                for date, value in zip(dates, values):
                    await conn.execute(
                        """
                        INSERT INTO demand_forecasts 
                        (material_code, forecast_date, forecast_quantity,
                         confidence_interval_lower, confidence_interval_upper, forecast_method)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (material_code, forecast_date) 
                        DO UPDATE SET
                            forecast_quantity = EXCLUDED.forecast_quantity,
                            confidence_interval_lower = EXCLUDED.confidence_interval_lower,
                            confidence_interval_upper = EXCLUDED.confidence_interval_upper,
                            forecast_method = EXCLUDED.forecast_method
                        """,
                        material_code,
                        date,
                        value,
                        max(0, value - confidence_factor * std_dev),
                        value + confidence_factor * std_dev,
                        "moving_average"
                    )
                    
        except Exception as e:
            logger.error(f"Error saving demand forecast: {e}")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "supply-chain"}

@app.post("/purchase-orders")
async def create_purchase_order(order_data: PurchaseOrderCreate):
    """创建采购订单"""
    order_id = await SupplyChainManager.create_purchase_order(order_data)
    return {"message": "Purchase order created", "order_id": order_id}

@app.post("/inventory/transactions")
async def process_inventory_transaction(transaction: InventoryTransaction):
    """处理库存事务"""
    transaction_id = await SupplyChainManager.process_inventory_transaction(transaction)
    return {"message": "Inventory transaction processed", "transaction_id": transaction_id}

@app.post("/demand/forecast")
async def generate_demand_forecast(request: DemandForecastRequest):
    """生成需求预测"""
    forecast_result = await SupplyChainManager.generate_demand_forecast(request)
    return forecast_result

@app.get("/inventory/alerts")
async def get_inventory_alerts(
    alert_type: Optional[str] = None,
    alert_level: Optional[str] = None,
    status: str = "active",
    limit: int = 50
):
    """获取库存预警"""
    async with db_pool.acquire() as conn:
        query = """
            SELECT ia.*, m.material_name
            FROM inventory_alerts ia
            JOIN materials m ON ia.material_code = m.material_code
            WHERE ia.status = $1
        """
        params = [status]
        param_count = 2
        
        if alert_type:
            query += f" AND ia.alert_type = ${param_count}"
            params.append(alert_type)
            param_count += 1
        
        if alert_level:
            query += f" AND ia.alert_level = ${param_count}"
            params.append(alert_level)
            param_count += 1
        
        query += f" ORDER BY ia.created_at DESC LIMIT ${param_count}"
        params.append(limit)
        
        rows = await conn.fetch(query, *params)
        return [dict(row) for row in rows]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)
