'use client';

import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, DatePicker, message, Switch, Descriptions } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  PlusOutlined,
  ReloadOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Line, Column } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useProductionLines, useProductionAnalytics } from '@/hooks/useApi';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 样式组件
const ProductionContainer = styled.div`
  .production-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .status-running {
    color: var(--status-success);
    background: rgba(0, 212, 170, 0.1);
    border: 1px solid rgba(0, 212, 170, 0.3);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .status-maintenance {
    color: var(--status-warning);
    background: rgba(255, 140, 66, 0.1);
    border: 1px solid rgba(255, 140, 66, 0.3);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .status-offline {
    color: var(--status-error);
    background: rgba(255, 71, 87, 0.1);
    border: 1px solid rgba(255, 71, 87, 0.3);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const ProductionPage: React.FC = () => {
  const [selectedLine, setSelectedLine] = useState<string | null>(null);
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLineData, setSelectedLineData] = useState<any>(null);
  const [realTimeMode, setRealTimeMode] = useState(true);
  const [form] = Form.useForm();
  
  // 获取数据
  const { data: productionData, loading: linesLoading, refetch: refetchLines } = useProductionLines();
  const { data: analyticsData, loading: analyticsLoading } = useProductionAnalytics();

  // 模拟数据
  const mockProductionLines = [
    { id: 'line_001', name: '发动机缸体生产线', status: 'running', efficiency: 92.1, output: 145, target: 160 },
    { id: 'line_002', name: '变速箱齿轮生产线', status: 'running', efficiency: 88.5, output: 132, target: 150 },
    { id: 'line_003', name: '制动盘生产线', status: 'maintenance', efficiency: 0, output: 0, target: 120 },
    { id: 'line_004', name: '悬挂系统生产线', status: 'running', efficiency: 78.9, output: 98, target: 125 },
    { id: 'line_005', name: '电子控制单元生产线', status: 'running', efficiency: 96.3, output: 187, target: 195 }
  ];

  const mockAnalytics = {
    efficiency_trend: [
      { time: '00:00', efficiency: 82.1 },
      { time: '04:00', efficiency: 84.3 },
      { time: '08:00', efficiency: 87.2 },
      { time: '12:00', efficiency: 85.8 },
      { time: '16:00', efficiency: 89.1 },
      { time: '20:00', efficiency: 86.5 }
    ],
    output_by_line: [
      { line: '发动机缸体', output: 145, target: 160 },
      { line: '变速箱齿轮', output: 132, target: 150 },
      { line: '制动盘', output: 0, target: 120 },
      { line: '悬挂系统', output: 98, target: 125 },
      { line: '电子控制', output: 187, target: 195 }
    ]
  };

  // 生产线表格列
  const productionLineColumns = [
    {
      title: '生产线名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <span style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{text}</span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          running: { text: '运行中', class: 'status-running', icon: <PlayCircleOutlined /> },
          maintenance: { text: '维护中', class: 'status-maintenance', icon: <SettingOutlined /> },
          offline: { text: '离线', class: 'status-offline', icon: <StopOutlined /> }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <span className={config.class}>
            {config.icon}
            <span style={{ marginLeft: '4px' }}>{config.text}</span>
          </span>
        );
      }
    },
    {
      title: '效率',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (efficiency: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Progress
            percent={efficiency}
            size="small"
            strokeColor={{
              '0%': '#ff4757',
              '50%': '#ff8c42',
              '100%': '#00d4aa'
            }}
            style={{ flex: 1, maxWidth: '100px' }}
          />
          <span style={{ color: 'var(--text-primary)', fontWeight: 500, minWidth: '50px' }}>
            {efficiency.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      title: '今日产量',
      dataIndex: 'output',
      key: 'output',
      render: (output: number, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>
            {output} / {record.target}
          </div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            完成率: {((output / record.target) * 100).toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button
            size="small"
            icon={<BarChartOutlined />}
            onClick={() => {
              setSelectedLineData(record);
              setDetailModalVisible(true);
            }}
          >
            详情
          </Button>
          {record.status === 'running' ? (
            <Button size="small" icon={<PauseCircleOutlined />} danger>
              暂停
            </Button>
          ) : (
            <Button size="small" icon={<PlayCircleOutlined />} type="primary">
              启动
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 实时数据更新
  useEffect(() => {
    if (!realTimeMode) return;

    const interval = setInterval(() => {
      // 模拟实时数据更新
      console.log('实时数据更新...');
      // 这里可以调用API获取最新数据
      refetchLines();
    }, 10000); // 每10秒更新一次

    return () => clearInterval(interval);
  }, [realTimeMode, refetchLines]);

  // 创建生产计划
  const handleCreatePlan = async (values: any) => {
    try {
      // 这里调用API创建生产计划
      message.success('生产计划创建成功！');
      setPlanModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建生产计划失败');
    }
  };

  return (
    <MainLayout>
      <ProductionContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
              <SectionTitle>生产管理</SectionTitle>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ color: 'var(--text-secondary)', fontSize: '14px' }}>实时监控:</span>
                <Switch
                  checked={realTimeMode}
                  onChange={setRealTimeMode}
                  checkedChildren="开"
                  unCheckedChildren="关"
                />
                {realTimeMode && (
                  <Tag color="green" style={{ margin: 0 }}>
                    <span style={{ fontSize: '12px' }}>●</span> 实时更新
                  </Tag>
                )}
              </div>
            </div>
            <Space>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => setPlanModalVisible(true)}
              >
                新建计划
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={refetchLines}
                loading={linesLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 概览统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="production-card">
                  <Statistic
                    title="运行生产线"
                    value={mockProductionLines.filter(line => line.status === 'running').length}
                    suffix={`/ ${mockProductionLines.length}`}
                    prefix={<PlayCircleOutlined style={{ color: 'var(--status-success)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="production-card">
                  <Statistic
                    title="平均效率"
                    value={mockProductionLines.reduce((acc, line) => acc + line.efficiency, 0) / mockProductionLines.length}
                    precision={1}
                    suffix="%"
                    prefix={<BarChartOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="production-card">
                  <Statistic
                    title="今日总产量"
                    value={mockProductionLines.reduce((acc, line) => acc + line.output, 0)}
                    prefix={<CheckCircleOutlined style={{ color: 'var(--color-accent-green)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="production-card">
                  <Statistic
                    title="维护中"
                    value={mockProductionLines.filter(line => line.status === 'maintenance').length}
                    prefix={<ExclamationCircleOutlined style={{ color: 'var(--status-warning)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="production-card" title="效率趋势" style={{ height: '400px' }}>
                  <Line
                    data={mockAnalytics.efficiency_trend}
                    xField="time"
                    yField="efficiency"
                    smooth
                    color="var(--color-accent-blue)"
                    point={{
                      size: 4,
                      shape: 'circle',
                      style: {
                        fill: 'var(--color-accent-blue)',
                        stroke: 'var(--bg-card)',
                        lineWidth: 2
                      }
                    }}
                    yAxis={{
                      min: 70,
                      max: 100
                    }}
                  />
                </Card>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="production-card" title="产量对比" style={{ height: '400px' }}>
                  <Column
                    data={mockAnalytics.output_by_line}
                    xField="line"
                    yField="output"
                    seriesField="type"
                    isGroup={true}
                    color={['var(--color-accent-blue)', 'var(--color-accent-gray)']}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 生产线列表 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Card className="production-card" title="生产线状态">
              <Table
                dataSource={mockProductionLines}
                columns={productionLineColumns}
                rowKey="id"
                pagination={false}
                loading={linesLoading}
              />
            </Card>
          </motion.div>

          {/* 创建生产计划模态框 */}
          <Modal
            title="创建生产计划"
            open={planModalVisible}
            onCancel={() => setPlanModalVisible(false)}
            onOk={() => form.submit()}
            okText="创建"
            cancelText="取消"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreatePlan}
            >
              <Form.Item
                name="product_code"
                label="产品代码"
                rules={[{ required: true, message: '请输入产品代码' }]}
              >
                <Input placeholder="请输入产品代码" />
              </Form.Item>

              <Form.Item
                name="quantity"
                label="计划数量"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <Input type="number" placeholder="请输入计划数量" />
              </Form.Item>

              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="normal">普通</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="deadline"
                label="截止时间"
                rules={[{ required: true, message: '请选择截止时间' }]}
              >
                <DatePicker
                  showTime
                  style={{ width: '100%' }}
                  placeholder="请选择截止时间"
                />
              </Form.Item>
            </Form>
          </Modal>

          {/* 生产线详情模态框 */}
          <Modal
            title={`生产线详情 - ${selectedLineData?.name || ''}`}
            open={detailModalVisible}
            onCancel={() => {
              setDetailModalVisible(false);
              setSelectedLineData(null);
            }}
            footer={[
              <Button key="close" onClick={() => setDetailModalVisible(false)}>
                关闭
              </Button>
            ]}
            width={800}
          >
            {selectedLineData && (
              <div>
                <Descriptions bordered column={2} style={{ marginBottom: '24px' }}>
                  <Descriptions.Item label="生产线名称">{selectedLineData.name}</Descriptions.Item>
                  <Descriptions.Item label="生产线ID">{selectedLineData.id}</Descriptions.Item>
                  <Descriptions.Item label="当前状态">
                    <Tag color={selectedLineData.status === 'running' ? 'green' : selectedLineData.status === 'maintenance' ? 'orange' : 'red'}>
                      {selectedLineData.status === 'running' ? '运行中' : selectedLineData.status === 'maintenance' ? '维护中' : '离线'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="运行效率">
                    <span style={{ color: selectedLineData.efficiency > 90 ? 'var(--status-success)' : selectedLineData.efficiency > 70 ? 'var(--status-warning)' : 'var(--status-error)' }}>
                      {selectedLineData.efficiency.toFixed(1)}%
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="今日产量">{selectedLineData.output} 件</Descriptions.Item>
                  <Descriptions.Item label="目标产量">{selectedLineData.target} 件</Descriptions.Item>
                  <Descriptions.Item label="完成率">
                    <span style={{ color: (selectedLineData.output / selectedLineData.target) > 0.9 ? 'var(--status-success)' : 'var(--status-warning)' }}>
                      {((selectedLineData.output / selectedLineData.target) * 100).toFixed(1)}%
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="运行时长">8小时32分钟</Descriptions.Item>
                </Descriptions>

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Card title="实时参数" size="small">
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>设备温度:</span>
                          <span style={{ color: 'var(--status-success)' }}>72°C</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>运行速度:</span>
                          <span style={{ color: 'var(--color-accent-blue)' }}>85%</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>压力值:</span>
                          <span style={{ color: 'var(--status-success)' }}>2.3 MPa</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>振动水平:</span>
                          <span style={{ color: 'var(--status-success)' }}>正常</span>
                        </div>
                      </div>
                    </Card>
                  </Col>

                  <Col span={12}>
                    <Card title="质量指标" size="small">
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>合格率:</span>
                          <span style={{ color: 'var(--status-success)' }}>98.5%</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>缺陷率:</span>
                          <span style={{ color: 'var(--status-warning)' }}>1.5%</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>返工率:</span>
                          <span style={{ color: 'var(--status-success)' }}>0.8%</span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>废品率:</span>
                          <span style={{ color: 'var(--status-success)' }}>0.2%</span>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                <Card title="最近告警" size="small" style={{ marginTop: '16px' }}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>设备温度偏高</span>
                      <div>
                        <Tag color="orange" size="small">警告</Tag>
                        <span style={{ fontSize: '12px', color: 'var(--text-muted)' }}>2小时前</span>
                      </div>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>生产速度下降</span>
                      <div>
                        <Tag color="blue" size="small">信息</Tag>
                        <span style={{ fontSize: '12px', color: 'var(--text-muted)' }}>4小时前</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </Modal>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreatePlan}
            >
              <Form.Item
                name="product_code"
                label="产品代码"
                rules={[{ required: true, message: '请输入产品代码' }]}
              >
                <Input placeholder="请输入产品代码" />
              </Form.Item>
              
              <Form.Item
                name="quantity"
                label="计划数量"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <Input type="number" placeholder="请输入计划数量" />
              </Form.Item>
              
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="normal">普通</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="deadline"
                label="截止时间"
                rules={[{ required: true, message: '请选择截止时间' }]}
              >
                <DatePicker 
                  showTime 
                  style={{ width: '100%' }}
                  placeholder="请选择截止时间"
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </ProductionContainer>
    </MainLayout>
  );
};

export default ProductionPage;
