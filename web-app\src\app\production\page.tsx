'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, DatePicker, message } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  PlusOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Line, Column } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useProductionLines, useProductionAnalytics } from '@/hooks/useApi';
import dayjs from 'dayjs';

const { Option } = Select;

// 样式组件
const ProductionContainer = styled.div`
  .production-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const ProductionPage: React.FC = () => {
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  // 获取数据
  const { data: productionData, loading: linesLoading, refetch: refetchLines } = useProductionLines();
  const { data: analyticsData, loading: analyticsLoading } = useProductionAnalytics();

  // 模拟数据
  const mockProductionLines = [
    { id: 'line_001', name: '发动机缸体生产线', status: 'running', efficiency: 92.1, output: 145, target: 160 },
    { id: 'line_002', name: '变速箱齿轮生产线', status: 'running', efficiency: 88.5, output: 132, target: 150 },
    { id: 'line_003', name: '制动盘生产线', status: 'maintenance', efficiency: 0, output: 0, target: 120 },
    { id: 'line_004', name: '悬挂系统生产线', status: 'running', efficiency: 78.9, output: 98, target: 125 },
    { id: 'line_005', name: '电子控制单元生产线', status: 'running', efficiency: 96.3, output: 187, target: 195 }
  ];

  // 生产线表格列
  const productionLineColumns = [
    {
      title: '生产线名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <span style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{text}</span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          running: { text: '运行中', color: 'green' },
          maintenance: { text: '维护中', color: 'orange' },
          offline: { text: '离线', color: 'red' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '效率',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (efficiency: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Progress
            percent={efficiency}
            size="small"
            strokeColor={{
              '0%': '#ff4757',
              '50%': '#ff8c42',
              '100%': '#00d4aa'
            }}
            style={{ flex: 1, maxWidth: '100px' }}
          />
          <span style={{ color: 'var(--text-primary)', fontWeight: 500, minWidth: '50px' }}>
            {efficiency.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      title: '今日产量',
      dataIndex: 'output',
      key: 'output',
      render: (output: number, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>
            {output} / {record.target}
          </div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            完成率: {((output / record.target) * 100).toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button size="small" icon={<BarChartOutlined />}>
            详情
          </Button>
          {record.status === 'running' ? (
            <Button size="small" icon={<PauseCircleOutlined />} danger>
              暂停
            </Button>
          ) : (
            <Button size="small" icon={<PlayCircleOutlined />} type="primary">
              启动
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 创建生产计划
  const handleCreatePlan = async (values: any) => {
    try {
      message.success('生产计划创建成功！');
      setPlanModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建生产计划失败');
    }
  };

  return (
    <MainLayout>
      <ProductionContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>生产管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setPlanModalVisible(true)}
              >
                新建计划
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refetchLines}
                loading={linesLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 概览统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="production-card">
                <Statistic
                  title="运行生产线"
                  value={mockProductionLines.filter(line => line.status === 'running').length}
                  suffix={`/ ${mockProductionLines.length}`}
                  prefix={<PlayCircleOutlined style={{ color: 'var(--status-success)' }} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="production-card">
                <Statistic
                  title="平均效率"
                  value={mockProductionLines.reduce((acc, line) => acc + line.efficiency, 0) / mockProductionLines.length}
                  precision={1}
                  suffix="%"
                  prefix={<BarChartOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="production-card">
                <Statistic
                  title="今日总产量"
                  value={mockProductionLines.reduce((acc, line) => acc + line.output, 0)}
                  prefix={<CheckCircleOutlined style={{ color: 'var(--color-accent-green)' }} />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="production-card">
                <Statistic
                  title="维护中"
                  value={mockProductionLines.filter(line => line.status === 'maintenance').length}
                  prefix={<ExclamationCircleOutlined style={{ color: 'var(--status-warning)' }} />}
                />
              </Card>
            </Col>
          </Row>

          {/* 生产线列表 */}
          <Card className="production-card" title="生产线状态">
            <Table
              dataSource={mockProductionLines}
              columns={productionLineColumns}
              rowKey="id"
              pagination={false}
              loading={linesLoading}
            />
          </Card>

          {/* 创建生产计划模态框 */}
          <Modal
            title="创建生产计划"
            open={planModalVisible}
            onCancel={() => setPlanModalVisible(false)}
            onOk={() => form.submit()}
            okText="创建"
            cancelText="取消"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreatePlan}
            >
              <Form.Item
                name="product_code"
                label="产品代码"
                rules={[{ required: true, message: '请输入产品代码' }]}
              >
                <Input placeholder="请输入产品代码" />
              </Form.Item>
              
              <Form.Item
                name="quantity"
                label="计划数量"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <Input type="number" placeholder="请输入计划数量" />
              </Form.Item>
              
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="normal">普通</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="deadline"
                label="截止时间"
                rules={[{ required: true, message: '请选择截止时间' }]}
              >
                <DatePicker 
                  showTime 
                  style={{ width: '100%' }}
                  placeholder="请选择截止时间"
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </ProductionContainer>
    </MainLayout>
  );
};

export default ProductionPage;
