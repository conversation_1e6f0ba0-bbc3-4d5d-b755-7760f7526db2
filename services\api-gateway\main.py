"""
工业智能体平台 - API网关服务
提供统一的API入口，处理路由、认证、限流等功能
"""

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
import httpx
import asyncio
import time
import logging
from typing import Dict, Any, Optional
import os
from datetime import datetime, timedelta
import redis
import json
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="工业智能体平台 API网关",
    description="统一API入口，提供路由、认证、限流等功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境应该限制具体主机
)

# 安全配置
security = HTTPBearer()

# Redis连接（用于限流和缓存）
redis_client = redis.Redis(
    host=os.getenv("REDIS_HOST", "localhost"),
    port=int(os.getenv("REDIS_PORT", "6379")),
    db=0,
    decode_responses=True
)

# 服务注册表
SERVICE_REGISTRY = {
    "auth": {
        "url": os.getenv("AUTH_SERVICE_URL", "http://localhost:8001"),
        "health_endpoint": "/health"
    },
    "llm": {
        "url": os.getenv("LLM_SERVICE_URL", "http://localhost:8002"),
        "health_endpoint": "/health"
    },
    "agent": {
        "url": os.getenv("AGENT_SERVICE_URL", "http://localhost:8003"),
        "health_endpoint": "/health"
    },
    "production": {
        "url": os.getenv("PRODUCTION_SERVICE_URL", "http://localhost:8004"),
        "health_endpoint": "/health"
    },
    "maintenance": {
        "url": os.getenv("MAINTENANCE_SERVICE_URL", "http://localhost:8005"),
        "health_endpoint": "/health"
    },
    "quality": {
        "url": os.getenv("QUALITY_SERVICE_URL", "http://localhost:8006"),
        "health_endpoint": "/health"
    },
    "supply_chain": {
        "url": os.getenv("SUPPLY_CHAIN_SERVICE_URL", "http://localhost:8007"),
        "health_endpoint": "/health"
    },
    "knowledge": {
        "url": os.getenv("KNOWLEDGE_SERVICE_URL", "http://localhost:8008"),
        "health_endpoint": "/health"
    },
    "data_ingestion": {
        "url": os.getenv("DATA_INGESTION_SERVICE_URL", "http://localhost:8009"),
        "health_endpoint": "/health"
    }
}

# 路由配置
ROUTE_CONFIG = {
    "/api/v1/auth": "auth",
    "/api/v1/llm": "llm",
    "/api/v1/agents": "agent",
    "/api/v1/production": "production",
    "/api/v1/maintenance": "maintenance",
    "/api/v1/quality": "quality",
    "/api/v1/supply-chain": "supply_chain",
    "/api/v1/knowledge": "knowledge",
    "/api/v1/data": "data_ingestion"
}

class RateLimiter:
    """限流器"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """
        检查是否允许请求
        :param key: 限流键（通常是用户ID或IP）
        :param limit: 限制次数
        :param window: 时间窗口（秒）
        """
        current_time = int(time.time())
        pipeline = self.redis.pipeline()
        
        # 使用滑动窗口算法
        pipeline.zremrangebyscore(key, 0, current_time - window)
        pipeline.zcard(key)
        pipeline.zadd(key, {str(current_time): current_time})
        pipeline.expire(key, window)
        
        results = pipeline.execute()
        current_requests = results[1]
        
        return current_requests < limit

rate_limiter = RateLimiter(redis_client)

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = {}
        self.last_failure_time = {}
        self.state = {}  # open, closed, half_open
    
    def is_open(self, service: str) -> bool:
        """检查熔断器是否开启"""
        if service not in self.state:
            self.state[service] = "closed"
            return False
        
        if self.state[service] == "open":
            # 检查是否可以进入半开状态
            if time.time() - self.last_failure_time.get(service, 0) > self.timeout:
                self.state[service] = "half_open"
                return False
            return True
        
        return False
    
    def record_success(self, service: str):
        """记录成功请求"""
        self.failure_count[service] = 0
        self.state[service] = "closed"
    
    def record_failure(self, service: str):
        """记录失败请求"""
        self.failure_count[service] = self.failure_count.get(service, 0) + 1
        self.last_failure_time[service] = time.time()
        
        if self.failure_count[service] >= self.failure_threshold:
            self.state[service] = "open"

circuit_breaker = CircuitBreaker()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """验证JWT令牌"""
    token = credentials.credentials
    
    # 首先检查Redis缓存
    cached_user = redis_client.get(f"token:{token}")
    if cached_user:
        return json.loads(cached_user)
    
    # 调用认证服务验证令牌
    try:
        auth_service_url = SERVICE_REGISTRY["auth"]["url"]
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_service_url}/verify-token",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code == 200:
                user_info = response.json()
                # 缓存用户信息（5分钟）
                redis_client.setex(f"token:{token}", 300, json.dumps(user_info))
                return user_info
            else:
                raise HTTPException(status_code=401, detail="Invalid token")
                
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(status_code=401, detail="Token verification failed")

async def check_rate_limit(request: Request, user_info: Dict[str, Any]):
    """检查限流"""
    user_id = user_info.get("user_id")
    client_ip = request.client.host
    
    # 根据用户角色设置不同的限流策略
    role = user_info.get("role", "user")
    if role == "admin":
        limit = 1000  # 管理员每分钟1000次请求
    elif role == "premium":
        limit = 500   # 高级用户每分钟500次请求
    else:
        limit = 100   # 普通用户每分钟100次请求
    
    # 检查用户级别限流
    user_key = f"rate_limit:user:{user_id}"
    if not await rate_limiter.is_allowed(user_key, limit, 60):
        raise HTTPException(status_code=429, detail="Rate limit exceeded for user")
    
    # 检查IP级别限流
    ip_key = f"rate_limit:ip:{client_ip}"
    if not await rate_limiter.is_allowed(ip_key, limit * 2, 60):
        raise HTTPException(status_code=429, detail="Rate limit exceeded for IP")

def get_service_for_path(path: str) -> Optional[str]:
    """根据路径获取对应的服务"""
    for route_prefix, service_name in ROUTE_CONFIG.items():
        if path.startswith(route_prefix):
            return service_name
    return None

async def proxy_request(request: Request, service_name: str, path: str):
    """代理请求到后端服务"""
    
    # 检查熔断器
    if circuit_breaker.is_open(service_name):
        raise HTTPException(status_code=503, detail=f"Service {service_name} is temporarily unavailable")
    
    service_config = SERVICE_REGISTRY.get(service_name)
    if not service_config:
        raise HTTPException(status_code=404, detail=f"Service {service_name} not found")
    
    # 构建目标URL
    target_url = f"{service_config['url']}{path}"
    if request.url.query:
        target_url += f"?{request.url.query}"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 转发请求
            response = await client.request(
                method=request.method,
                url=target_url,
                headers=dict(request.headers),
                content=await request.body()
            )
            
            # 记录成功
            circuit_breaker.record_success(service_name)
            
            # 返回响应
            return JSONResponse(
                content=response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
    except Exception as e:
        logger.error(f"Request to {service_name} failed: {e}")
        circuit_breaker.record_failure(service_name)
        raise HTTPException(status_code=502, detail=f"Service {service_name} unavailable")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

@app.get("/services/status")
async def services_status():
    """获取所有服务状态"""
    status = {}
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        for service_name, config in SERVICE_REGISTRY.items():
            try:
                response = await client.get(f"{config['url']}{config['health_endpoint']}")
                status[service_name] = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "response_time": response.elapsed.total_seconds(),
                    "circuit_breaker": circuit_breaker.state.get(service_name, "closed")
                }
            except Exception as e:
                status[service_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "circuit_breaker": circuit_breaker.state.get(service_name, "closed")
                }
    
    return status

@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_handler(request: Request, path: str, user_info: Dict[str, Any] = Depends(verify_token)):
    """统一代理处理器"""
    
    # 检查限流
    await check_rate_limit(request, user_info)
    
    # 获取对应的服务
    service_name = get_service_for_path(f"/{path}")
    if not service_name:
        raise HTTPException(status_code=404, detail="Service not found")
    
    # 代理请求
    return await proxy_request(request, service_name, f"/{path}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
