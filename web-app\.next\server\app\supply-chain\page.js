/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/supply-chain/page";
exports.ids = ["app/supply-chain/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsupply-chain%2Fpage&page=%2Fsupply-chain%2Fpage&appPaths=%2Fsupply-chain%2Fpage&pagePath=private-next-app-dir%2Fsupply-chain%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsupply-chain%2Fpage&page=%2Fsupply-chain%2Fpage&appPaths=%2Fsupply-chain%2Fpage&pagePath=private-next-app-dir%2Fsupply-chain%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/supply-chain/page.tsx */ \"(rsc)/./src/app/supply-chain/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'supply-chain',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/supply-chain/page\",\n        pathname: \"/supply-chain\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsupply-chain%2Fpage&page=%2Fsupply-chain%2Fpage&appPaths=%2Fsupply-chain%2Fpage&pagePath=private-next-app-dir%2Fsupply-chain%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/supply-chain/page.tsx */ \"(rsc)/./src/app/supply-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3N1cHBseS1jaGFpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBaUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHN1cHBseS1jaGFpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/supply-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/supply-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\supply-chain\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/supply-chain/page.tsx */ \"(ssr)/./src/app/supply-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3N1cHBseS1jaGFpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBaUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHN1cHBseS1jaGFpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Csupply-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d15376eed6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXHdlYi1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBkMTUzNzZlZWQ2ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/app/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/../node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(ssr)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/locale/zh-cn */ \"(ssr)/../node_modules/dayjs/locale/zh-cn.js\");\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/relativeTime */ \"(ssr)/../node_modules/dayjs/plugin/relativeTime.js\");\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/utc */ \"(ssr)/../node_modules/dayjs/plugin/utc.js\");\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/timezone */ \"(ssr)/../node_modules/dayjs/plugin/timezone.js\");\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// 配置dayjs\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().locale('zh-cn');\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default()));\n// 创建QueryClient实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 3,\n            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000\n        },\n        mutations: {\n            retry: 1\n        }\n    }\n});\n// Ant Design工业风格主题配置\nconst antdTheme = {\n    token: {\n        colorPrimary: '#4a90e2',\n        colorSuccess: '#00d4aa',\n        colorWarning: '#ff8c42',\n        colorError: '#ff4757',\n        colorInfo: '#4a90e2',\n        colorBgBase: '#0f0f0f',\n        colorBgContainer: '#1a1a1a',\n        colorBgElevated: '#242424',\n        colorBorder: '#333333',\n        colorText: '#ffffff',\n        colorTextSecondary: '#b0b0b0',\n        borderRadius: 8,\n        wireframe: false\n    },\n    components: {\n        Layout: {\n            headerBg: '#1a1a1a',\n            siderBg: '#1a1a1a',\n            bodyBg: '#0f0f0f'\n        },\n        Menu: {\n            darkItemBg: 'transparent',\n            darkSubMenuItemBg: '#242424',\n            darkItemSelectedBg: 'rgba(74, 144, 226, 0.1)',\n            darkItemColor: '#b0b0b0',\n            darkItemSelectedColor: '#4a90e2',\n            darkItemHoverColor: '#4a90e2'\n        },\n        Card: {\n            headerBg: '#242424',\n            colorBgContainer: '#242424'\n        },\n        Table: {\n            headerBg: '#1a1a1a',\n            colorBgContainer: '#242424'\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"基于大语言模型的工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                            theme: antdTheme,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/supply-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/supply-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/tabs/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Form,Input,Modal,Progress,Row,Select,Space,Statistic,Table,Tabs,Tag,message!=!antd */ \"(ssr)/../node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/WarningOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,ExclamationCircleOutlined,PlusOutlined,ReloadOutlined,ShopOutlined,ShoppingCartOutlined,WarningOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @ant-design/charts */ \"(ssr)/../node_modules/@ant-design/plots/es/components/pie/index.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @ant-design/charts */ \"(ssr)/../node_modules/@ant-design/plots/es/components/column/index.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! styled-components */ \"(ssr)/../node_modules/styled-components/dist/styled-components.esm.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(ssr)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_countup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-countup */ \"(ssr)/../node_modules/react-countup/build/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst { Option } = _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst { TabPane } = _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n// 样式组件\nconst SupplyChainContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].div`\n  .supply-card {\n    background: var(--bg-card);\n    border: 1px solid var(--border-primary);\n    border-radius: 8px;\n    box-shadow: var(--shadow-card);\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: var(--shadow-hover);\n    }\n  }\n  \n  .stock-status {\n    &.normal {\n      color: var(--status-success);\n      background: rgba(0, 212, 170, 0.1);\n      border: 1px solid rgba(0, 212, 170, 0.3);\n    }\n    \n    &.low {\n      color: var(--status-warning);\n      background: rgba(255, 140, 66, 0.1);\n      border: 1px solid rgba(255, 140, 66, 0.3);\n    }\n    \n    &.critical {\n      color: var(--status-error);\n      background: rgba(255, 71, 87, 0.1);\n      border: 1px solid rgba(255, 71, 87, 0.3);\n    }\n    \n    padding: 4px 12px;\n    border-radius: 12px;\n    font-size: 12px;\n    font-weight: 500;\n    display: inline-flex;\n    align-items: center;\n    gap: 4px;\n  }\n`;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_8__[\"default\"].h3`\n  color: var(--text-primary);\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &::before {\n    content: '';\n    width: 4px;\n    height: 16px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 2px;\n  }\n`;\nconst SupplyChainPage = ()=>{\n    const [purchaseModalVisible, setPurchaseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    // 获取库存数据\n    const { data: inventoryData, loading: inventoryLoading, refetch: refetchInventory } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_3__.useInventoryStatus)();\n    // 模拟库存数据\n    const mockInventory = [\n        {\n            id: 'mat_001',\n            material: '钢板',\n            category: '原材料',\n            current_stock: 1250,\n            minimum_stock: 100,\n            maximum_stock: 2000,\n            unit: 'kg',\n            unit_price: 8.5,\n            supplier: '钢铁集团A',\n            last_purchase: '2024-12-20',\n            status: 'normal'\n        },\n        {\n            id: 'mat_002',\n            material: '螺栓',\n            category: '标准件',\n            current_stock: 45,\n            minimum_stock: 200,\n            maximum_stock: 1000,\n            unit: '个',\n            unit_price: 0.8,\n            supplier: '标准件公司B',\n            last_purchase: '2024-12-15',\n            status: 'critical'\n        },\n        {\n            id: 'mat_003',\n            material: '轴承',\n            category: '零部件',\n            current_stock: 180,\n            minimum_stock: 20,\n            maximum_stock: 500,\n            unit: '个',\n            unit_price: 45.0,\n            supplier: '轴承制造C',\n            last_purchase: '2024-12-25',\n            status: 'normal'\n        },\n        {\n            id: 'mat_004',\n            material: '电机',\n            category: '零部件',\n            current_stock: 15,\n            minimum_stock: 10,\n            maximum_stock: 100,\n            unit: '个',\n            unit_price: 280.0,\n            supplier: '电机厂D',\n            last_purchase: '2024-12-22',\n            status: 'low'\n        },\n        {\n            id: 'mat_005',\n            material: '密封圈',\n            category: '辅料',\n            current_stock: 180,\n            minimum_stock: 150,\n            maximum_stock: 800,\n            unit: '个',\n            unit_price: 2.5,\n            supplier: '橡胶制品E',\n            last_purchase: '2024-12-18',\n            status: 'low'\n        }\n    ];\n    // 供应商数据\n    const mockSuppliers = [\n        {\n            id: 'sup_001',\n            name: '钢铁集团A',\n            category: '原材料',\n            rating: 4.8,\n            delivery_rate: 98.5,\n            quality_score: 96.2,\n            payment_terms: '30天',\n            contact: '张经理',\n            phone: '138-0000-0001'\n        },\n        {\n            id: 'sup_002',\n            name: '标准件公司B',\n            category: '标准件',\n            rating: 4.2,\n            delivery_rate: 92.3,\n            quality_score: 94.1,\n            payment_terms: '15天',\n            contact: '李经理',\n            phone: '138-0000-0002'\n        },\n        {\n            id: 'sup_003',\n            name: '轴承制造C',\n            category: '零部件',\n            rating: 4.6,\n            delivery_rate: 95.8,\n            quality_score: 97.5,\n            payment_terms: '45天',\n            contact: '王经理',\n            phone: '138-0000-0003'\n        }\n    ];\n    // 采购订单数据\n    const mockPurchaseOrders = [\n        {\n            id: 'po_001',\n            material: '螺栓',\n            quantity: 500,\n            unit_price: 0.8,\n            total_amount: 400,\n            supplier: '标准件公司B',\n            order_date: '2024-12-28',\n            expected_delivery: '2025-01-05',\n            status: 'pending'\n        },\n        {\n            id: 'po_002',\n            material: '钢板',\n            quantity: 800,\n            unit_price: 8.5,\n            total_amount: 6800,\n            supplier: '钢铁集团A',\n            order_date: '2024-12-27',\n            expected_delivery: '2025-01-03',\n            status: 'confirmed'\n        },\n        {\n            id: 'po_003',\n            material: '电机',\n            quantity: 20,\n            unit_price: 280,\n            total_amount: 5600,\n            supplier: '电机厂D',\n            order_date: '2024-12-26',\n            expected_delivery: '2025-01-08',\n            status: 'shipped'\n        }\n    ];\n    // 库存表格列\n    const inventoryColumns = [\n        {\n            title: '物料信息',\n            key: 'material',\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'var(--text-primary)',\n                                fontWeight: 500\n                            },\n                            children: record.material\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: [\n                                record.category,\n                                \" | \",\n                                record.id\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '库存状态',\n            key: 'stock_status',\n            render: (_, record)=>{\n                const getStockStatus = (current, min, max)=>{\n                    if (current <= min) return {\n                        status: 'critical',\n                        text: '严重不足',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 80\n                        }, undefined)\n                    };\n                    if (current <= min * 1.5) return {\n                        status: 'low',\n                        text: '库存偏低',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 81\n                        }, undefined)\n                    };\n                    return {\n                        status: 'normal',\n                        text: '库存正常',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 58\n                        }, undefined)\n                    };\n                };\n                const statusInfo = getStockStatus(record.current_stock, record.minimum_stock, record.maximum_stock);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `stock-status ${statusInfo.status}`,\n                            children: [\n                                statusInfo.icon,\n                                statusInfo.text\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '4px',\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: [\n                                \"当前: \",\n                                record.current_stock,\n                                \" \",\n                                record.unit\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, undefined);\n            }\n        },\n        {\n            title: '库存量',\n            key: 'stock_level',\n            render: (_, record)=>{\n                const percentage = record.current_stock / record.maximum_stock * 100;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            percent: percentage,\n                            size: \"small\",\n                            strokeColor: {\n                                '0%': '#ff4757',\n                                '30%': '#ff8c42',\n                                '60%': '#4a90e2',\n                                '100%': '#00d4aa'\n                            },\n                            style: {\n                                marginBottom: '4px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: [\n                                \"最小: \",\n                                record.minimum_stock,\n                                \" | 最大: \",\n                                record.maximum_stock\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, undefined);\n            }\n        },\n        {\n            title: '单价',\n            dataIndex: 'unit_price',\n            key: 'unit_price',\n            render: (price, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'var(--text-primary)',\n                                fontWeight: 500\n                            },\n                            children: [\n                                \"\\xa5\",\n                                price.toFixed(2),\n                                \"/\",\n                                record.unit\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: [\n                                \"库存价值: \\xa5\",\n                                (price * record.current_stock).toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '供应商',\n            dataIndex: 'supplier',\n            key: 'supplier'\n        },\n        {\n            title: '最后采购',\n            dataIndex: 'last_purchase',\n            key: 'last_purchase',\n            render: (date)=>dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).format('MM-DD')\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: \"small\",\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            form.setFieldsValue({\n                                material: record.material,\n                                supplier: record.supplier,\n                                unit_price: record.unit_price\n                            });\n                            setPurchaseModalVisible(true);\n                        },\n                        children: \"采购\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 供应商表格列\n    const supplierColumns = [\n        {\n            title: '供应商名称',\n            dataIndex: 'name',\n            key: 'name',\n            render: (text, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'var(--text-primary)',\n                                fontWeight: 500\n                            },\n                            children: text\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: record.category\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '评级',\n            dataIndex: 'rating',\n            key: 'rating',\n            render: (rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'var(--color-accent-green)',\n                        fontWeight: 600\n                    },\n                    children: [\n                        rating.toFixed(1),\n                        \" ⭐\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '交付率',\n            dataIndex: 'delivery_rate',\n            key: 'delivery_rate',\n            render: (rate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            percent: rate,\n                            size: \"small\",\n                            strokeColor: \"var(--status-success)\"\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: '12px'\n                            },\n                            children: [\n                                rate.toFixed(1),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '质量评分',\n            dataIndex: 'quality_score',\n            key: 'quality_score',\n            render: (score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: 'var(--text-primary)',\n                        fontWeight: 500\n                    },\n                    children: [\n                        score.toFixed(1),\n                        \"分\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '付款条件',\n            dataIndex: 'payment_terms',\n            key: 'payment_terms'\n        },\n        {\n            title: '联系方式',\n            key: 'contact',\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'var(--text-primary)'\n                            },\n                            children: record.contact\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '12px',\n                                color: 'var(--text-muted)'\n                            },\n                            children: record.phone\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 采购订单表格列\n    const orderColumns = [\n        {\n            title: '订单号',\n            dataIndex: 'id',\n            key: 'id',\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: 'var(--color-accent-blue)',\n                        fontWeight: 500\n                    },\n                    children: id\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '物料',\n            dataIndex: 'material',\n            key: 'material'\n        },\n        {\n            title: '数量',\n            dataIndex: 'quantity',\n            key: 'quantity'\n        },\n        {\n            title: '单价',\n            dataIndex: 'unit_price',\n            key: 'unit_price',\n            render: (price)=>`¥${price.toFixed(2)}`\n        },\n        {\n            title: '总金额',\n            dataIndex: 'total_amount',\n            key: 'total_amount',\n            render: (amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: 'var(--text-primary)',\n                        fontWeight: 600\n                    },\n                    children: [\n                        \"\\xa5\",\n                        amount.toLocaleString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: '供应商',\n            dataIndex: 'supplier',\n            key: 'supplier'\n        },\n        {\n            title: '预计交付',\n            dataIndex: 'expected_delivery',\n            key: 'expected_delivery',\n            render: (date)=>dayjs__WEBPACK_IMPORTED_MODULE_4___default()(date).format('MM-DD')\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: (status)=>{\n                const statusConfig = {\n                    pending: {\n                        text: '待确认',\n                        color: 'orange'\n                    },\n                    confirmed: {\n                        text: '已确认',\n                        color: 'blue'\n                    },\n                    shipped: {\n                        text: '已发货',\n                        color: 'green'\n                    },\n                    delivered: {\n                        text: '已交付',\n                        color: 'green'\n                    },\n                    cancelled: {\n                        text: '已取消',\n                        color: 'red'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 16\n                }, undefined);\n            }\n        }\n    ];\n    // 创建采购订单\n    const handleCreatePurchaseOrder = async (values)=>{\n        try {\n            // 这里调用API创建采购订单\n            _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"].success('采购订单创建成功！');\n            setPurchaseModalVisible(false);\n            form.resetFields();\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"].error('创建采购订单失败');\n        }\n    };\n    // 库存分类统计\n    const categoryData = [\n        {\n            category: '原材料',\n            value: 35,\n            count: 1\n        },\n        {\n            category: '零部件',\n            value: 45,\n            count: 2\n        },\n        {\n            category: '标准件',\n            value: 15,\n            count: 1\n        },\n        {\n            category: '辅料',\n            value: 5,\n            count: 1\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupplyChainContainer, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '24px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '24px',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                children: \"供应链管理\"\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        type: \"primary\",\n                                        onClick: ()=>setPurchaseModalVisible(true),\n                                        children: \"新建采购\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: refetchInventory,\n                                        loading: inventoryLoading,\n                                        children: \"刷新\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        gutter: [\n                            24,\n                            24\n                        ],\n                        style: {\n                            marginBottom: '24px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            span: 24,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                message: \"库存预警\",\n                                description: \"螺栓库存严重不足(45/200)，密封圈库存偏低(180/150)，建议立即安排采购。\",\n                                type: \"error\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 23\n                                }, void 0),\n                                showIcon: true,\n                                closable: true\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        gutter: [\n                            24,\n                            24\n                        ],\n                        style: {\n                            marginBottom: '32px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                sm: 12,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            title: \"库存物料种类\",\n                                            value: mockInventory.length,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                style: {\n                                                    color: 'var(--color-accent-blue)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 29\n                                            }, void 0),\n                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    end: value,\n                                                    duration: 2\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 43\n                                                }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                sm: 12,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            title: \"库存预警\",\n                                            value: mockInventory.filter((item)=>item.status !== 'normal').length,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                style: {\n                                                    color: 'var(--status-warning)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 29\n                                            }, void 0),\n                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    end: value,\n                                                    duration: 2\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 43\n                                                }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                sm: 12,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            title: \"库存总价值\",\n                                            value: mockInventory.reduce((acc, item)=>acc + item.current_stock * item.unit_price, 0),\n                                            precision: 0,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                style: {\n                                                    color: 'var(--color-accent-green)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 29\n                                            }, void 0),\n                                            formatter: (value)=>`¥${value.toLocaleString()}`\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                sm: 12,\n                                lg: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            title: \"活跃供应商\",\n                                            value: mockSuppliers.length,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_ExclamationCircleOutlined_PlusOutlined_ReloadOutlined_ShopOutlined_ShoppingCartOutlined_WarningOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                style: {\n                                                    color: 'var(--status-success)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 29\n                                            }, void 0),\n                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    end: value,\n                                                    duration: 2\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 43\n                                                }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        gutter: [\n                            24,\n                            24\n                        ],\n                        style: {\n                            marginBottom: '32px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                lg: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        title: \"库存分类分布\",\n                                        style: {\n                                            height: '400px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            data: categoryData,\n                                            angleField: \"value\",\n                                            colorField: \"category\",\n                                            radius: 0.8,\n                                            innerRadius: 0.4,\n                                            label: {\n                                                type: 'outer',\n                                                content: '{name}: {percentage}%'\n                                            },\n                                            color: [\n                                                '#4a90e2',\n                                                '#00d4aa',\n                                                '#ff8c42',\n                                                '#ff4757'\n                                            ]\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                xs: 24,\n                                lg: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"supply-card\",\n                                        title: \"库存水平对比\",\n                                        style: {\n                                            height: '400px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            data: mockInventory.map((item)=>({\n                                                    material: item.material,\n                                                    current: item.current_stock,\n                                                    minimum: item.minimum_stock,\n                                                    maximum: item.maximum_stock\n                                                })),\n                                            xField: \"material\",\n                                            yField: \"current\",\n                                            color: \"var(--color-accent-blue)\",\n                                            columnStyle: {\n                                                radius: [\n                                                    4,\n                                                    4,\n                                                    0,\n                                                    0\n                                                ]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.7\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            className: \"supply-card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                defaultActiveKey: \"inventory\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                                        tab: \"库存管理\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            dataSource: mockInventory,\n                                            columns: inventoryColumns,\n                                            rowKey: \"id\",\n                                            pagination: {\n                                                pageSize: 10,\n                                                showSizeChanger: true,\n                                                showQuickJumper: true,\n                                                showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                                            },\n                                            loading: inventoryLoading\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, \"inventory\", false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                                        tab: \"供应商管理\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            dataSource: mockSuppliers,\n                                            columns: supplierColumns,\n                                            rowKey: \"id\",\n                                            pagination: false\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, \"suppliers\", false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                                        tab: \"采购订单\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            dataSource: mockPurchaseOrders,\n                                            columns: orderColumns,\n                                            rowKey: \"id\",\n                                            pagination: false\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, \"orders\", false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        title: \"创建采购订单\",\n                        open: purchaseModalVisible,\n                        onCancel: ()=>{\n                            setPurchaseModalVisible(false);\n                            form.resetFields();\n                        },\n                        onOk: ()=>form.submit(),\n                        okText: \"创建订单\",\n                        cancelText: \"取消\",\n                        width: 600,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            form: form,\n                            layout: \"vertical\",\n                            onFinish: handleCreatePurchaseOrder,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"material\",\n                                    label: \"物料名称\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请输入物料名称'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        placeholder: \"请输入物料名称\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"quantity\",\n                                    label: \"采购数量\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请输入采购数量'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        type: \"number\",\n                                        placeholder: \"请输入采购数量\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"unit_price\",\n                                    label: \"单价\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请输入单价'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        type: \"number\",\n                                        step: \"0.01\",\n                                        placeholder: \"请输入单价\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"supplier\",\n                                    label: \"供应商\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请选择供应商'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        placeholder: \"请选择供应商\",\n                                        children: mockSuppliers.map((supplier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: supplier.name,\n                                                children: supplier.name\n                                            }, supplier.id, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"expected_delivery\",\n                                    label: \"期望交付日期\",\n                                    rules: [\n                                        {\n                                            required: true,\n                                            message: '请选择期望交付日期'\n                                        }\n                                    ],\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        type: \"date\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                    name: \"notes\",\n                                    label: \"备注\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Form_Input_Modal_Progress_Row_Select_Space_Statistic_Table_Tabs_Tag_message_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"].TextArea, {\n                                        rows: 3,\n                                        placeholder: \"请输入备注信息\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n            lineNumber: 503,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\supply-chain\\\\page.tsx\",\n        lineNumber: 502,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SupplyChainPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/supply-chain/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Dropdown,Layout,Menu,Space!=!antd */ \"(ssr)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ToolOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MonitorOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,MonitorOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"(ssr)/../node_modules/styled-components/dist/styled-components.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n// 样式组件\nconst StyledLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"]))`\n  min-height: 100vh;\n  background: var(--bg-primary);\n`;\nconst StyledHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Header)`\n  background: var(--bg-secondary);\n  border-bottom: 1px solid var(--border-primary);\n  padding: 0 24px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: linear-gradient(90deg, \n      transparent 0%, \n      var(--color-accent-blue) 50%, \n      transparent 100%);\n    opacity: 0.6;\n  }\n`;\nconst StyledSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Sider)`\n  background: var(--bg-secondary);\n  border-right: 1px solid var(--border-primary);\n  \n  .ant-layout-sider-trigger {\n    background: var(--bg-primary);\n    border-top: 1px solid var(--border-primary);\n    color: var(--text-secondary);\n    \n    &:hover {\n      background: var(--color-accent-blue);\n      color: white;\n    }\n  }\n`;\nconst StyledContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Content)`\n  background: var(--bg-primary);\n  padding: 24px;\n  overflow-y: auto;\n`;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 20px;\n  font-weight: 700;\n  color: var(--text-primary);\n  \n  .logo-icon {\n    width: 32px;\n    height: 32px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 6px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: white;\n    font-size: 16px;\n  }\n`;\nconst StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  box-shadow: 0 0 10px ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  animation: pulse 2s infinite;\n`;\nconst HeaderActions = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n`;\n// 菜单配置\nconst menuItems = [\n    {\n        key: '/',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 130,\n            columnNumber: 11\n        }, undefined),\n        label: '总览仪表板',\n        path: '/'\n    },\n    {\n        key: '/production',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 136,\n            columnNumber: 11\n        }, undefined),\n        label: '生产管理',\n        path: '/production'\n    },\n    {\n        key: '/maintenance',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 142,\n            columnNumber: 11\n        }, undefined),\n        label: '设备维护',\n        path: '/maintenance'\n    },\n    {\n        key: '/quality',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 148,\n            columnNumber: 11\n        }, undefined),\n        label: '质量管理',\n        path: '/quality'\n    },\n    {\n        key: '/supply-chain',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 154,\n            columnNumber: 11\n        }, undefined),\n        label: '供应链',\n        path: '/supply-chain'\n    },\n    {\n        key: '/knowledge',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 160,\n            columnNumber: 11\n        }, undefined),\n        label: '知识管理',\n        path: '/knowledge'\n    },\n    {\n        key: '/agents',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 166,\n            columnNumber: 11\n        }, undefined),\n        label: '智能体',\n        path: '/agents',\n        children: [\n            {\n                key: '/agents/chat',\n                label: '智能对话',\n                path: '/agents/chat'\n            },\n            {\n                key: '/agents/orchestration',\n                label: '智能体编排',\n                path: '/agents/orchestration'\n            }\n        ]\n    },\n    {\n        key: '/monitoring',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 176,\n            columnNumber: 11\n        }, undefined),\n        label: '系统监控',\n        path: '/monitoring'\n    }\n];\nconst MainLayout = ({ children })=>{\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('online');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 模拟系统状态检查\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MainLayout.useEffect\": ()=>{\n            const checkSystemStatus = {\n                \"MainLayout.useEffect.checkSystemStatus\": ()=>{\n                    // 这里可以调用实际的健康检查API\n                    const statuses = [\n                        'online',\n                        'warning',\n                        'offline'\n                    ];\n                    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];\n                    setSystemStatus(randomStatus);\n                }\n            }[\"MainLayout.useEffect.checkSystemStatus\"];\n            const interval = setInterval(checkSystemStatus, 30000); // 30秒检查一次\n            return ({\n                \"MainLayout.useEffect\": ()=>clearInterval(interval)\n            })[\"MainLayout.useEffect\"];\n        }\n    }[\"MainLayout.useEffect\"], []);\n    // 用户菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined),\n            label: '个人资料'\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 214,\n                columnNumber: 13\n            }, undefined),\n            label: '系统设置'\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 222,\n                columnNumber: 13\n            }, undefined),\n            label: '退出登录',\n            danger: true\n        }\n    ];\n    // 构建菜单项\n    const buildMenuItems = (items)=>{\n        return items.map((item)=>({\n                key: item.key,\n                icon: item.icon,\n                label: item.children ? item.label : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.path,\n                    style: {\n                        color: 'inherit',\n                        textDecoration: 'none'\n                    },\n                    children: item.label\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined),\n                children: item.children?.map((child)=>({\n                        key: child.key,\n                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: child.path,\n                            style: {\n                                color: 'inherit',\n                                textDecoration: 'none'\n                            },\n                            children: child.label\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined)\n                    }))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledSider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 240,\n                collapsedWidth: 80,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        style: {\n                            padding: '16px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"logo-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                                    children: !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            width: 'auto'\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"工业智能体\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        selectedKeys: [\n                            pathname\n                        ],\n                        defaultOpenKeys: [\n                            '/production',\n                            '/maintenance',\n                            '/quality'\n                        ],\n                        items: buildMenuItems(menuItems),\n                        style: {\n                            border: 'none'\n                        }\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: ()=>setCollapsed(!collapsed),\n                                        style: {\n                                            fontSize: '16px',\n                                            width: 40,\n                                            height: 40,\n                                            color: 'var(--text-secondary)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIndicator, {\n                                                status: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)',\n                                                    fontSize: '14px'\n                                                },\n                                                children: [\n                                                    \"系统状态: \",\n                                                    systemStatus === 'online' ? '正常' : systemStatus === 'warning' ? '警告' : '离线'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderActions, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                color: 'var(--text-secondary)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        trigger: [\n                                            'click'\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            style: {\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Dropdown_Layout_Menu_Space_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_MonitorOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        background: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: 'var(--text-primary)'\n                                                    },\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAiChat: () => (/* binding */ useAiChat),\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useBatchOperations: () => (/* binding */ useBatchOperations),\n/* harmony export */   useDashboard: () => (/* binding */ useDashboard),\n/* harmony export */   useEquipmentStatus: () => (/* binding */ useEquipmentStatus),\n/* harmony export */   useInventoryStatus: () => (/* binding */ useInventoryStatus),\n/* harmony export */   useProductionAnalytics: () => (/* binding */ useProductionAnalytics),\n/* harmony export */   useProductionLines: () => (/* binding */ useProductionLines),\n/* harmony export */   useQualityMetrics: () => (/* binding */ useQualityMetrics),\n/* harmony export */   useRealTimeData: () => (/* binding */ useRealTimeData),\n/* harmony export */   useSystemHealth: () => (/* binding */ useSystemHealth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/**\n * API数据获取Hook\n */ \n\n// 通用API Hook\nfunction useApi(apiCall, dependencies = []) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApi.useCallback[fetchData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await apiCall();\n                setData(response.data);\n            } catch (err) {\n                setError((0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(err));\n                console.error('API Error:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useApi.useCallback[fetchData]\"], dependencies);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApi.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"useApi.useEffect\"], [\n        fetchData\n    ]);\n    const refetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApi.useCallback[refetch]\": ()=>{\n            fetchData();\n        }\n    }[\"useApi.useCallback[refetch]\"], [\n        fetchData\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        refetch\n    };\n}\n// 仪表板数据Hook\nfunction useDashboard() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.dashboard);\n}\n// 生产线数据Hook\nfunction useProductionLines() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.production.getLines);\n}\n// 设备状态Hook\nfunction useEquipmentStatus() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.equipment.getStatus);\n}\n// 质量指标Hook\nfunction useQualityMetrics() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.quality.getMetrics);\n}\n// 库存状态Hook\nfunction useInventoryStatus() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.inventory.getStatus);\n}\n// 生产分析Hook\nfunction useProductionAnalytics() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.production.getAnalytics);\n}\n// AI聊天Hook\nfunction useAiChat() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAiChat.useCallback[sendMessage]\": async (query)=>{\n            const userMessage = {\n                id: Date.now().toString(),\n                type: 'user',\n                content: query,\n                timestamp: new Date()\n            };\n            setMessages({\n                \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        userMessage\n                    ]\n            }[\"useAiChat.useCallback[sendMessage]\"]);\n            setLoading(true);\n            try {\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_1__.api.ai.chat(query);\n                const aiMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'ai',\n                    content: response.data.response,\n                    timestamp: new Date()\n                };\n                setMessages({\n                    \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            aiMessage\n                        ]\n                }[\"useAiChat.useCallback[sendMessage]\"]);\n            } catch (error) {\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'ai',\n                    content: '抱歉，我现在无法回答您的问题。请稍后再试。',\n                    timestamp: new Date()\n                };\n                setMessages({\n                    \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            errorMessage\n                        ]\n                }[\"useAiChat.useCallback[sendMessage]\"]);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useAiChat.useCallback[sendMessage]\"], []);\n    const clearMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAiChat.useCallback[clearMessages]\": ()=>{\n            setMessages([]);\n        }\n    }[\"useAiChat.useCallback[clearMessages]\"], []);\n    return {\n        messages,\n        loading,\n        sendMessage,\n        clearMessages\n    };\n}\n// 实时数据Hook（模拟WebSocket）\nfunction useRealTimeData(apiCall, interval = 30000 // 30秒刷新一次\n) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[fetchData]\": async ()=>{\n            try {\n                setError(null);\n                const response = await apiCall();\n                setData(response.data);\n                setLastUpdate(new Date());\n            } catch (err) {\n                setError((0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(err));\n                console.error('Real-time data error:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useRealTimeData.useCallback[fetchData]\"], [\n        apiCall\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeData.useEffect\": ()=>{\n            // 初始加载\n            fetchData();\n            // 设置定时器\n            const timer = setInterval(fetchData, interval);\n            return ({\n                \"useRealTimeData.useEffect\": ()=>clearInterval(timer)\n            })[\"useRealTimeData.useEffect\"];\n        }\n    }[\"useRealTimeData.useEffect\"], [\n        fetchData,\n        interval\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[forceRefresh]\": ()=>{\n            setLoading(true);\n            fetchData();\n        }\n    }[\"useRealTimeData.useCallback[forceRefresh]\"], [\n        fetchData\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        lastUpdate,\n        forceRefresh\n    };\n}\n// 系统健康状态Hook\nfunction useSystemHealth() {\n    const [isHealthy, setIsHealthy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSystemHealth.useEffect\": ()=>{\n            const checkHealth = {\n                \"useSystemHealth.useEffect.checkHealth\": async ()=>{\n                    try {\n                        await _services_api__WEBPACK_IMPORTED_MODULE_1__.api.health();\n                        setIsHealthy(true);\n                    } catch (error) {\n                        setIsHealthy(false);\n                    } finally{\n                        setLastCheck(new Date());\n                    }\n                }\n            }[\"useSystemHealth.useEffect.checkHealth\"];\n            // 初始检查\n            checkHealth();\n            // 每30秒检查一次\n            const timer = setInterval(checkHealth, 30000);\n            return ({\n                \"useSystemHealth.useEffect\": ()=>clearInterval(timer)\n            })[\"useSystemHealth.useEffect\"];\n        }\n    }[\"useSystemHealth.useEffect\"], []);\n    return {\n        isHealthy,\n        lastCheck\n    };\n}\n// 批量操作Hook\nfunction useBatchOperations() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const executeBatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useBatchOperations.useCallback[executeBatch]\": async (operations)=>{\n            setLoading(true);\n            setResults([]);\n            const batchResults = await Promise.allSettled(operations.map({\n                \"useBatchOperations.useCallback[executeBatch]\": (operation)=>operation()\n            }[\"useBatchOperations.useCallback[executeBatch]\"]));\n            const formattedResults = batchResults.map({\n                \"useBatchOperations.useCallback[executeBatch].formattedResults\": (result)=>{\n                    if (result.status === 'fulfilled') {\n                        return {\n                            success: true,\n                            data: result.value.data\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            error: (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(result.reason)\n                        };\n                    }\n                }\n            }[\"useBatchOperations.useCallback[executeBatch].formattedResults\"]);\n            setResults(formattedResults);\n            setLoading(false);\n            return formattedResults;\n        }\n    }[\"useBatchOperations.useCallback[executeBatch]\"], []);\n    return {\n        loading,\n        results,\n        executeBatch\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatApiError: () => (/* binding */ formatApiError),\n/* harmony export */   isApiAvailable: () => (/* binding */ isApiAvailable)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../node_modules/axios/lib/axios.js\");\n/**\n * API服务 - 连接后端工业智能体平台API\n */ \n// API基础配置\nconst API_BASE_URL = 'http://localhost:8888';\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 可以在这里添加认证token等\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n}, (error)=>{\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\n    return Promise.reject(error);\n});\n// API接口定义\nconst api = {\n    // 基础接口\n    health: ()=>apiClient.get('/health'),\n    dashboard: ()=>apiClient.get('/dashboard'),\n    // 生产管理\n    production: {\n        getLines: ()=>apiClient.get('/production/lines'),\n        createPlan: (plan)=>apiClient.post('/production/plan', plan),\n        getAnalytics: ()=>apiClient.get('/analytics/production')\n    },\n    // 设备管理\n    equipment: {\n        getStatus: ()=>apiClient.get('/equipment'),\n        scheduleMaintenance: (task)=>apiClient.post('/maintenance/schedule', task)\n    },\n    // 质量管理\n    quality: {\n        getMetrics: ()=>apiClient.get('/quality/metrics'),\n        recordInspection: (inspection)=>apiClient.post('/quality/inspection', inspection)\n    },\n    // 库存管理\n    inventory: {\n        getStatus: ()=>apiClient.get('/inventory')\n    },\n    // AI智能助手\n    ai: {\n        chat: (query)=>apiClient.get('/ai/chat', {\n                params: {\n                    query\n                }\n            })\n    }\n};\n// 工具函数\nconst formatApiError = (error)=>{\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    }\n    if (error.message) {\n        return error.message;\n    }\n    return '网络错误，请稍后重试';\n};\nconst isApiAvailable = async ()=>{\n    try {\n        await api.health();\n        return true;\n    } catch (error) {\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?8873":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/framer-motion","vendor-chunks/@rc-component","vendor-chunks/motion-dom","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/@babel","vendor-chunks/styled-components","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/stylis","vendor-chunks/rc-pagination","vendor-chunks/rc-dialog","vendor-chunks/rc-overflow","vendor-chunks/rc-collapse","vendor-chunks/dayjs","vendor-chunks/motion-utils","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/@emotion","vendor-chunks/rc-picker","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/shallowequal","vendor-chunks/@antv","vendor-chunks/d3-array","vendor-chunks/d3-shape","vendor-chunks/d3-geo","vendor-chunks/axios","vendor-chunks/d3-scale-chromatic","vendor-chunks/rc-table","vendor-chunks/d3-hierarchy","vendor-chunks/rc-select","vendor-chunks/rc-virtual-list","vendor-chunks/d3-format","vendor-chunks/d3-quadtree","vendor-chunks/rc-tree","vendor-chunks/d3-force","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/rc-progress","vendor-chunks/gl-matrix","vendor-chunks/es-errors","vendor-chunks/d3-interpolate","vendor-chunks/d3-dsv","vendor-chunks/rc-input","vendor-chunks/call-bind-apply-helpers","vendor-chunks/d3-color","vendor-chunks/rc-textarea","vendor-chunks/get-proto","vendor-chunks/d3-path","vendor-chunks/pdfast","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/eventemitter3","vendor-chunks/tslib","vendor-chunks/throttle-debounce","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/internmap","vendor-chunks/flru","vendor-chunks/d3-timer","vendor-chunks/d3-dispatch","vendor-chunks/compute-scroll-into-view","vendor-chunks/simple-swizzle","vendor-chunks/react-countup","vendor-chunks/rc-checkbox","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/lodash","vendor-chunks/is-arrayish","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/fecha","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/countup.js","vendor-chunks/combined-stream","vendor-chunks/color-string","vendor-chunks/color-name"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsupply-chain%2Fpage&page=%2Fsupply-chain%2Fpage&appPaths=%2Fsupply-chain%2Fpage&pagePath=private-next-app-dir%2Fsupply-chain%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();