"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(app-pages-browser)/../node_modules/antd/es/app/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(app-pages-browser)/../node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(app-pages-browser)/../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/locale/zh-cn */ \"(app-pages-browser)/../node_modules/dayjs/locale/zh-cn.js\");\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/relativeTime */ \"(app-pages-browser)/../node_modules/dayjs/plugin/relativeTime.js\");\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/utc */ \"(app-pages-browser)/../node_modules/dayjs/plugin/utc.js\");\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/timezone */ \"(app-pages-browser)/../node_modules/dayjs/plugin/timezone.js\");\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./globals.css */ \"(app-pages-browser)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// 配置dayjs\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().locale('zh-cn');\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default()));\n// 创建QueryClient实例\nvar queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 3,\n            retryDelay: function(attemptIndex) {\n                return Math.min(1000 * Math.pow(2, attemptIndex), 30000);\n            },\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000\n        },\n        mutations: {\n            retry: 1\n        }\n    }\n});\n// Ant Design主题配置\nvar antdTheme = {\n    token: {\n        colorPrimary: '#1890ff',\n        colorSuccess: '#52c41a',\n        colorWarning: '#faad14',\n        colorError: '#ff4d4f',\n        colorInfo: '#1890ff',\n        borderRadius: 6,\n        wireframe: false\n    },\n    components: {\n        Layout: {\n            headerBg: '#001529',\n            siderBg: '#001529'\n        },\n        Menu: {\n            darkItemBg: '#001529',\n            darkSubMenuItemBg: '#000c17',\n            darkItemSelectedBg: '#1890ff'\n        }\n    }\n};\nfunction RootLayout(param) {\n    var children = param.children;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"基于大语言模型的工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                            theme: antdTheme,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQzRCO0FBQ2pCO0FBQ29DO0FBQ0w7QUFDMUM7QUFDRTtBQUN5QjtBQUNsQjtBQUNVO0FBRXRCO0FBRXZCLFVBQVU7QUFDVlEsbURBQVksQ0FBQztBQUNiQSxtREFBWSxDQUFDQyxrRUFBWUE7QUFDekJELG1EQUFZLENBQUNFLHlEQUFHQTtBQUNoQkYsbURBQVksQ0FBQ0csOERBQVFBO0FBRXJCLGtCQUFrQjtBQUNsQixJQUFNRyxjQUFjLElBQUlULDhEQUFXQSxDQUFDO0lBQ2xDVSxnQkFBZ0I7UUFDZEMsU0FBUztZQUNQQyxPQUFPO1lBQ1BDLFlBQVksU0FBQ0M7dUJBQWlCQyxLQUFLQyxHQUFHLENBQUMsT0FBTyxZQUFLRixlQUFjOztZQUNqRUcsV0FBVyxJQUFJLEtBQUs7WUFDcEJDLFFBQVEsS0FBSyxLQUFLO1FBQ3BCO1FBQ0FDLFdBQVc7WUFDVFAsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBLGlCQUFpQjtBQUNqQixJQUFNUSxZQUFZO0lBQ2hCQyxPQUFPO1FBQ0xDLGNBQWM7UUFDZEMsY0FBYztRQUNkQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFdBQVc7SUFDYjtJQUNBQyxZQUFZO1FBQ1ZDLFFBQVE7WUFDTkMsVUFBVTtZQUNWQyxTQUFTO1FBQ1g7UUFDQUMsTUFBTTtZQUNKQyxZQUFZO1lBQ1pDLG1CQUFtQjtZQUNuQkMsb0JBQW9CO1FBQ3RCO0lBQ0Y7QUFDRjtBQUVlLFNBQVNDLFdBQVcsS0FJbEM7UUFKa0MsaUJBQ2pDQztJQUlBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7O2tDQUNDLDhEQUFDQztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXhCLDhEQUFDQzswQkFDQyw0RUFBQ2hELHNFQUFtQkE7b0JBQUNpRCxRQUFRekM7O3NDQUMzQiw4REFBQ2IsdUZBQWNBOzRCQUFDVyxRQUFRUiwwREFBSUE7NEJBQUVvRCxPQUFPL0I7c0NBQ25DLDRFQUFDdEIsdUZBQU9BOzBDQUNMd0M7Ozs7Ozs7Ozs7O3NDQUdMLDhEQUFDcEMsK0VBQWtCQTs0QkFBQ2tELGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdDO0tBekJ3QmYiLCJzb3VyY2VzIjpbIko6XFxhdWdtZW50XFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcd2ViLWFwcFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ29uZmlnUHJvdmlkZXIsIEFwcCBhcyBBbnRkQXBwIH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgemhDTiBmcm9tICdhbnRkL2xvY2FsZS96aF9DTic7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnO1xuaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJztcbmltcG9ydCAnZGF5anMvbG9jYWxlL3poLWNuJztcbmltcG9ydCByZWxhdGl2ZVRpbWUgZnJvbSAnZGF5anMvcGx1Z2luL3JlbGF0aXZlVGltZSc7XG5pbXBvcnQgdXRjIGZyb20gJ2RheWpzL3BsdWdpbi91dGMnO1xuaW1wb3J0IHRpbWV6b25lIGZyb20gJ2RheWpzL3BsdWdpbi90aW1lem9uZSc7XG5cbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbi8vIOmFjee9rmRheWpzXG5kYXlqcy5sb2NhbGUoJ3poLWNuJyk7XG5kYXlqcy5leHRlbmQocmVsYXRpdmVUaW1lKTtcbmRheWpzLmV4dGVuZCh1dGMpO1xuZGF5anMuZXh0ZW5kKHRpbWV6b25lKTtcblxuLy8g5Yib5bu6UXVlcnlDbGllbnTlrp7kvotcbmNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICByZXRyeTogMyxcbiAgICAgIHJldHJ5RGVsYXk6IChhdHRlbXB0SW5kZXgpID0+IE1hdGgubWluKDEwMDAgKiAyICoqIGF0dGVtcHRJbmRleCwgMzAwMDApLFxuICAgICAgc3RhbGVUaW1lOiA1ICogNjAgKiAxMDAwLCAvLyA15YiG6ZKfXG4gICAgICBnY1RpbWU6IDEwICogNjAgKiAxMDAwLCAvLyAxMOWIhumSnyAo5paw54mI5pys5L2/55SoZ2NUaW1l5pu/5LujY2FjaGVUaW1lKVxuICAgIH0sXG4gICAgbXV0YXRpb25zOiB7XG4gICAgICByZXRyeTogMSxcbiAgICB9LFxuICB9LFxufSk7XG5cbi8vIEFudCBEZXNpZ27kuLvpopjphY3nva5cbmNvbnN0IGFudGRUaGVtZSA9IHtcbiAgdG9rZW46IHtcbiAgICBjb2xvclByaW1hcnk6ICcjMTg5MGZmJyxcbiAgICBjb2xvclN1Y2Nlc3M6ICcjNTJjNDFhJyxcbiAgICBjb2xvcldhcm5pbmc6ICcjZmFhZDE0JyxcbiAgICBjb2xvckVycm9yOiAnI2ZmNGQ0ZicsXG4gICAgY29sb3JJbmZvOiAnIzE4OTBmZicsXG4gICAgYm9yZGVyUmFkaXVzOiA2LFxuICAgIHdpcmVmcmFtZTogZmFsc2UsXG4gIH0sXG4gIGNvbXBvbmVudHM6IHtcbiAgICBMYXlvdXQ6IHtcbiAgICAgIGhlYWRlckJnOiAnIzAwMTUyOScsXG4gICAgICBzaWRlckJnOiAnIzAwMTUyOScsXG4gICAgfSxcbiAgICBNZW51OiB7XG4gICAgICBkYXJrSXRlbUJnOiAnIzAwMTUyOScsXG4gICAgICBkYXJrU3ViTWVudUl0ZW1CZzogJyMwMDBjMTcnLFxuICAgICAgZGFya0l0ZW1TZWxlY3RlZEJnOiAnIzE4OTBmZicsXG4gICAgfSxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIj5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8dGl0bGU+5bel5Lia5pm66IO95L2T5bmz5Y+wPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIuWfuuS6juWkp+ivreiogOaooeWei+eahOW3peS4muaZuuiDveS9k+W5s+WPsFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8UXVlcnlDbGllbnRQcm92aWRlciBjbGllbnQ9e3F1ZXJ5Q2xpZW50fT5cbiAgICAgICAgICA8Q29uZmlnUHJvdmlkZXIgbG9jYWxlPXt6aENOfSB0aGVtZT17YW50ZFRoZW1lfT5cbiAgICAgICAgICAgIDxBbnRkQXBwPlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L0FudGRBcHA+XG4gICAgICAgICAgPC9Db25maWdQcm92aWRlcj5cbiAgICAgICAgICA8UmVhY3RRdWVyeURldnRvb2xzIGluaXRpYWxJc09wZW49e2ZhbHNlfSAvPlxuICAgICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29uZmlnUHJvdmlkZXIiLCJBcHAiLCJBbnRkQXBwIiwiemhDTiIsIlF1ZXJ5Q2xpZW50IiwiUXVlcnlDbGllbnRQcm92aWRlciIsIlJlYWN0UXVlcnlEZXZ0b29scyIsImRheWpzIiwicmVsYXRpdmVUaW1lIiwidXRjIiwidGltZXpvbmUiLCJsb2NhbGUiLCJleHRlbmQiLCJxdWVyeUNsaWVudCIsImRlZmF1bHRPcHRpb25zIiwicXVlcmllcyIsInJldHJ5IiwicmV0cnlEZWxheSIsImF0dGVtcHRJbmRleCIsIk1hdGgiLCJtaW4iLCJzdGFsZVRpbWUiLCJnY1RpbWUiLCJtdXRhdGlvbnMiLCJhbnRkVGhlbWUiLCJ0b2tlbiIsImNvbG9yUHJpbWFyeSIsImNvbG9yU3VjY2VzcyIsImNvbG9yV2FybmluZyIsImNvbG9yRXJyb3IiLCJjb2xvckluZm8iLCJib3JkZXJSYWRpdXMiLCJ3aXJlZnJhbWUiLCJjb21wb25lbnRzIiwiTGF5b3V0IiwiaGVhZGVyQmciLCJzaWRlckJnIiwiTWVudSIsImRhcmtJdGVtQmciLCJkYXJrU3ViTWVudUl0ZW1CZyIsImRhcmtJdGVtU2VsZWN0ZWRCZyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsInRpdGxlIiwibWV0YSIsIm5hbWUiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5IiwiY2xpZW50IiwidGhlbWUiLCJpbml0aWFsSXNPcGVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ })

});