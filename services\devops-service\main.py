"""
工业智能体平台 - 运维服务
实现部署管理、配置管理、日志管理等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import subprocess
import yaml
import docker
import kubernetes
from kubernetes import client, config
import git
import uuid
from enum import Enum
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
docker_client = None
k8s_client = None

class DeploymentStatus(str, Enum):
    PENDING = "pending"
    DEPLOYING = "deploying"
    DEPLOYED = "deployed"
    FAILED = "failed"
    ROLLING_BACK = "rolling_back"
    ROLLED_BACK = "rolled_back"

class EnvironmentType(str, Enum):
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class ConfigType(str, Enum):
    APPLICATION = "application"
    DATABASE = "database"
    INFRASTRUCTURE = "infrastructure"
    SECURITY = "security"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer, docker_client, k8s_client

    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )

    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )

    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()

    # Docker客户端
    try:
        docker_client = docker.from_env()
    except Exception as e:
        logger.warning(f"Docker client initialization failed: {e}")
        docker_client = None

    # Kubernetes客户端
    try:
        config.load_incluster_config()  # 在集群内运行
    except:
        try:
            config.load_kube_config()  # 本地开发
        except Exception as e:
            logger.warning(f"Kubernetes config load failed: {e}")

    try:
        k8s_client = client.ApiClient()
    except Exception as e:
        logger.warning(f"Kubernetes client initialization failed: {e}")
        k8s_client = None

    # 初始化数据库表
    await initialize_database()

    logger.info("DevOps service initialized")

    yield

    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    if docker_client:
        docker_client.close()

    logger.info("DevOps service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="运维服务",
    description="部署管理、配置管理、日志管理",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class DeploymentConfig(BaseModel):
    service_name: str
    version: str
    environment: EnvironmentType
    image: str
    replicas: int = 1
    resources: Dict[str, Any] = {}
    environment_variables: Dict[str, str] = {}
    volumes: List[Dict[str, str]] = []
    ports: List[Dict[str, int]] = []

class ConfigurationItem(BaseModel):
    name: str
    config_type: ConfigType
    environment: EnvironmentType
    content: Dict[str, Any]
    description: Optional[str] = None
    is_encrypted: bool = False

class BackupConfig(BaseModel):
    name: str
    backup_type: str  # database, files, configuration
    source: str
    destination: str
    schedule: str  # cron expression
    retention_days: int = 30
    is_active: bool = True

class LogQuery(BaseModel):
    service_name: Optional[str] = None
    level: Optional[str] = None  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    search_text: Optional[str] = None
    limit: int = 1000

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 部署记录表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS deployments (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100) NOT NULL,
                version VARCHAR(50) NOT NULL,
                environment VARCHAR(20) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                image VARCHAR(500),
                config JSONB,
                deployed_by UUID REFERENCES users(id),
                started_at TIMESTAMP WITH TIME ZONE,
                completed_at TIMESTAMP WITH TIME ZONE,
                error_message TEXT,
                rollback_version VARCHAR(50),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 配置管理表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS configurations (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                config_name VARCHAR(200) NOT NULL,
                config_type VARCHAR(50) NOT NULL,
                environment VARCHAR(20) NOT NULL,
                content JSONB NOT NULL,
                description TEXT,
                is_encrypted BOOLEAN DEFAULT false,
                version INTEGER DEFAULT 1,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(config_name, environment)
            )
        """)

        # 备份任务表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS backup_jobs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                job_name VARCHAR(200) NOT NULL,
                backup_type VARCHAR(50) NOT NULL,
                source_path VARCHAR(500) NOT NULL,
                destination_path VARCHAR(500) NOT NULL,
                schedule_cron VARCHAR(100),
                retention_days INTEGER DEFAULT 30,
                is_active BOOLEAN DEFAULT true,
                last_run TIMESTAMP WITH TIME ZONE,
                next_run TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 备份记录表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS backup_records (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                job_id UUID NOT NULL REFERENCES backup_jobs(id),
                backup_file VARCHAR(500),
                file_size BIGINT,
                status VARCHAR(20) NOT NULL, -- success, failed, in_progress
                started_at TIMESTAMP WITH TIME ZONE,
                completed_at TIMESTAMP WITH TIME ZONE,
                error_message TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 系统日志表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS system_logs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100),
                log_level VARCHAR(20),
                message TEXT NOT NULL,
                details JSONB,
                timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

class DeploymentManager:
    """部署管理器"""

    @staticmethod
    async def deploy_service(config: DeploymentConfig, deployed_by: str) -> str:
        """部署服务"""
        try:
            deployment_id = str(uuid.uuid4())

            # 记录部署开始
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO deployments
                    (id, service_name, version, environment, status, image, config,
                     deployed_by, started_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                    deployment_id,
                    config.service_name,
                    config.version,
                    config.environment.value,
                    DeploymentStatus.DEPLOYING.value,
                    config.image,
                    json.dumps(config.dict()),
                    deployed_by,
                    datetime.utcnow()
                )

            # 执行部署
            if config.environment == EnvironmentType.PRODUCTION and k8s_client:
                success = await DeploymentManager._deploy_to_kubernetes(config)
            else:
                success = await DeploymentManager._deploy_to_docker(config)

            # 更新部署状态
            status = DeploymentStatus.DEPLOYED if success else DeploymentStatus.FAILED
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    UPDATE deployments
                    SET status = $1, completed_at = $2
                    WHERE id = $3
                    """,
                    status.value,
                    datetime.utcnow(),
                    deployment_id
                )

            # 发送事件
            await kafka_producer.send("devops_events", {
                "type": "deployment_completed",
                "deployment_id": deployment_id,
                "service_name": config.service_name,
                "version": config.version,
                "environment": config.environment.value,
                "status": status.value,
                "timestamp": datetime.utcnow().isoformat()
            })

            logger.info(f"Deployment {deployment_id} completed with status: {status.value}")
            return deployment_id

        except Exception as e:
            logger.error(f"Error deploying service: {e}")

            # 更新失败状态
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    UPDATE deployments
                    SET status = $1, completed_at = $2, error_message = $3
                    WHERE id = $4
                    """,
                    DeploymentStatus.FAILED.value,
                    datetime.utcnow(),
                    str(e),
                    deployment_id
                )

            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _deploy_to_kubernetes(config: DeploymentConfig) -> bool:
        """部署到Kubernetes"""
        try:
            apps_v1 = client.AppsV1Api()
            core_v1 = client.CoreV1Api()

            # 创建Deployment
            deployment_manifest = {
                "apiVersion": "apps/v1",
                "kind": "Deployment",
                "metadata": {
                    "name": config.service_name,
                    "namespace": config.environment.value
                },
                "spec": {
                    "replicas": config.replicas,
                    "selector": {
                        "matchLabels": {
                            "app": config.service_name
                        }
                    },
                    "template": {
                        "metadata": {
                            "labels": {
                                "app": config.service_name,
                                "version": config.version
                            }
                        },
                        "spec": {
                            "containers": [{
                                "name": config.service_name,
                                "image": config.image,
                                "env": [
                                    {"name": k, "value": v}
                                    for k, v in config.environment_variables.items()
                                ],
                                "ports": [
                                    {"containerPort": port["containerPort"]}
                                    for port in config.ports
                                ],
                                "resources": config.resources
                            }]
                        }
                    }
                }
            }

            # 应用Deployment
            try:
                apps_v1.patch_namespaced_deployment(
                    name=config.service_name,
                    namespace=config.environment.value,
                    body=deployment_manifest
                )
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    apps_v1.create_namespaced_deployment(
                        namespace=config.environment.value,
                        body=deployment_manifest
                    )
                else:
                    raise

            # 创建Service
            service_manifest = {
                "apiVersion": "v1",
                "kind": "Service",
                "metadata": {
                    "name": config.service_name,
                    "namespace": config.environment.value
                },
                "spec": {
                    "selector": {
                        "app": config.service_name
                    },
                    "ports": [
                        {
                            "port": port["port"],
                            "targetPort": port["containerPort"]
                        }
                        for port in config.ports
                    ]
                }
            }

            try:
                core_v1.patch_namespaced_service(
                    name=config.service_name,
                    namespace=config.environment.value,
                    body=service_manifest
                )
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    core_v1.create_namespaced_service(
                        namespace=config.environment.value,
                        body=service_manifest
                    )
                else:
                    raise

            return True

        except Exception as e:
            logger.error(f"Error deploying to Kubernetes: {e}")
            return False

    @staticmethod
    async def _deploy_to_docker(config: DeploymentConfig) -> bool:
        """部署到Docker"""
        try:
            if not docker_client:
                logger.error("Docker client not available")
                return False

            # 停止现有容器
            try:
                existing_container = docker_client.containers.get(config.service_name)
                existing_container.stop()
                existing_container.remove()
            except docker.errors.NotFound:
                pass

            # 启动新容器
            container = docker_client.containers.run(
                config.image,
                name=config.service_name,
                environment=config.environment_variables,
                ports={
                    f"{port['containerPort']}/tcp": port['port']
                    for port in config.ports
                },
                detach=True,
                restart_policy={"Name": "unless-stopped"}
            )

            logger.info(f"Container {config.service_name} started: {container.id}")
            return True

        except Exception as e:
            logger.error(f"Error deploying to Docker: {e}")
            return False

class ConfigurationManager:
    """配置管理器"""

    @staticmethod
    async def save_configuration(config: ConfigurationItem, created_by: str) -> str:
        """保存配置"""
        try:
            config_id = str(uuid.uuid4())

            # 加密敏感配置
            content = config.content
            if config.is_encrypted:
                content = await ConfigurationManager._encrypt_config(content)

            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO configurations
                    (id, config_name, config_type, environment, content, description,
                     is_encrypted, created_by)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (config_name, environment)
                    DO UPDATE SET
                        content = EXCLUDED.content,
                        description = EXCLUDED.description,
                        is_encrypted = EXCLUDED.is_encrypted,
                        version = configurations.version + 1,
                        updated_at = CURRENT_TIMESTAMP
                    """,
                    config_id,
                    config.name,
                    config.config_type.value,
                    config.environment.value,
                    json.dumps(content),
                    config.description,
                    config.is_encrypted,
                    created_by
                )

            logger.info(f"Configuration saved: {config.name}")
            return config_id

        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _encrypt_config(content: Dict[str, Any]) -> Dict[str, Any]:
        """加密配置内容"""
        # 这里应该实现真正的加密逻辑
        # 为了演示，我们只是标记为加密
        return {"encrypted": True, "data": content}

    @staticmethod
    async def get_configuration(name: str, environment: EnvironmentType) -> Optional[Dict[str, Any]]:
        """获取配置"""
        try:
            async with db_pool.acquire() as conn:
                config = await conn.fetchrow(
                    """
                    SELECT * FROM configurations
                    WHERE config_name = $1 AND environment = $2
                    ORDER BY version DESC LIMIT 1
                    """,
                    name,
                    environment.value
                )

                if config:
                    content = json.loads(config['content'])

                    # 解密配置
                    if config['is_encrypted']:
                        content = await ConfigurationManager._decrypt_config(content)

                    return {
                        "id": config['id'],
                        "name": config['config_name'],
                        "type": config['config_type'],
                        "environment": config['environment'],
                        "content": content,
                        "description": config['description'],
                        "version": config['version'],
                        "created_at": config['created_at'],
                        "updated_at": config['updated_at']
                    }

                return None

        except Exception as e:
            logger.error(f"Error getting configuration: {e}")
            return None

    @staticmethod
    async def _decrypt_config(content: Dict[str, Any]) -> Dict[str, Any]:
        """解密配置内容"""
        # 这里应该实现真正的解密逻辑
        if content.get("encrypted"):
            return content.get("data", {})
        return content

class BackupManager:
    """备份管理器"""

    @staticmethod
    async def create_backup_job(backup_config: BackupConfig) -> str:
        """创建备份任务"""
        try:
            job_id = str(uuid.uuid4())

            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO backup_jobs
                    (id, job_name, backup_type, source_path, destination_path,
                     schedule_cron, retention_days, is_active)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """,
                    job_id,
                    backup_config.name,
                    backup_config.backup_type,
                    backup_config.source,
                    backup_config.destination,
                    backup_config.schedule,
                    backup_config.retention_days,
                    backup_config.is_active
                )

            logger.info(f"Backup job created: {backup_config.name}")
            return job_id

        except Exception as e:
            logger.error(f"Error creating backup job: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def execute_backup(job_id: str) -> bool:
        """执行备份"""
        try:
            # 获取备份任务信息
            async with db_pool.acquire() as conn:
                job = await conn.fetchrow(
                    "SELECT * FROM backup_jobs WHERE id = $1", job_id
                )

                if not job:
                    logger.error(f"Backup job not found: {job_id}")
                    return False

                # 创建备份记录
                record_id = str(uuid.uuid4())
                await conn.execute(
                    """
                    INSERT INTO backup_records
                    (id, job_id, status, started_at)
                    VALUES ($1, $2, $3, $4)
                    """,
                    record_id,
                    job_id,
                    "in_progress",
                    datetime.utcnow()
                )

            # 执行备份
            backup_type = job['backup_type']
            source = job['source_path']
            destination = job['destination_path']

            if backup_type == "database":
                success, backup_file, file_size = await BackupManager._backup_database(source, destination)
            elif backup_type == "files":
                success, backup_file, file_size = await BackupManager._backup_files(source, destination)
            else:
                success, backup_file, file_size = False, None, 0

            # 更新备份记录
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    UPDATE backup_records
                    SET status = $1, completed_at = $2, backup_file = $3, file_size = $4
                    WHERE id = $5
                    """,
                    "success" if success else "failed",
                    datetime.utcnow(),
                    backup_file,
                    file_size,
                    record_id
                )

                # 更新任务最后运行时间
                await conn.execute(
                    "UPDATE backup_jobs SET last_run = $1 WHERE id = $2",
                    datetime.utcnow(),
                    job_id
                )

            return success

        except Exception as e:
            logger.error(f"Error executing backup: {e}")
            return False

    @staticmethod
    async def _backup_database(source: str, destination: str) -> tuple:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{destination}/db_backup_{timestamp}.sql"

            # 执行pg_dump
            cmd = [
                "pg_dump",
                "-h", os.getenv("DB_HOST", "localhost"),
                "-p", os.getenv("DB_PORT", "5432"),
                "-U", os.getenv("DB_USER", "admin"),
                "-d", source,
                "-f", backup_file
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={**os.environ, "PGPASSWORD": os.getenv("DB_PASSWORD", "admin123")}
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                file_size = Path(backup_file).stat().st_size
                return True, backup_file, file_size
            else:
                logger.error(f"Database backup failed: {stderr.decode()}")
                return False, None, 0

        except Exception as e:
            logger.error(f"Error backing up database: {e}")
            return False, None, 0

    @staticmethod
    async def _backup_files(source: str, destination: str) -> tuple:
        """备份文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{destination}/files_backup_{timestamp}.tar.gz"

            # 执行tar压缩
            cmd = ["tar", "-czf", backup_file, "-C", source, "."]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                file_size = Path(backup_file).stat().st_size
                return True, backup_file, file_size
            else:
                logger.error(f"File backup failed: {stderr.decode()}")
                return False, None, 0

        except Exception as e:
            logger.error(f"Error backing up files: {e}")
            return False, None, 0

class LogManager:
    """日志管理器"""

    @staticmethod
    async def query_logs(query: LogQuery) -> List[Dict[str, Any]]:
        """查询日志"""
        try:
            async with db_pool.acquire() as conn:
                sql = "SELECT * FROM system_logs WHERE 1=1"
                params = []
                param_count = 1

                if query.service_name:
                    sql += f" AND service_name = ${param_count}"
                    params.append(query.service_name)
                    param_count += 1

                if query.level:
                    sql += f" AND log_level = ${param_count}"
                    params.append(query.level)
                    param_count += 1

                if query.start_time:
                    sql += f" AND timestamp >= ${param_count}"
                    params.append(query.start_time)
                    param_count += 1

                if query.end_time:
                    sql += f" AND timestamp <= ${param_count}"
                    params.append(query.end_time)
                    param_count += 1

                if query.search_text:
                    sql += f" AND message ILIKE ${param_count}"
                    params.append(f"%{query.search_text}%")
                    param_count += 1

                sql += f" ORDER BY timestamp DESC LIMIT ${param_count}"
                params.append(query.limit)

                logs = await conn.fetch(sql, *params)
                return [dict(log) for log in logs]

        except Exception as e:
            logger.error(f"Error querying logs: {e}")
            return []

    @staticmethod
    async def write_log(service_name: str, level: str, message: str, details: Dict[str, Any] = None):
        """写入日志"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO system_logs (service_name, log_level, message, details)
                    VALUES ($1, $2, $3, $4)
                    """,
                    service_name,
                    level,
                    message,
                    json.dumps(details) if details else None
                )

        except Exception as e:
            logger.error(f"Error writing log: {e}")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "devops-service"}

@app.post("/deployments")
async def deploy_service(config: DeploymentConfig, deployed_by: str = "system"):
    """部署服务"""
    deployment_id = await DeploymentManager.deploy_service(config, deployed_by)
    return {"message": "Deployment started", "deployment_id": deployment_id}

@app.get("/deployments")
async def get_deployments(
    service_name: Optional[str] = None,
    environment: Optional[EnvironmentType] = None,
    status: Optional[DeploymentStatus] = None,
    limit: int = 50
):
    """获取部署记录"""
    async with db_pool.acquire() as conn:
        query = "SELECT * FROM deployments WHERE 1=1"
        params = []
        param_count = 1

        if service_name:
            query += f" AND service_name = ${param_count}"
            params.append(service_name)
            param_count += 1

        if environment:
            query += f" AND environment = ${param_count}"
            params.append(environment.value)
            param_count += 1

        if status:
            query += f" AND status = ${param_count}"
            params.append(status.value)
            param_count += 1

        query += f" ORDER BY created_at DESC LIMIT ${param_count}"
        params.append(limit)

        deployments = await conn.fetch(query, *params)
        return [dict(deployment) for deployment in deployments]

@app.post("/configurations")
async def save_configuration(config: ConfigurationItem, created_by: str = "system"):
    """保存配置"""
    config_id = await ConfigurationManager.save_configuration(config, created_by)
    return {"message": "Configuration saved", "config_id": config_id}

@app.get("/configurations/{name}")
async def get_configuration(name: str, environment: EnvironmentType):
    """获取配置"""
    config = await ConfigurationManager.get_configuration(name, environment)
    if config:
        return config
    else:
        raise HTTPException(status_code=404, detail="Configuration not found")

@app.post("/backups/jobs")
async def create_backup_job(backup_config: BackupConfig):
    """创建备份任务"""
    job_id = await BackupManager.create_backup_job(backup_config)
    return {"message": "Backup job created", "job_id": job_id}

@app.post("/backups/jobs/{job_id}/execute")
async def execute_backup(job_id: str):
    """执行备份"""
    success = await BackupManager.execute_backup(job_id)
    if success:
        return {"message": "Backup completed successfully"}
    else:
        raise HTTPException(status_code=500, detail="Backup failed")

@app.get("/backups/records")
async def get_backup_records(job_id: Optional[str] = None, limit: int = 50):
    """获取备份记录"""
    async with db_pool.acquire() as conn:
        if job_id:
            records = await conn.fetch(
                """
                SELECT br.*, bj.job_name
                FROM backup_records br
                JOIN backup_jobs bj ON br.job_id = bj.id
                WHERE br.job_id = $1
                ORDER BY br.created_at DESC LIMIT $2
                """,
                job_id, limit
            )
        else:
            records = await conn.fetch(
                """
                SELECT br.*, bj.job_name
                FROM backup_records br
                JOIN backup_jobs bj ON br.job_id = bj.id
                ORDER BY br.created_at DESC LIMIT $1
                """,
                limit
            )

        return [dict(record) for record in records]

@app.post("/logs/query")
async def query_logs(query: LogQuery):
    """查询日志"""
    logs = await LogManager.query_logs(query)
    return {"logs": logs, "count": len(logs)}

@app.post("/logs")
async def write_log(
    service_name: str,
    level: str,
    message: str,
    details: Optional[Dict[str, Any]] = None
):
    """写入日志"""
    await LogManager.write_log(service_name, level, message, details)
    return {"message": "Log written"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8012)