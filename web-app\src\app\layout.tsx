'use client';

import React from 'react';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import './globals.css';

// 配置dayjs
dayjs.locale('zh-cn');
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

// 创建QueryClient实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5分钟
      gcTime: 10 * 60 * 1000, // 10分钟 (新版本使用gcTime替代cacheTime)
    },
    mutations: {
      retry: 1,
    },
  },
});

// Ant Design工业风格主题配置
const antdTheme = {
  token: {
    colorPrimary: '#4a90e2',        // 钢青蓝
    colorSuccess: '#00d4aa',        // 电光青绿
    colorWarning: '#ff8c42',        // 琥珀橙
    colorError: '#ff4757',          // 错误红
    colorInfo: '#4a90e2',           // 信息蓝
    colorBgBase: '#0f0f0f',         // 主背景
    colorBgContainer: '#1a1a1a',    // 容器背景
    colorBgElevated: '#242424',     // 卡片背景
    colorBorder: '#333333',         // 边框色
    colorText: '#ffffff',           // 主文字
    colorTextSecondary: '#b0b0b0',  // 次文字
    borderRadius: 8,
    wireframe: false,
  },
  components: {
    Layout: {
      headerBg: '#1a1a1a',
      siderBg: '#1a1a1a',
      bodyBg: '#0f0f0f',
    },
    Menu: {
      darkItemBg: 'transparent',
      darkSubMenuItemBg: '#242424',
      darkItemSelectedBg: 'rgba(74, 144, 226, 0.1)',
      darkItemColor: '#b0b0b0',
      darkItemSelectedColor: '#4a90e2',
      darkItemHoverColor: '#4a90e2',
    },
    Card: {
      headerBg: '#242424',
      colorBgContainer: '#242424',
    },
    Table: {
      headerBg: '#1a1a1a',
      colorBgContainer: '#242424',
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        <title>工业智能体平台</title>
        <meta name="description" content="基于大语言模型的工业智能体平台" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
        <QueryClientProvider client={queryClient}>
          <ConfigProvider locale={zhCN} theme={antdTheme}>
            <AntdApp>
              {children}
            </AntdApp>
          </ConfigProvider>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </body>
    </html>
  );
}
