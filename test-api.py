#!/usr/bin/env python3
"""
测试工业智能体平台API
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8888"

def test_api():
    """测试API功能"""
    print("🧪 测试工业智能体平台API...")
    
    # 1. 测试根端点
    print("\n1. 测试根端点...")
    response = requests.get(f"{BASE_URL}/")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    
    # 2. 测试仪表板
    print("\n2. 测试仪表板数据...")
    response = requests.get(f"{BASE_URL}/dashboard")
    data = response.json()
    print(f"状态码: {response.status_code}")
    print(f"生产线总数: {data['overview']['total_production_lines']}")
    print(f"运行中的生产线: {data['overview']['active_lines']}")
    print(f"平均效率: {data['overview']['average_efficiency']}%")
    print(f"库存预警: {len(data['inventory_alerts'])} 项")
    
    # 3. 测试生产线状态
    print("\n3. 测试生产线状态...")
    response = requests.get(f"{BASE_URL}/production/lines")
    data = response.json()
    print(f"状态码: {response.status_code}")
    for line in data['production_lines']:
        print(f"  - {line['name']}: {line['status']} (效率: {line['efficiency']}%)")
    
    # 4. 测试设备状态
    print("\n4. 测试设备状态...")
    response = requests.get(f"{BASE_URL}/equipment")
    data = response.json()
    print(f"状态码: {response.status_code}")
    for eq in data['equipment']:
        print(f"  - {eq['name']}: {eq['status']} (利用率: {eq['utilization']}%)")
    
    # 5. 测试质量指标
    print("\n5. 测试质量指标...")
    response = requests.get(f"{BASE_URL}/quality/metrics")
    data = response.json()
    print(f"状态码: {response.status_code}")
    metrics = data['current_metrics']
    print(f"缺陷率: {metrics['defect_rate']:.3f}")
    print(f"一次通过率: {metrics['first_pass_yield']:.3f}")
    print(f"质量评分: {metrics['quality_score']:.1f}")
    
    # 6. 测试库存状态
    print("\n6. 测试库存状态...")
    response = requests.get(f"{BASE_URL}/inventory")
    data = response.json()
    print(f"状态码: {response.status_code}")
    print(f"库存预警数量: {len(data['alerts'])}")
    for alert in data['alerts']:
        print(f"  - {alert['material']}: {alert['message']}")
    
    # 7. 测试AI聊天
    print("\n7. 测试AI聊天...")
    queries = ["生产效率", "质量问题", "设备状态", "库存管理"]
    for query in queries:
        response = requests.get(f"{BASE_URL}/ai/chat", params={"query": query})
        data = response.json()
        print(f"问题: {query}")
        print(f"回答: {data['response']}")
        print()
    
    # 8. 测试生产计划创建
    print("\n8. 测试生产计划创建...")
    plan_data = {
        "product_code": "PROD001",
        "quantity": 100,
        "priority": "high",
        "deadline": (datetime.now() + timedelta(days=7)).isoformat()
    }
    response = requests.post(f"{BASE_URL}/production/plan", json=plan_data)
    data = response.json()
    print(f"状态码: {response.status_code}")
    print(f"计划ID: {data['plan_id']}")
    print(f"分配生产线: {data['assigned_line']}")
    print(f"消息: {data['message']}")
    
    # 9. 测试维护任务安排
    print("\n9. 测试维护任务安排...")
    maintenance_data = {
        "equipment_id": "eq_001",
        "task_type": "preventive",
        "description": "定期保养",
        "scheduled_date": (datetime.now() + timedelta(days=3)).isoformat(),
        "estimated_duration": 4
    }
    response = requests.post(f"{BASE_URL}/maintenance/schedule", json=maintenance_data)
    data = response.json()
    print(f"状态码: {response.status_code}")
    print(f"任务ID: {data['task_id']}")
    print(f"设备名称: {data['equipment_name']}")
    print(f"维护紧急度: {data['urgency']}")
    print(f"预估成本: ¥{data['estimated_cost']}")
    
    # 10. 测试生产分析
    print("\n10. 测试生产分析...")
    response = requests.get(f"{BASE_URL}/analytics/production")
    data = response.json()
    print(f"状态码: {response.status_code}")
    print("效率趋势:")
    for trend in data['efficiency_trend']:
        print(f"  {trend['date']}: {trend['efficiency']}%")
    print("瓶颈分析:")
    for bottleneck in data['bottlenecks']:
        print(f"  - {bottleneck['process']}: {bottleneck['impact']} ({bottleneck['suggestion']})")
    
    print("\n✅ API测试完成！")

if __name__ == "__main__":
    try:
        test_api()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务正在运行在 http://localhost:8888")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
