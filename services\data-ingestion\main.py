"""
工业智能体平台 - 数据接入服务
负责从各种数据源（MES、ERP、SCADA、PLC等）接入数据
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import httpx
import struct
from pymodbus.client.asynchronous.tcp import AsyncModbusTCPClient
from pymodbus.exceptions import ModbusException
import xml.etree.ElementTree as ET

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接池
db_pool = None
timescale_pool = None
redis_client = None
kafka_producer = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, timescale_pool, redis_client, kafka_producer
    
    # 启动时初始化连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    timescale_pool = await asyncpg.create_pool(
        host=os.getenv("TIMESCALE_HOST", "localhost"),
        port=int(os.getenv("TIMESCALE_PORT", "5433")),
        user=os.getenv("TIMESCALE_USER", "admin"),
        password=os.getenv("TIMESCALE_PASSWORD", "admin123"),
        database=os.getenv("TIMESCALE_DB", "timeseries_data"),
        min_size=5,
        max_size=20
    )
    
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x).encode('utf-8')
    )
    await kafka_producer.start()
    
    logger.info("Data ingestion service initialized")
    
    yield
    
    # 关闭时清理资源
    if db_pool:
        await db_pool.close()
    if timescale_pool:
        await timescale_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Data ingestion service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="数据接入服务",
    description="从各种工业数据源接入数据",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class DataSource(BaseModel):
    id: str
    name: str
    type: str  # mes, erp, scada, plc, api, file
    connection_config: Dict[str, Any]
    data_mapping: Dict[str, Any]
    polling_interval: int = 60  # 秒
    is_active: bool = True

class SensorData(BaseModel):
    sensor_id: str
    timestamp: datetime
    value: Union[float, int, str, bool]
    unit: Optional[str] = None
    quality: Optional[str] = "good"  # good, bad, uncertain

class ProductionData(BaseModel):
    order_id: str
    product_id: str
    operation: str
    timestamp: datetime
    quantity: float
    status: str
    operator_id: Optional[str] = None
    equipment_id: Optional[str] = None

class QualityData(BaseModel):
    inspection_id: str
    product_id: str
    timestamp: datetime
    measurements: Dict[str, float]
    result: str  # pass, fail, rework
    defects: List[str] = []

class DataConnector:
    """数据连接器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def connect(self):
        """建立连接"""
        raise NotImplementedError
    
    async def disconnect(self):
        """断开连接"""
        raise NotImplementedError
    
    async def fetch_data(self) -> List[Dict[str, Any]]:
        """获取数据"""
        raise NotImplementedError

class MESConnector(DataConnector):
    """MES系统连接器"""
    
    async def connect(self):
        self.client = httpx.AsyncClient(
            base_url=self.config["base_url"],
            headers={"Authorization": f"Bearer {self.config['api_key']}"}
        )
    
    async def disconnect(self):
        if hasattr(self, 'client'):
            await self.client.aclose()
    
    async def fetch_data(self) -> List[Dict[str, Any]]:
        try:
            response = await self.client.get("/api/production/current")
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"MES API error: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"MES fetch error: {e}")
            return []

class ERPConnector(DataConnector):
    """ERP系统连接器"""
    
    async def connect(self):
        self.client = httpx.AsyncClient(
            base_url=self.config["base_url"],
            auth=(self.config["username"], self.config["password"])
        )
    
    async def disconnect(self):
        if hasattr(self, 'client'):
            await self.client.aclose()
    
    async def fetch_data(self) -> List[Dict[str, Any]]:
        try:
            # 获取订单数据
            orders_response = await self.client.get("/api/orders")
            inventory_response = await self.client.get("/api/inventory")
            
            data = []
            if orders_response.status_code == 200:
                data.extend(orders_response.json())
            if inventory_response.status_code == 200:
                data.extend(inventory_response.json())
            
            return data
        except Exception as e:
            logger.error(f"ERP fetch error: {e}")
            return []

class PLCConnector(DataConnector):
    """PLC连接器（Modbus TCP）"""
    
    async def connect(self):
        self.client = AsyncModbusTCPClient(
            host=self.config["host"],
            port=self.config.get("port", 502)
        )
        await self.client.connect()
    
    async def disconnect(self):
        if hasattr(self, 'client'):
            await self.client.close()
    
    async def fetch_data(self) -> List[Dict[str, Any]]:
        try:
            data = []
            
            # 读取保持寄存器
            for register_config in self.config.get("registers", []):
                result = await self.client.read_holding_registers(
                    register_config["address"],
                    register_config["count"],
                    unit=self.config.get("unit_id", 1)
                )
                
                if not result.isError():
                    for i, value in enumerate(result.registers):
                        data.append({
                            "sensor_id": f"{register_config['name']}_{i}",
                            "timestamp": datetime.utcnow(),
                            "value": value,
                            "unit": register_config.get("unit"),
                            "data_type": "plc_register"
                        })
            
            return data
        except Exception as e:
            logger.error(f"PLC fetch error: {e}")
            return []

class SCADAConnector(DataConnector):
    """SCADA系统连接器"""
    
    async def connect(self):
        # 假设SCADA通过OPC UA或HTTP API提供数据
        self.client = httpx.AsyncClient(
            base_url=self.config["base_url"],
            headers={"X-API-Key": self.config["api_key"]}
        )
    
    async def disconnect(self):
        if hasattr(self, 'client'):
            await self.client.aclose()
    
    async def fetch_data(self) -> List[Dict[str, Any]]:
        try:
            response = await self.client.get("/api/realtime-data")
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"SCADA API error: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"SCADA fetch error: {e}")
            return []

# 连接器工厂
CONNECTOR_TYPES = {
    "mes": MESConnector,
    "erp": ERPConnector,
    "plc": PLCConnector,
    "scada": SCADAConnector
}

class DataIngestionManager:
    """数据接入管理器"""
    
    def __init__(self):
        self.active_connectors = {}
        self.polling_tasks = {}
    
    async def add_data_source(self, data_source: DataSource):
        """添加数据源"""
        if data_source.type not in CONNECTOR_TYPES:
            raise ValueError(f"Unsupported data source type: {data_source.type}")
        
        # 创建连接器
        connector_class = CONNECTOR_TYPES[data_source.type]
        connector = connector_class(data_source.connection_config)
        
        try:
            await connector.connect()
            self.active_connectors[data_source.id] = {
                "connector": connector,
                "config": data_source
            }
            
            # 启动轮询任务
            if data_source.is_active:
                await self.start_polling(data_source.id)
            
            logger.info(f"Data source {data_source.id} added successfully")
            
        except Exception as e:
            logger.error(f"Failed to add data source {data_source.id}: {e}")
            raise
    
    async def remove_data_source(self, source_id: str):
        """移除数据源"""
        if source_id in self.active_connectors:
            # 停止轮询
            await self.stop_polling(source_id)
            
            # 断开连接
            connector_info = self.active_connectors[source_id]
            await connector_info["connector"].disconnect()
            
            del self.active_connectors[source_id]
            logger.info(f"Data source {source_id} removed")
    
    async def start_polling(self, source_id: str):
        """开始轮询数据"""
        if source_id not in self.active_connectors:
            return
        
        connector_info = self.active_connectors[source_id]
        config = connector_info["config"]
        
        async def polling_task():
            while source_id in self.active_connectors:
                try:
                    # 获取数据
                    data = await connector_info["connector"].fetch_data()
                    
                    if data:
                        # 处理数据
                        await self.process_data(source_id, data)
                    
                    # 等待下次轮询
                    await asyncio.sleep(config.polling_interval)
                    
                except Exception as e:
                    logger.error(f"Polling error for {source_id}: {e}")
                    await asyncio.sleep(config.polling_interval)
        
        # 启动轮询任务
        task = asyncio.create_task(polling_task())
        self.polling_tasks[source_id] = task
        logger.info(f"Started polling for {source_id}")
    
    async def stop_polling(self, source_id: str):
        """停止轮询"""
        if source_id in self.polling_tasks:
            self.polling_tasks[source_id].cancel()
            del self.polling_tasks[source_id]
            logger.info(f"Stopped polling for {source_id}")
    
    async def process_data(self, source_id: str, data: List[Dict[str, Any]]):
        """处理接入的数据"""
        try:
            # 数据预处理
            processed_data = await self.preprocess_data(source_id, data)
            
            # 存储到时序数据库
            await self.store_timeseries_data(processed_data)
            
            # 发送到Kafka
            await self.send_to_kafka(source_id, processed_data)
            
            # 缓存最新数据
            await self.cache_latest_data(source_id, processed_data)
            
            logger.info(f"Processed {len(processed_data)} records from {source_id}")
            
        except Exception as e:
            logger.error(f"Data processing error for {source_id}: {e}")
    
    async def preprocess_data(self, source_id: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据预处理"""
        processed = []
        
        for record in data:
            # 添加元数据
            record["source_id"] = source_id
            record["ingestion_time"] = datetime.utcnow()
            
            # 数据验证和清洗
            if self.validate_record(record):
                processed.append(record)
        
        return processed
    
    def validate_record(self, record: Dict[str, Any]) -> bool:
        """验证数据记录"""
        # 基本验证逻辑
        required_fields = ["timestamp"]
        return all(field in record for field in required_fields)
    
    async def store_timeseries_data(self, data: List[Dict[str, Any]]):
        """存储时序数据"""
        if not data:
            return
        
        async with timescale_pool.acquire() as conn:
            # 创建时序数据表（如果不存在）
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS sensor_data (
                    time TIMESTAMPTZ NOT NULL,
                    source_id TEXT NOT NULL,
                    sensor_id TEXT NOT NULL,
                    value DOUBLE PRECISION,
                    unit TEXT,
                    quality TEXT,
                    metadata JSONB
                );
                
                SELECT create_hypertable('sensor_data', 'time', if_not_exists => TRUE);
            """)
            
            # 批量插入数据
            values = []
            for record in data:
                if record.get("data_type") in ["sensor", "plc_register"]:
                    values.append((
                        record["timestamp"],
                        record["source_id"],
                        record.get("sensor_id", "unknown"),
                        record.get("value"),
                        record.get("unit"),
                        record.get("quality", "good"),
                        json.dumps(record)
                    ))
            
            if values:
                await conn.executemany(
                    "INSERT INTO sensor_data (time, source_id, sensor_id, value, unit, quality, metadata) VALUES ($1, $2, $3, $4, $5, $6, $7)",
                    values
                )
    
    async def send_to_kafka(self, source_id: str, data: List[Dict[str, Any]]):
        """发送数据到Kafka"""
        for record in data:
            topic = f"data-ingestion-{source_id}"
            await kafka_producer.send(topic, record)
    
    async def cache_latest_data(self, source_id: str, data: List[Dict[str, Any]]):
        """缓存最新数据"""
        if data:
            latest_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "count": len(data),
                "sample": data[-1] if data else None
            }
            await redis_client.setex(
                f"latest_data:{source_id}",
                300,  # 5分钟过期
                json.dumps(latest_data, default=str)
            )

# 全局数据接入管理器
ingestion_manager = DataIngestionManager()

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "data-ingestion"}

@app.post("/data-sources")
async def create_data_source(data_source: DataSource):
    """创建数据源"""
    try:
        await ingestion_manager.add_data_source(data_source)
        
        # 保存到数据库
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO data_sources (id, name, type, connection_config, data_mapping, polling_interval, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    connection_config = EXCLUDED.connection_config,
                    data_mapping = EXCLUDED.data_mapping,
                    polling_interval = EXCLUDED.polling_interval,
                    is_active = EXCLUDED.is_active,
                    updated_at = CURRENT_TIMESTAMP
                """,
                data_source.id,
                data_source.name,
                data_source.type,
                json.dumps(data_source.connection_config),
                json.dumps(data_source.data_mapping),
                data_source.polling_interval,
                data_source.is_active
            )
        
        return {"message": "Data source created successfully", "id": data_source.id}
        
    except Exception as e:
        logger.error(f"Failed to create data source: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-sources")
async def list_data_sources():
    """获取数据源列表"""
    async with db_pool.acquire() as conn:
        rows = await conn.fetch("SELECT * FROM data_sources ORDER BY created_at DESC")
        return [dict(row) for row in rows]

@app.delete("/data-sources/{source_id}")
async def delete_data_source(source_id: str):
    """删除数据源"""
    try:
        await ingestion_manager.remove_data_source(source_id)
        
        async with db_pool.acquire() as conn:
            await conn.execute("DELETE FROM data_sources WHERE id = $1", source_id)
        
        return {"message": "Data source deleted successfully"}
        
    except Exception as e:
        logger.error(f"Failed to delete data source: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-sources/{source_id}/status")
async def get_data_source_status(source_id: str):
    """获取数据源状态"""
    # 从Redis获取最新数据
    latest_data = await redis_client.get(f"latest_data:{source_id}")
    
    status = {
        "source_id": source_id,
        "is_connected": source_id in ingestion_manager.active_connectors,
        "is_polling": source_id in ingestion_manager.polling_tasks,
        "latest_data": json.loads(latest_data) if latest_data else None
    }
    
    return status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8009)
