{"name": "rc-table", "version": "7.51.1", "description": "table ui component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-table", "table", "component", "ui"], "files": ["assets/*.css", "es", "lib"], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "homepage": "http://github.com/react-component/table", "maintainers": ["<EMAIL>", "<EMAIL>"], "repository": {"type": "git", "url": "**************:react-component/table.git"}, "bugs": {"url": "http://github.com/react-component/table/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "vitest --watch false", "coverage": "vitest run --coverage", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish --any-branch", "lint": "eslint src/ --ext .tsx,.ts", "tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build", "prepare": "husky install"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/context": "^1.4.0", "classnames": "^2.2.5", "rc-resize-observer": "^1.1.0", "rc-util": "^5.44.3", "rc-virtual-list": "^3.14.2"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^12.1.5", "@types/enzyme": "^3.10.5", "@types/jest": "^29.5.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.5", "@types/responselike": "^1.0.0", "@types/styled-components": "^5.1.32", "@umijs/fabric": "^4.0.1", "@vitest/coverage-v8": "^2.0.5", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.0", "dumi": "^2.1.3", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-unicorn": "^56.0.0", "father": "^4.0.0", "gh-pages": "^6.1.0", "glob": "^7.1.6", "husky": "^9.0.11", "immutability-helper": "^3.0.0", "jsdom": "^25.0.0", "less": "^4.1.3", "lint-staged": "^15.1.0", "np": "^10.0.1", "prettier": "^3.1.0", "rc-animate": "^3.0.0", "rc-dropdown": "~4.0.1", "rc-menu": "~9.13.0", "rc-tooltip": "^6.2.0", "react": "^16.0.0", "react-dnd": "^2.5.4", "react-dnd-html5-backend": "^2.5.4", "react-dom": "^16.0.0", "react-resizable": "^3.0.5", "react-virtualized": "^9.12.0", "react-window": "^1.8.5", "regenerator-runtime": "^0.14.0", "styled-components": "^6.1.1", "typescript": "~5.7.2", "vitest": "^2.0.5"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write"]}}