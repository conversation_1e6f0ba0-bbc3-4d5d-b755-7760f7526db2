function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UngroupOutlinedSvg from "@ant-design/icons-svg/es/asn/UngroupOutlined";
import AntdIcon from "../components/AntdIcon";
const UngroupOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: UngroupOutlinedSvg
}));

/**![ungroup](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik03MzYgNTUwSDI4OGMtOC44IDAtMTYgNy4yLTE2IDE2djE3NmMwIDguOCA3LjIgMTYgMTYgMTZoNDQ4YzguOCAwIDE2LTcuMiAxNi0xNlY1NjZjMC04LjgtNy4yLTE2LTE2LTE2em0tNTYgMTM2SDM0NHYtNjRoMzM2djY0em0yMDggMTMwYy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6TTczNiAyNjZIMjg4Yy04LjggMC0xNiA3LjItMTYgMTZ2MTc2YzAgOC44IDcuMiAxNiAxNiAxNmg0NDhjOC44IDAgMTYtNy4yIDE2LTE2VjI4MmMwLTguOC03LjItMTYtMTYtMTZ6bS01NiAxMzZIMzQ0di02NGgzMzZ2NjR6bTIwOC0xOTRjMzkuOCAwIDcyLTMyLjIgNzItNzJzLTMyLjItNzItNzItNzItNzIgMzIuMi03MiA3MiAzMi4yIDcyIDcyIDcyem0wLTk2YzEzLjMgMCAyNCAxMC43IDI0IDI0cy0xMC43IDI0LTI0IDI0LTI0LTEwLjctMjQtMjQgMTAuNy0yNCAyNC0yNHpNMTM2IDY0Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6bTAgNjU2Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(UngroupOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UngroupOutlined';
}
export default RefIcon;