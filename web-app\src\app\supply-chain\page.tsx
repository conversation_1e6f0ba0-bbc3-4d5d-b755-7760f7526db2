'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, message, Alert, Tabs } from 'antd';
import {
  ShoppingCartOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  TruckOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  ShopOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Line, Column, Pie } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useInventoryStatus } from '@/hooks/useApi';
import dayjs from 'dayjs';
import CountUp from 'react-countup';

const { Option } = Select;
const { TabPane } = Tabs;

// 样式组件
const SupplyChainContainer = styled.div`
  .supply-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .stock-status {
    &.normal {
      color: var(--status-success);
      background: rgba(0, 212, 170, 0.1);
      border: 1px solid rgba(0, 212, 170, 0.3);
    }
    
    &.low {
      color: var(--status-warning);
      background: rgba(255, 140, 66, 0.1);
      border: 1px solid rgba(255, 140, 66, 0.3);
    }
    
    &.critical {
      color: var(--status-error);
      background: rgba(255, 71, 87, 0.1);
      border: 1px solid rgba(255, 71, 87, 0.3);
    }
    
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const SupplyChainPage: React.FC = () => {
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  // 获取库存数据
  const { data: inventoryData, loading: inventoryLoading, refetch: refetchInventory } = useInventoryStatus();

  // 模拟库存数据
  const mockInventory = [
    { 
      id: 'mat_001',
      material: '钢板',
      category: '原材料',
      current_stock: 1250,
      minimum_stock: 100,
      maximum_stock: 2000,
      unit: 'kg',
      unit_price: 8.5,
      supplier: '钢铁集团A',
      last_purchase: '2024-12-20',
      status: 'normal'
    },
    { 
      id: 'mat_002',
      material: '螺栓',
      category: '标准件',
      current_stock: 45,
      minimum_stock: 200,
      maximum_stock: 1000,
      unit: '个',
      unit_price: 0.8,
      supplier: '标准件公司B',
      last_purchase: '2024-12-15',
      status: 'critical'
    },
    { 
      id: 'mat_003',
      material: '轴承',
      category: '零部件',
      current_stock: 180,
      minimum_stock: 20,
      maximum_stock: 500,
      unit: '个',
      unit_price: 45.0,
      supplier: '轴承制造C',
      last_purchase: '2024-12-25',
      status: 'normal'
    },
    { 
      id: 'mat_004',
      material: '电机',
      category: '零部件',
      current_stock: 15,
      minimum_stock: 10,
      maximum_stock: 100,
      unit: '个',
      unit_price: 280.0,
      supplier: '电机厂D',
      last_purchase: '2024-12-22',
      status: 'low'
    },
    { 
      id: 'mat_005',
      material: '密封圈',
      category: '辅料',
      current_stock: 180,
      minimum_stock: 150,
      maximum_stock: 800,
      unit: '个',
      unit_price: 2.5,
      supplier: '橡胶制品E',
      last_purchase: '2024-12-18',
      status: 'low'
    }
  ];

  // 供应商数据
  const mockSuppliers = [
    {
      id: 'sup_001',
      name: '钢铁集团A',
      category: '原材料',
      rating: 4.8,
      delivery_rate: 98.5,
      quality_score: 96.2,
      payment_terms: '30天',
      contact: '张经理',
      phone: '138-0000-0001'
    },
    {
      id: 'sup_002',
      name: '标准件公司B',
      category: '标准件',
      rating: 4.2,
      delivery_rate: 92.3,
      quality_score: 94.1,
      payment_terms: '15天',
      contact: '李经理',
      phone: '138-0000-0002'
    },
    {
      id: 'sup_003',
      name: '轴承制造C',
      category: '零部件',
      rating: 4.6,
      delivery_rate: 95.8,
      quality_score: 97.5,
      payment_terms: '45天',
      contact: '王经理',
      phone: '138-0000-0003'
    }
  ];

  // 采购订单数据
  const mockPurchaseOrders = [
    {
      id: 'po_001',
      material: '螺栓',
      quantity: 500,
      unit_price: 0.8,
      total_amount: 400,
      supplier: '标准件公司B',
      order_date: '2024-12-28',
      expected_delivery: '2025-01-05',
      status: 'pending'
    },
    {
      id: 'po_002',
      material: '钢板',
      quantity: 800,
      unit_price: 8.5,
      total_amount: 6800,
      supplier: '钢铁集团A',
      order_date: '2024-12-27',
      expected_delivery: '2025-01-03',
      status: 'confirmed'
    },
    {
      id: 'po_003',
      material: '电机',
      quantity: 20,
      unit_price: 280,
      total_amount: 5600,
      supplier: '电机厂D',
      order_date: '2024-12-26',
      expected_delivery: '2025-01-08',
      status: 'shipped'
    }
  ];

  // 库存表格列
  const inventoryColumns = [
    {
      title: '物料信息',
      key: 'material',
      render: (_, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{record.material}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            {record.category} | {record.id}
          </div>
        </div>
      )
    },
    {
      title: '库存状态',
      key: 'stock_status',
      render: (_, record: any) => {
        const getStockStatus = (current: number, min: number, max: number) => {
          if (current <= min) return { status: 'critical', text: '严重不足', icon: <ExclamationCircleOutlined /> };
          if (current <= min * 1.5) return { status: 'low', text: '库存偏低', icon: <WarningOutlined /> };
          return { status: 'normal', text: '库存正常', icon: <CheckCircleOutlined /> };
        };
        
        const statusInfo = getStockStatus(record.current_stock, record.minimum_stock, record.maximum_stock);
        
        return (
          <div>
            <span className={`stock-status ${statusInfo.status}`}>
              {statusInfo.icon}
              {statusInfo.text}
            </span>
            <div style={{ marginTop: '4px', fontSize: '12px', color: 'var(--text-muted)' }}>
              当前: {record.current_stock} {record.unit}
            </div>
          </div>
        );
      }
    },
    {
      title: '库存量',
      key: 'stock_level',
      render: (_, record: any) => {
        const percentage = (record.current_stock / record.maximum_stock) * 100;
        return (
          <div>
            <Progress
              percent={percentage}
              size="small"
              strokeColor={{
                '0%': '#ff4757',
                '30%': '#ff8c42',
                '60%': '#4a90e2',
                '100%': '#00d4aa'
              }}
              style={{ marginBottom: '4px' }}
            />
            <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
              最小: {record.minimum_stock} | 最大: {record.maximum_stock}
            </div>
          </div>
        );
      }
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (price: number, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>
            ¥{price.toFixed(2)}/{record.unit}
          </div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            库存价值: ¥{(price * record.current_stock).toLocaleString()}
          </div>
        </div>
      )
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier'
    },
    {
      title: '最后采购',
      dataIndex: 'last_purchase',
      key: 'last_purchase',
      render: (date: string) => dayjs(date).format('MM-DD')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button 
            size="small" 
            type="primary"
            icon={<ShoppingCartOutlined />}
            onClick={() => {
              form.setFieldsValue({
                material: record.material,
                supplier: record.supplier,
                unit_price: record.unit_price
              });
              setPurchaseModalVisible(true);
            }}
          >
            采购
          </Button>
        </Space>
      )
    }
  ];

  // 供应商表格列
  const supplierColumns = [
    {
      title: '供应商名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{record.category}</div>
        </div>
      )
    },
    {
      title: '评级',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating: number) => (
        <div style={{ color: 'var(--color-accent-green)', fontWeight: 600 }}>
          {rating.toFixed(1)} ⭐
        </div>
      )
    },
    {
      title: '交付率',
      dataIndex: 'delivery_rate',
      key: 'delivery_rate',
      render: (rate: number) => (
        <div>
          <Progress percent={rate} size="small" strokeColor="var(--status-success)" />
          <span style={{ fontSize: '12px' }}>{rate.toFixed(1)}%</span>
        </div>
      )
    },
    {
      title: '质量评分',
      dataIndex: 'quality_score',
      key: 'quality_score',
      render: (score: number) => (
        <span style={{ color: 'var(--text-primary)', fontWeight: 500 }}>
          {score.toFixed(1)}分
        </span>
      )
    },
    {
      title: '付款条件',
      dataIndex: 'payment_terms',
      key: 'payment_terms'
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)' }}>{record.contact}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{record.phone}</div>
        </div>
      )
    }
  ];

  // 采购订单表格列
  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'id',
      key: 'id',
      render: (id: string) => (
        <span style={{ color: 'var(--color-accent-blue)', fontWeight: 500 }}>{id}</span>
      )
    },
    {
      title: '物料',
      dataIndex: 'material',
      key: 'material'
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity'
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: number) => (
        <span style={{ color: 'var(--text-primary)', fontWeight: 600 }}>
          ¥{amount.toLocaleString()}
        </span>
      )
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier'
    },
    {
      title: '预计交付',
      dataIndex: 'expected_delivery',
      key: 'expected_delivery',
      render: (date: string) => dayjs(date).format('MM-DD')
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { text: '待确认', color: 'orange' },
          confirmed: { text: '已确认', color: 'blue' },
          shipped: { text: '已发货', color: 'green' },
          delivered: { text: '已交付', color: 'green' },
          cancelled: { text: '已取消', color: 'red' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  // 创建采购订单
  const handleCreatePurchaseOrder = async (values: any) => {
    try {
      // 这里调用API创建采购订单
      message.success('采购订单创建成功！');
      setPurchaseModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建采购订单失败');
    }
  };

  // 库存分类统计
  const categoryData = [
    { category: '原材料', value: 35, count: 1 },
    { category: '零部件', value: 45, count: 2 },
    { category: '标准件', value: 15, count: 1 },
    { category: '辅料', value: 5, count: 1 }
  ];

  return (
    <MainLayout>
      <SupplyChainContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>供应链管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setPurchaseModalVisible(true)}
              >
                新建采购
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refetchInventory}
                loading={inventoryLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 库存预警 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            <Col span={24}>
              <Alert
                message="库存预警"
                description="螺栓库存严重不足(45/200)，密封圈库存偏低(180/150)，建议立即安排采购。"
                type="error"
                icon={<WarningOutlined />}
                showIcon
                closable
              />
            </Col>
          </Row>

          {/* 概览统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="supply-card">
                  <Statistic
                    title="库存物料种类"
                    value={mockInventory.length}
                    prefix={<ShoppingCartOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="supply-card">
                  <Statistic
                    title="库存预警"
                    value={mockInventory.filter(item => item.status !== 'normal').length}
                    prefix={<WarningOutlined style={{ color: 'var(--status-warning)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="supply-card">
                  <Statistic
                    title="库存总价值"
                    value={mockInventory.reduce((acc, item) => acc + (item.current_stock * item.unit_price), 0)}
                    precision={0}
                    prefix={<DollarOutlined style={{ color: 'var(--color-accent-green)' }} />}
                    formatter={(value) => `¥${(value as number).toLocaleString()}`}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="supply-card">
                  <Statistic
                    title="活跃供应商"
                    value={mockSuppliers.length}
                    prefix={<ShopOutlined style={{ color: 'var(--status-success)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="supply-card" title="库存分类分布" style={{ height: '400px' }}>
                  <Pie
                    data={categoryData}
                    angleField="value"
                    colorField="category"
                    radius={0.8}
                    innerRadius={0.4}
                    label={{
                      type: 'outer',
                      content: '{name}: {percentage}%'
                    }}
                    color={['#4a90e2', '#00d4aa', '#ff8c42', '#ff4757']}
                  />
                </Card>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="supply-card" title="库存水平对比" style={{ height: '400px' }}>
                  <Column
                    data={mockInventory.map(item => ({
                      material: item.material,
                      current: item.current_stock,
                      minimum: item.minimum_stock,
                      maximum: item.maximum_stock
                    }))}
                    xField="material"
                    yField="current"
                    color="var(--color-accent-blue)"
                    columnStyle={{
                      radius: [4, 4, 0, 0]
                    }}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 标签页内容 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Card className="supply-card">
              <Tabs defaultActiveKey="inventory">
                <TabPane tab="库存管理" key="inventory">
                  <Table
                    dataSource={mockInventory}
                    columns={inventoryColumns}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }}
                    loading={inventoryLoading}
                  />
                </TabPane>
                
                <TabPane tab="供应商管理" key="suppliers">
                  <Table
                    dataSource={mockSuppliers}
                    columns={supplierColumns}
                    rowKey="id"
                    pagination={false}
                  />
                </TabPane>
                
                <TabPane tab="采购订单" key="orders">
                  <Table
                    dataSource={mockPurchaseOrders}
                    columns={orderColumns}
                    rowKey="id"
                    pagination={false}
                  />
                </TabPane>
              </Tabs>
            </Card>
          </motion.div>

          {/* 创建采购订单模态框 */}
          <Modal
            title="创建采购订单"
            open={purchaseModalVisible}
            onCancel={() => {
              setPurchaseModalVisible(false);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            okText="创建订单"
            cancelText="取消"
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreatePurchaseOrder}
            >
              <Form.Item
                name="material"
                label="物料名称"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
              
              <Form.Item
                name="quantity"
                label="采购数量"
                rules={[{ required: true, message: '请输入采购数量' }]}
              >
                <Input type="number" placeholder="请输入采购数量" />
              </Form.Item>
              
              <Form.Item
                name="unit_price"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <Input type="number" step="0.01" placeholder="请输入单价" />
              </Form.Item>
              
              <Form.Item
                name="supplier"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <Select placeholder="请选择供应商">
                  {mockSuppliers.map(supplier => (
                    <Option key={supplier.id} value={supplier.name}>{supplier.name}</Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                name="expected_delivery"
                label="期望交付日期"
                rules={[{ required: true, message: '请选择期望交付日期' }]}
              >
                <Input type="date" />
              </Form.Item>
              
              <Form.Item
                name="notes"
                label="备注"
              >
                <Input.TextArea rows={3} placeholder="请输入备注信息" />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </SupplyChainContainer>
    </MainLayout>
  );
};

export default SupplyChainPage;
