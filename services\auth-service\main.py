"""
工业智能体平台 - 认证服务
提供用户认证、授权、JWT令牌管理等功能
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
import os
import logging
from typing import Optional, Dict, Any
import asyncpg
import asyncio
from contextlib import asynccontextmanager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# 数据库连接池
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool
    
    # 启动时创建数据库连接池
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    logger.info("Database connection pool created")
    
    yield
    
    # 关闭时清理资源
    if db_pool:
        await db_pool.close()
        logger.info("Database connection pool closed")

# 创建FastAPI应用
app = FastAPI(
    title="认证服务",
    description="用户认证、授权、JWT令牌管理",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    department: Optional[str] = None
    role: str = "user"

class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    id: str
    username: str
    email: str
    full_name: Optional[str] = None
    department: Optional[str] = None
    role: str
    is_active: bool

class UserInDB(User):
    password_hash: str

# 工具函数
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    """创建刷新令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_user_by_username(username: str) -> Optional[UserInDB]:
    """根据用户名获取用户"""
    async with db_pool.acquire() as conn:
        row = await conn.fetchrow(
            "SELECT id, username, email, password_hash, full_name, department, role, is_active FROM users WHERE username = $1",
            username
        )
        if row:
            return UserInDB(**dict(row))
    return None

async def get_user_by_id(user_id: str) -> Optional[User]:
    """根据用户ID获取用户"""
    async with db_pool.acquire() as conn:
        row = await conn.fetchrow(
            "SELECT id, username, email, full_name, department, role, is_active FROM users WHERE id = $1",
            user_id
        )
        if row:
            return User(**dict(row))
    return None

async def create_user(user_data: UserCreate) -> User:
    """创建新用户"""
    # 检查用户名是否已存在
    existing_user = await get_user_by_username(user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # 检查邮箱是否已存在
    async with db_pool.acquire() as conn:
        existing_email = await conn.fetchrow(
            "SELECT id FROM users WHERE email = $1", user_data.email
        )
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # 创建用户
        password_hash = get_password_hash(user_data.password)
        user_id = await conn.fetchval(
            """
            INSERT INTO users (username, email, password_hash, full_name, department, role)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id
            """,
            user_data.username,
            user_data.email,
            password_hash,
            user_data.full_name,
            user_data.department,
            user_data.role
        )
        
        return User(
            id=str(user_id),
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            department=user_data.department,
            role=user_data.role,
            is_active=True
        )

async def authenticate_user(username: str, password: str) -> Optional[UserInDB]:
    """验证用户凭据"""
    user = await get_user_by_username(username)
    if not user:
        return None
    if not verify_password(password, user.password_hash):
        return None
    return user

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    user = await get_user_by_username(token_data.username)
    if user is None:
        raise credentials_exception
    
    return User(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        department=user.department,
        role=user.role,
        is_active=user.is_active
    )

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "auth-service"}

@app.post("/register", response_model=User)
async def register(user_data: UserCreate):
    """用户注册"""
    try:
        user = await create_user(user_data)
        logger.info(f"New user registered: {user.username}")
        return user
    except Exception as e:
        logger.error(f"User registration failed: {e}")
        raise

@app.post("/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """用户登录"""
    user = await authenticate_user(user_credentials.username, user_credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # 创建令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id, "role": user.role},
        expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    logger.info(f"User logged in: {user.username}")
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@app.post("/refresh", response_model=Token)
async def refresh_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """刷新令牌"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        
        if username is None or token_type != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        user = await get_user_by_username(username)
        if user is None or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id, "role": user.role},
            expires_delta=access_token_expires
        )
        
        return Token(
            access_token=access_token,
            refresh_token=credentials.credentials,  # 保持原有刷新令牌
            expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

@app.post("/verify-token")
async def verify_token(current_user: User = Depends(get_current_user)):
    """验证令牌"""
    return {
        "user_id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "role": current_user.role,
        "department": current_user.department,
        "is_active": current_user.is_active
    }

@app.get("/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user

@app.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 在实际应用中，这里可以将令牌加入黑名单
    logger.info(f"User logged out: {current_user.username}")
    return {"message": "Successfully logged out"}

# 权限管理
class PermissionManager:
    """权限管理器"""

    @staticmethod
    async def check_permission(user_id: str, resource: str, action: str) -> bool:
        """检查用户权限"""
        try:
            async with db_pool.acquire() as conn:
                # 获取用户角色
                user_roles = await conn.fetch(
                    """
                    SELECT r.role_name, r.permissions
                    FROM user_roles ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = $1 AND ur.is_active = true
                    """,
                    user_id
                )

                # 检查权限
                for role in user_roles:
                    permissions = role['permissions'] or {}
                    resource_perms = permissions.get(resource, [])

                    if action in resource_perms or 'all' in resource_perms:
                        return True

                return False

        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False

    @staticmethod
    async def get_user_permissions(user_id: str) -> Dict[str, List[str]]:
        """获取用户所有权限"""
        try:
            async with db_pool.acquire() as conn:
                user_roles = await conn.fetch(
                    """
                    SELECT r.role_name, r.permissions
                    FROM user_roles ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = $1 AND ur.is_active = true
                    """,
                    user_id
                )

                all_permissions = {}
                for role in user_roles:
                    permissions = role['permissions'] or {}
                    for resource, actions in permissions.items():
                        if resource not in all_permissions:
                            all_permissions[resource] = set()
                        all_permissions[resource].update(actions)

                # 转换为列表
                return {resource: list(actions) for resource, actions in all_permissions.items()}

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return {}

class SecurityManager:
    """安全管理器"""

    @staticmethod
    async def log_security_event(event_type: str, user_id: str = None,
                                ip_address: str = None, details: Dict[str, Any] = None):
        """记录安全事件"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO security_logs
                    (event_type, user_id, ip_address, details, created_at)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    event_type,
                    user_id,
                    ip_address,
                    json.dumps(details) if details else None,
                    datetime.utcnow()
                )

        except Exception as e:
            logger.error(f"Error logging security event: {e}")

    @staticmethod
    async def check_rate_limit(user_id: str, action: str, limit: int = 100, window: int = 3600) -> bool:
        """检查速率限制"""
        try:
            key = f"rate_limit:{user_id}:{action}"
            current_count = await redis_client.get(key)

            if current_count is None:
                await redis_client.setex(key, window, 1)
                return True

            current_count = int(current_count)
            if current_count >= limit:
                return False

            await redis_client.incr(key)
            return True

        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return True

    @staticmethod
    async def detect_suspicious_activity(user_id: str, ip_address: str, action: str) -> bool:
        """检测可疑活动"""
        try:
            # 检查IP地址变化
            last_ip_key = f"last_ip:{user_id}"
            last_ip = await redis_client.get(last_ip_key)

            if last_ip and last_ip.decode() != ip_address:
                # IP地址发生变化，记录安全事件
                await SecurityManager.log_security_event(
                    "ip_change",
                    user_id,
                    ip_address,
                    {"previous_ip": last_ip.decode(), "new_ip": ip_address}
                )

            await redis_client.setex(last_ip_key, 86400, ip_address)  # 24小时过期

            # 检查登录频率
            login_key = f"login_attempts:{user_id}"
            login_count = await redis_client.get(login_key)

            if action == "login":
                if login_count is None:
                    await redis_client.setex(login_key, 3600, 1)
                else:
                    login_count = int(login_count)
                    if login_count > 10:  # 1小时内超过10次登录
                        await SecurityManager.log_security_event(
                            "excessive_login_attempts",
                            user_id,
                            ip_address,
                            {"attempts": login_count}
                        )
                        return True

                    await redis_client.incr(login_key)

            return False

        except Exception as e:
            logger.error(f"Error detecting suspicious activity: {e}")
            return False

# 新增API端点
@app.post("/permissions/check")
async def check_permission(
    resource: str,
    action: str,
    current_user: dict = Depends(get_current_user)
):
    """检查权限"""
    has_permission = await PermissionManager.check_permission(
        current_user["id"], resource, action
    )
    return {"has_permission": has_permission}

@app.get("/permissions/user")
async def get_user_permissions(current_user: dict = Depends(get_current_user)):
    """获取用户权限"""
    permissions = await PermissionManager.get_user_permissions(current_user["id"])
    return {"permissions": permissions}

@app.get("/security/logs")
async def get_security_logs(
    limit: int = 50,
    offset: int = 0,
    current_user: dict = Depends(get_current_user)
):
    """获取安全日志"""
    # 检查管理员权限
    has_permission = await PermissionManager.check_permission(
        current_user["id"], "security", "read"
    )

    if not has_permission:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    async with db_pool.acquire() as conn:
        logs = await conn.fetch(
            """
            SELECT sl.*, u.username
            FROM security_logs sl
            LEFT JOIN users u ON sl.user_id = u.id
            ORDER BY sl.created_at DESC
            LIMIT $1 OFFSET $2
            """,
            limit, offset
        )

        return [dict(log) for log in logs]

@app.post("/roles")
async def create_role(
    role_data: RoleCreate,
    current_user: dict = Depends(get_current_user)
):
    """创建角色"""
    # 检查管理员权限
    has_permission = await PermissionManager.check_permission(
        current_user["id"], "roles", "create"
    )

    if not has_permission:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    role_id = await AuthManager.create_role(role_data)
    return {"message": "Role created successfully", "role_id": role_id}

@app.post("/users/{user_id}/roles")
async def assign_role(
    user_id: str,
    role_assignment: UserRoleAssignment,
    current_user: dict = Depends(get_current_user)
):
    """分配角色"""
    # 检查管理员权限
    has_permission = await PermissionManager.check_permission(
        current_user["id"], "users", "manage"
    )

    if not has_permission:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    success = await AuthManager.assign_role_to_user(user_id, role_assignment.role_id)
    if success:
        return {"message": "Role assigned successfully"}
    else:
        raise HTTPException(status_code=400, detail="Failed to assign role")

# 权限管理
class PermissionManager:
    """权限管理器"""

    @staticmethod
    async def check_permission(user_id: str, resource: str, action: str) -> bool:
        """检查用户权限"""
        try:
            async with db_pool.acquire() as conn:
                # 获取用户角色
                user_roles = await conn.fetch(
                    """
                    SELECT r.role_name, r.permissions
                    FROM user_roles ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = $1 AND ur.is_active = true
                    """,
                    user_id
                )

                # 检查权限
                for role in user_roles:
                    permissions = role['permissions'] or {}
                    resource_perms = permissions.get(resource, [])

                    if action in resource_perms or 'all' in resource_perms:
                        return True

                return False

        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False

class SecurityManager:
    """安全管理器"""

    @staticmethod
    async def log_security_event(event_type: str, user_id: str = None,
                                ip_address: str = None, details: Dict[str, Any] = None):
        """记录安全事件"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO security_logs
                    (event_type, user_id, ip_address, details, created_at)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    event_type,
                    user_id,
                    ip_address,
                    json.dumps(details) if details else None,
                    datetime.utcnow()
                )

        except Exception as e:
            logger.error(f"Error logging security event: {e}")

    @staticmethod
    async def check_rate_limit(user_id: str, action: str, limit: int = 100, window: int = 3600) -> bool:
        """检查速率限制"""
        try:
            key = f"rate_limit:{user_id}:{action}"
            current_count = await redis_client.get(key)

            if current_count is None:
                await redis_client.setex(key, window, 1)
                return True

            current_count = int(current_count)
            if current_count >= limit:
                return False

            await redis_client.incr(key)
            return True

        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return True

@app.post("/permissions/check")
async def check_permission(
    resource: str,
    action: str,
    current_user: dict = Depends(get_current_user)
):
    """检查权限"""
    has_permission = await PermissionManager.check_permission(
        current_user["id"], resource, action
    )
    return {"has_permission": has_permission}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
