/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"J:\\augment\\industry-ai-platform\\web-app\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiSjpcXFxcYXVnbWVudFxcXFxpbmR1c3RyeS1haS1wbGF0Zm9ybVxcXFx3ZWItYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJKJTNBJTVDJTVDYXVnbWVudCU1QyU1Q2luZHVzdHJ5LWFpLXBsYXRmb3JtJTVDJTVDd2ViLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIko6XFxcXGF1Z21lbnRcXFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXFxcd2ViLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22J%3A%5C%5Caugment%5C%5Cindustry-ai-platform%5C%5Cweb-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d15376eed6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXHdlYi1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBkMTUzNzZlZWQ2ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var _barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=App,ConfigProvider!=!antd */ \"(ssr)/../node_modules/antd/es/app/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/../node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(ssr)/../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/locale/zh-cn */ \"(ssr)/../node_modules/dayjs/locale/zh-cn.js\");\n/* harmony import */ var dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_zh_cn__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/relativeTime */ \"(ssr)/../node_modules/dayjs/plugin/relativeTime.js\");\n/* harmony import */ var dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/utc */ \"(ssr)/../node_modules/dayjs/plugin/utc.js\");\n/* harmony import */ var dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/timezone */ \"(ssr)/../node_modules/dayjs/plugin/timezone.js\");\n/* harmony import */ var dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// 配置dayjs\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().locale('zh-cn');\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_relativeTime__WEBPACK_IMPORTED_MODULE_4___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_utc__WEBPACK_IMPORTED_MODULE_5___default()));\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_timezone__WEBPACK_IMPORTED_MODULE_6___default()));\n// 创建QueryClient实例\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 3,\n            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000\n        },\n        mutations: {\n            retry: 1\n        }\n    }\n});\n// Ant Design工业风格主题配置\nconst antdTheme = {\n    token: {\n        colorPrimary: '#4a90e2',\n        colorSuccess: '#00d4aa',\n        colorWarning: '#ff8c42',\n        colorError: '#ff4757',\n        colorInfo: '#4a90e2',\n        colorBgBase: '#0f0f0f',\n        colorBgContainer: '#1a1a1a',\n        colorBgElevated: '#242424',\n        colorBorder: '#333333',\n        colorText: '#ffffff',\n        colorTextSecondary: '#b0b0b0',\n        borderRadius: 8,\n        wireframe: false\n    },\n    components: {\n        Layout: {\n            headerBg: '#1a1a1a',\n            siderBg: '#1a1a1a',\n            bodyBg: '#0f0f0f'\n        },\n        Menu: {\n            darkItemBg: 'transparent',\n            darkSubMenuItemBg: '#242424',\n            darkItemSelectedBg: 'rgba(74, 144, 226, 0.1)',\n            darkItemColor: '#b0b0b0',\n            darkItemSelectedColor: '#4a90e2',\n            darkItemHoverColor: '#4a90e2'\n        },\n        Card: {\n            headerBg: '#242424',\n            colorBgContainer: '#242424'\n        },\n        Table: {\n            headerBg: '#1a1a1a',\n            colorBgContainer: '#242424'\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"基于大语言模型的工业智能体平台\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n                    client: queryClient,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                            theme: antdTheme,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_App_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_13__.ReactQueryDevtools, {\n                            initialIsOpen: false\n                        }, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Dropdown,Layout,Menu,Progress,Row,Statistic,Table,Tag,Timeline!=!antd */ \"(ssr)/../node_modules/antd/es/timeline/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ToolOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpOutlined,BarChartOutlined,BellOutlined,BookOutlined,CheckCircleOutlined,ClockCircleOutlined,DashboardOutlined,ExclamationCircleOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,PlayCircleOutlined,ReloadOutlined,RobotOutlined,SafetyOutlined,SettingOutlined,ShoppingCartOutlined,ToolOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/../node_modules/@ant-design/icons/es/icons/ArrowUpOutlined.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @ant-design/charts */ \"(ssr)/../node_modules/@ant-design/plots/es/components/line/index.js\");\n/* harmony import */ var _ant_design_charts__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @ant-design/charts */ \"(ssr)/../node_modules/@ant-design/plots/es/components/gauge/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! styled-components */ \"(ssr)/../node_modules/styled-components/dist/styled-components.esm.js\");\n/* harmony import */ var react_countup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-countup */ \"(ssr)/../node_modules/react-countup/build/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n// 工业风格样式组件\nconst IndustrialLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"]))`\n  min-height: 100vh;\n  background: var(--bg-primary);\n`;\nconst IndustrialHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Header)`\n  background: var(--bg-secondary);\n  border-bottom: 1px solid var(--border-primary);\n  padding: 0 24px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: linear-gradient(90deg,\n      transparent 0%,\n      var(--color-accent-blue) 50%,\n      transparent 100%);\n    opacity: 0.6;\n  }\n`;\nconst IndustrialSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Sider)`\n  background: var(--bg-secondary);\n  border-right: 1px solid var(--border-primary);\n\n  .ant-layout-sider-trigger {\n    background: var(--bg-primary);\n    border-top: 1px solid var(--border-primary);\n    color: var(--text-secondary);\n\n    &:hover {\n      background: var(--color-accent-blue);\n      color: white;\n    }\n  }\n`;\nconst IndustrialContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Content)`\n  background: var(--bg-primary);\n  padding: 24px;\n  overflow-y: auto;\n`;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 20px;\n  font-weight: 700;\n  color: var(--text-primary);\n\n  .logo-icon {\n    width: 32px;\n    height: 32px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 6px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: white;\n    font-size: 16px;\n  }\n`;\nconst StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"].div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  box-shadow: 0 0 10px ${(props)=>props.status === 'online' ? 'var(--status-success)' : props.status === 'warning' ? 'var(--status-warning)' : 'var(--status-error)'};\n  animation: pulse 2s infinite;\n`;\nconst DashboardCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"]))`\n  background: var(--bg-card);\n  border: 1px solid var(--border-primary);\n  border-radius: 8px;\n  box-shadow: var(--shadow-card);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(90deg,\n      var(--color-accent-blue),\n      var(--color-accent-green));\n    opacity: 0;\n    transition: opacity 0.3s ease;\n  }\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-hover);\n\n    &::before {\n      opacity: 1;\n    }\n  }\n\n  .ant-card-head {\n    background: var(--bg-secondary);\n    border-bottom: 1px solid var(--border-primary);\n    color: var(--text-primary);\n  }\n\n  .ant-card-body {\n    background: var(--bg-card);\n  }\n`;\nconst MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(DashboardCard)`\n  text-align: center;\n\n  .ant-statistic-title {\n    color: var(--text-secondary);\n    font-size: 14px;\n    margin-bottom: 8px;\n  }\n\n  .ant-statistic-content {\n    color: var(--text-primary);\n    font-size: 32px;\n    font-weight: 700;\n  }\n`;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_7__[\"default\"].h2`\n  color: var(--text-primary);\n  font-size: 20px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n\n  &::before {\n    content: '';\n    width: 4px;\n    height: 20px;\n    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));\n    border-radius: 2px;\n  }\n`;\n// 模拟数据\nconst mockData = {\n    overview: {\n        totalEquipment: 156,\n        runningEquipment: 142,\n        maintenanceEquipment: 8,\n        errorEquipment: 6,\n        oeeRate: 0.85,\n        qualityRate: 0.96,\n        productionOutput: 2847,\n        energyConsumption: 1234.5\n    },\n    productionTrend: [\n        {\n            time: '00:00',\n            output: 120,\n            target: 130\n        },\n        {\n            time: '02:00',\n            output: 132,\n            target: 130\n        },\n        {\n            time: '04:00',\n            output: 101,\n            target: 130\n        },\n        {\n            time: '06:00',\n            output: 134,\n            target: 130\n        },\n        {\n            time: '08:00',\n            output: 90,\n            target: 130\n        },\n        {\n            time: '10:00',\n            output: 230,\n            target: 130\n        },\n        {\n            time: '12:00',\n            output: 210,\n            target: 130\n        },\n        {\n            time: '14:00',\n            output: 220,\n            target: 130\n        },\n        {\n            time: '16:00',\n            output: 200,\n            target: 130\n        },\n        {\n            time: '18:00',\n            output: 180,\n            target: 130\n        },\n        {\n            time: '20:00',\n            output: 160,\n            target: 130\n        },\n        {\n            time: '22:00',\n            output: 140,\n            target: 130\n        }\n    ],\n    equipmentStatus: [\n        {\n            name: 'CNC-001',\n            status: 'running',\n            utilization: 85,\n            temperature: 45\n        },\n        {\n            name: 'CNC-002',\n            status: 'running',\n            utilization: 92,\n            temperature: 48\n        },\n        {\n            name: 'Robot-001',\n            status: 'idle',\n            utilization: 0,\n            temperature: 25\n        },\n        {\n            name: 'Press-001',\n            status: 'maintenance',\n            utilization: 0,\n            temperature: 30\n        },\n        {\n            name: 'Grinder-001',\n            status: 'error',\n            utilization: 0,\n            temperature: 65\n        }\n    ],\n    recentAlerts: [\n        {\n            id: 1,\n            type: 'warning',\n            message: 'CNC-001温度异常',\n            time: '2分钟前'\n        },\n        {\n            id: 2,\n            type: 'error',\n            message: 'Grinder-001故障停机',\n            time: '15分钟前'\n        },\n        {\n            id: 3,\n            type: 'info',\n            message: 'Robot-001完成维护',\n            time: '1小时前'\n        },\n        {\n            id: 4,\n            type: 'success',\n            message: '生产线A达成日产目标',\n            time: '2小时前'\n        }\n    ],\n    agentTasks: [\n        {\n            id: 1,\n            name: '生产排产优化',\n            agent: '排产智能体',\n            status: 'running',\n            progress: 75\n        },\n        {\n            id: 2,\n            name: '设备预测维护',\n            agent: '维护智能体',\n            status: 'completed',\n            progress: 100\n        },\n        {\n            id: 3,\n            name: '质量异常分析',\n            agent: '质量智能体',\n            status: 'pending',\n            progress: 0\n        },\n        {\n            id: 4,\n            name: '供应链风险评估',\n            agent: '供应链智能体',\n            status: 'running',\n            progress: 45\n        }\n    ]\n};\nfunction HomePage() {\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // 获取仪表板数据\n    const { data: dashboardData, loading, error, refetch } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_5__.useDashboard)();\n    // 获取系统健康状态\n    const { isHealthy } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_5__.useSystemHealth)();\n    // 根据系统健康状态确定状态指示器\n    const systemStatus = isHealthy ? 'online' : 'offline';\n    // 使用真实数据或回退到模拟数据\n    const displayData = dashboardData || {\n        overview: {\n            total_production_lines: mockData.overview.totalEquipment,\n            active_lines: mockData.overview.runningEquipment,\n            average_efficiency: mockData.overview.oeeRate * 100,\n            total_equipment: mockData.overview.totalEquipment,\n            operational_equipment: mockData.overview.runningEquipment\n        },\n        production_lines: [],\n        equipment: [],\n        quality_metrics: {\n            defect_rate: 1 - mockData.overview.qualityRate,\n            first_pass_yield: mockData.overview.qualityRate,\n            customer_complaints: 2,\n            quality_score: mockData.overview.qualityRate * 100\n        },\n        inventory_alerts: []\n    };\n    // 路径映射\n    const pathMapping = {\n        'dashboard': '/',\n        'agents': '/agents',\n        'agent-chat': '/agents/chat',\n        'agent-workflow': '/agents/orchestration',\n        'agent-tasks': '/agents/tasks',\n        'production': '/production',\n        'production-planning': '/production',\n        'production-monitoring': '/production',\n        'production-analysis': '/production',\n        'quality': '/quality',\n        'quality-inspection': '/quality',\n        'quality-analysis': '/quality',\n        'quality-standards': '/quality',\n        'maintenance': '/maintenance',\n        'equipment-monitoring': '/maintenance',\n        'predictive-maintenance': '/maintenance',\n        'maintenance-planning': '/maintenance',\n        'supply-chain': '/supply-chain',\n        'inventory-management': '/supply-chain',\n        'supplier-management': '/supply-chain',\n        'procurement': '/supply-chain',\n        'knowledge': '/knowledge',\n        'knowledge-search': '/knowledge',\n        'knowledge-graph': '/knowledge',\n        'document-management': '/knowledge',\n        'monitoring': '/monitoring'\n    };\n    // 菜单项\n    const menuItems = [\n        {\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 13\n            }, this),\n            label: '总览'\n        },\n        {\n            key: 'agents',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 13\n            }, this),\n            label: '智能体',\n            children: [\n                {\n                    key: 'agent-chat',\n                    label: '智能对话'\n                },\n                {\n                    key: 'agent-workflow',\n                    label: '工作流'\n                },\n                {\n                    key: 'agent-tasks',\n                    label: '任务管理'\n                }\n            ]\n        },\n        {\n            key: 'production',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, this),\n            label: '生产管理',\n            children: [\n                {\n                    key: 'production-planning',\n                    label: '生产计划'\n                },\n                {\n                    key: 'production-monitoring',\n                    label: '生产监控'\n                },\n                {\n                    key: 'production-analysis',\n                    label: '生产分析'\n                }\n            ]\n        },\n        {\n            key: 'quality',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 345,\n                columnNumber: 13\n            }, this),\n            label: '质量管理',\n            children: [\n                {\n                    key: 'quality-inspection',\n                    label: '质量检验'\n                },\n                {\n                    key: 'quality-analysis',\n                    label: '质量分析'\n                },\n                {\n                    key: 'quality-standards',\n                    label: '质量标准'\n                }\n            ]\n        },\n        {\n            key: 'maintenance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, this),\n            label: '设备维护',\n            children: [\n                {\n                    key: 'equipment-monitoring',\n                    label: '设备监控'\n                },\n                {\n                    key: 'predictive-maintenance',\n                    label: '预测维护'\n                },\n                {\n                    key: 'maintenance-planning',\n                    label: '维护计划'\n                }\n            ]\n        },\n        {\n            key: 'supply-chain',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 13\n            }, this),\n            label: '供应链',\n            children: [\n                {\n                    key: 'inventory-management',\n                    label: '库存管理'\n                },\n                {\n                    key: 'supplier-management',\n                    label: '供应商管理'\n                },\n                {\n                    key: 'procurement',\n                    label: '采购管理'\n                }\n            ]\n        },\n        {\n            key: 'knowledge',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 13\n            }, this),\n            label: '知识库',\n            children: [\n                {\n                    key: 'knowledge-search',\n                    label: '知识搜索'\n                },\n                {\n                    key: 'knowledge-graph',\n                    label: '知识图谱'\n                },\n                {\n                    key: 'document-management',\n                    label: '文档管理'\n                }\n            ]\n        }\n    ];\n    // 用户菜单\n    const userMenuItems = [\n        {\n            key: 'profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 13\n            }, this),\n            label: '个人资料'\n        },\n        {\n            key: 'settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 13\n            }, this),\n            label: '设置'\n        },\n        {\n            type: 'divider'\n        },\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 13\n            }, this),\n            label: '退出登录'\n        }\n    ];\n    // 生产趋势图配置\n    const productionTrendConfig = {\n        data: mockData.productionTrend,\n        xField: 'time',\n        yField: 'output',\n        seriesField: 'type',\n        smooth: true,\n        animation: {\n            appear: {\n                animation: 'path-in',\n                duration: 1000\n            }\n        }\n    };\n    // OEE仪表盘配置\n    const oeeGaugeConfig = {\n        percent: mockData.overview.oeeRate,\n        range: {\n            color: [\n                '#F4664A',\n                '#FAAD14',\n                '#30BF78'\n            ]\n        },\n        indicator: {\n            pointer: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            },\n            pin: {\n                style: {\n                    stroke: '#D0D0D0'\n                }\n            }\n        },\n        statistic: {\n            content: {\n                style: {\n                    fontSize: '36px',\n                    lineHeight: '36px'\n                }\n            }\n        }\n    };\n    // 设备状态表格列\n    const equipmentColumns = [\n        {\n            title: '设备名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: (status)=>{\n                const statusMap = {\n                    running: {\n                        color: 'green',\n                        text: '运行中'\n                    },\n                    idle: {\n                        color: 'orange',\n                        text: '空闲'\n                    },\n                    maintenance: {\n                        color: 'blue',\n                        text: '维护中'\n                    },\n                    error: {\n                        color: 'red',\n                        text: '故障'\n                    }\n                };\n                const statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    color: statusInfo.color,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '利用率',\n            dataIndex: 'utilization',\n            key: 'utilization',\n            render: (utilization)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    percent: utilization,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '温度',\n            dataIndex: 'temperature',\n            key: 'temperature',\n            render: (temperature)=>`${temperature}°C`\n        }\n    ];\n    // 智能体任务表格列\n    const taskColumns = [\n        {\n            title: '任务名称',\n            dataIndex: 'name',\n            key: 'name'\n        },\n        {\n            title: '智能体',\n            dataIndex: 'agent',\n            key: 'agent'\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            render: (status)=>{\n                const statusMap = {\n                    running: {\n                        color: 'blue',\n                        text: '执行中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 56\n                        }, this)\n                    },\n                    completed: {\n                        color: 'green',\n                        text: '已完成',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 59\n                        }, this)\n                    },\n                    pending: {\n                        color: 'orange',\n                        text: '等待中',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 58\n                        }, this)\n                    },\n                    error: {\n                        color: 'red',\n                        text: '失败',\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 52\n                        }, this)\n                    }\n                };\n                const statusInfo = statusMap[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    color: statusInfo.color,\n                    icon: statusInfo.icon,\n                    children: statusInfo.text\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            title: '进度',\n            dataIndex: 'progress',\n            key: 'progress',\n            render: (progress)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    percent: progress,\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndustrialLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndustrialSider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 240,\n                collapsedWidth: 80,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        style: {\n                            padding: '16px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"logo-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_26__.AnimatePresence, {\n                                    children: !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.span, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            width: 'auto'\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"工业智能体\"\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        theme: \"dark\",\n                        mode: \"inline\",\n                        defaultSelectedKeys: [\n                            'dashboard'\n                        ],\n                        items: menuItems,\n                        style: {\n                            border: 'none'\n                        },\n                        onClick: ({ key })=>{\n                            const targetPath = pathMapping[key];\n                            if (targetPath && targetPath !== '/') {\n                                router.push(targetPath);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndustrialHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: ()=>setCollapsed(!collapsed),\n                                        style: {\n                                            fontSize: '16px',\n                                            width: 40,\n                                            height: 40,\n                                            color: 'var(--text-secondary)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIndicator, {\n                                                status: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: 'var(--text-secondary)',\n                                                    fontSize: '14px'\n                                                },\n                                                children: [\n                                                    \"系统状态: \",\n                                                    systemStatus === 'online' ? '正常' : systemStatus === 'warning' ? '警告' : '离线'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '16px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                color: 'var(--text-secondary)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                cursor: 'pointer'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        marginRight: 8,\n                                                        background: 'var(--color-accent-blue)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: 'var(--text-primary)'\n                                                    },\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndustrialContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: '24px',\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                            children: \"总览仪表板\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {}, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            onClick: refetch,\n                                            loading: loading,\n                                            className: \"metal-button\",\n                                            children: \"刷新数据\"\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                    gutter: [\n                                        24,\n                                        24\n                                    ],\n                                    style: {\n                                        marginBottom: 32\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            title: \"生产线总数\",\n                                                            value: dashboardData?.overview?.total_production_lines || displayData.overview.total_production_lines,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-blue)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 45\n                                                                }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--text-muted)'\n                                                            },\n                                                            children: [\n                                                                \"运行中: \",\n                                                                dashboardData?.overview?.active_lines || displayData.overview.active_lines,\n                                                                \" 条\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            title: \"平均效率\",\n                                                            value: dashboardData?.overview?.average_efficiency || displayData.overview.average_efficiency,\n                                                            precision: 1,\n                                                            suffix: \"%\",\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-success)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 45\n                                                                }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: \"较昨日 +2.3%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            title: \"质量评分\",\n                                                            value: dashboardData?.quality_metrics?.quality_score || displayData.quality_metrics.quality_score,\n                                                            precision: 1,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--color-accent-green)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2,\n                                                                    decimals: 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 45\n                                                                }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-success)'\n                                                            },\n                                                            children: [\n                                                                \"缺陷率: \",\n                                                                ((dashboardData?.quality_metrics?.defect_rate || displayData.quality_metrics.defect_rate) * 100).toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            sm: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            title: \"库存预警\",\n                                                            value: dashboardData?.inventory_alerts?.length || displayData.inventory_alerts.length,\n                                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpOutlined_BarChartOutlined_BellOutlined_BookOutlined_CheckCircleOutlined_ClockCircleOutlined_DashboardOutlined_ExclamationCircleOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_PlayCircleOutlined_ReloadOutlined_RobotOutlined_SafetyOutlined_SettingOutlined_ShoppingCartOutlined_ToolOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                style: {\n                                                                    color: 'var(--status-warning)'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            formatter: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    end: value,\n                                                                    duration: 2\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 45\n                                                                }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginTop: '8px',\n                                                                fontSize: '12px',\n                                                                color: 'var(--status-warning)'\n                                                            },\n                                                            children: \"需要立即处理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: 24\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            lg: 16,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"生产趋势\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/production/monitoring\",\n                                                    children: \"查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        ...productionTrendConfig\n                                                    }, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"OEE指标\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        height: 300\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_charts__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                        ...oeeGaugeConfig\n                                                    }, void 0, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                    gutter: [\n                                        16,\n                                        16\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"设备状态\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/maintenance/equipment-monitoring\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 41\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                                    dataSource: mockData.equipmentStatus,\n                                                    columns: equipmentColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                            xs: 24,\n                                            lg: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"智能体任务\",\n                                                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/agents/tasks\",\n                                                    children: \"查看全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 42\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                                    dataSource: mockData.agentTasks,\n                                                    columns: taskColumns,\n                                                    pagination: false,\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                    style: {\n                                        marginTop: 16\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                        span: 24,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            title: \"最近告警\",\n                                            extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/alerts\",\n                                                children: \"查看全部\"\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 41\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                                children: mockData.recentAlerts.map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Dropdown_Layout_Menu_Progress_Row_Statistic_Table_Tag_Timeline_antd__WEBPACK_IMPORTED_MODULE_43__[\"default\"].Item, {\n                                                        color: alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'orange' : alert.type === 'success' ? 'green' : 'blue',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                justifyContent: 'space-between'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: alert.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#999',\n                                                                        fontSize: '12px'\n                                                                    },\n                                                                    children: alert.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, alert.id, false, {\n                                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\augment\\\\industry-ai-platform\\\\web-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 530,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAiChat: () => (/* binding */ useAiChat),\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useBatchOperations: () => (/* binding */ useBatchOperations),\n/* harmony export */   useDashboard: () => (/* binding */ useDashboard),\n/* harmony export */   useEquipmentStatus: () => (/* binding */ useEquipmentStatus),\n/* harmony export */   useInventoryStatus: () => (/* binding */ useInventoryStatus),\n/* harmony export */   useProductionAnalytics: () => (/* binding */ useProductionAnalytics),\n/* harmony export */   useProductionLines: () => (/* binding */ useProductionLines),\n/* harmony export */   useQualityMetrics: () => (/* binding */ useQualityMetrics),\n/* harmony export */   useRealTimeData: () => (/* binding */ useRealTimeData),\n/* harmony export */   useSystemHealth: () => (/* binding */ useSystemHealth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/**\n * API数据获取Hook\n */ \n\n// 通用API Hook\nfunction useApi(apiCall, dependencies = []) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApi.useCallback[fetchData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await apiCall();\n                setData(response.data);\n            } catch (err) {\n                setError((0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(err));\n                console.error('API Error:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useApi.useCallback[fetchData]\"], dependencies);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApi.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"useApi.useEffect\"], [\n        fetchData\n    ]);\n    const refetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApi.useCallback[refetch]\": ()=>{\n            fetchData();\n        }\n    }[\"useApi.useCallback[refetch]\"], [\n        fetchData\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        refetch\n    };\n}\n// 仪表板数据Hook\nfunction useDashboard() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.dashboard);\n}\n// 生产线数据Hook\nfunction useProductionLines() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.production.getLines);\n}\n// 设备状态Hook\nfunction useEquipmentStatus() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.equipment.getStatus);\n}\n// 质量指标Hook\nfunction useQualityMetrics() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.quality.getMetrics);\n}\n// 库存状态Hook\nfunction useInventoryStatus() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.inventory.getStatus);\n}\n// 生产分析Hook\nfunction useProductionAnalytics() {\n    return useApi(_services_api__WEBPACK_IMPORTED_MODULE_1__.api.production.getAnalytics);\n}\n// AI聊天Hook\nfunction useAiChat() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAiChat.useCallback[sendMessage]\": async (query)=>{\n            const userMessage = {\n                id: Date.now().toString(),\n                type: 'user',\n                content: query,\n                timestamp: new Date()\n            };\n            setMessages({\n                \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        userMessage\n                    ]\n            }[\"useAiChat.useCallback[sendMessage]\"]);\n            setLoading(true);\n            try {\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_1__.api.ai.chat(query);\n                const aiMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'ai',\n                    content: response.data.response,\n                    timestamp: new Date()\n                };\n                setMessages({\n                    \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            aiMessage\n                        ]\n                }[\"useAiChat.useCallback[sendMessage]\"]);\n            } catch (error) {\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    type: 'ai',\n                    content: '抱歉，我现在无法回答您的问题。请稍后再试。',\n                    timestamp: new Date()\n                };\n                setMessages({\n                    \"useAiChat.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            errorMessage\n                        ]\n                }[\"useAiChat.useCallback[sendMessage]\"]);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useAiChat.useCallback[sendMessage]\"], []);\n    const clearMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAiChat.useCallback[clearMessages]\": ()=>{\n            setMessages([]);\n        }\n    }[\"useAiChat.useCallback[clearMessages]\"], []);\n    return {\n        messages,\n        loading,\n        sendMessage,\n        clearMessages\n    };\n}\n// 实时数据Hook（模拟WebSocket）\nfunction useRealTimeData(apiCall, interval = 30000 // 30秒刷新一次\n) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[fetchData]\": async ()=>{\n            try {\n                setError(null);\n                const response = await apiCall();\n                setData(response.data);\n                setLastUpdate(new Date());\n            } catch (err) {\n                setError((0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(err));\n                console.error('Real-time data error:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useRealTimeData.useCallback[fetchData]\"], [\n        apiCall\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeData.useEffect\": ()=>{\n            // 初始加载\n            fetchData();\n            // 设置定时器\n            const timer = setInterval(fetchData, interval);\n            return ({\n                \"useRealTimeData.useEffect\": ()=>clearInterval(timer)\n            })[\"useRealTimeData.useEffect\"];\n        }\n    }[\"useRealTimeData.useEffect\"], [\n        fetchData,\n        interval\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[forceRefresh]\": ()=>{\n            setLoading(true);\n            fetchData();\n        }\n    }[\"useRealTimeData.useCallback[forceRefresh]\"], [\n        fetchData\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        lastUpdate,\n        forceRefresh\n    };\n}\n// 系统健康状态Hook\nfunction useSystemHealth() {\n    const [isHealthy, setIsHealthy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSystemHealth.useEffect\": ()=>{\n            const checkHealth = {\n                \"useSystemHealth.useEffect.checkHealth\": async ()=>{\n                    try {\n                        await _services_api__WEBPACK_IMPORTED_MODULE_1__.api.health();\n                        setIsHealthy(true);\n                    } catch (error) {\n                        setIsHealthy(false);\n                    } finally{\n                        setLastCheck(new Date());\n                    }\n                }\n            }[\"useSystemHealth.useEffect.checkHealth\"];\n            // 初始检查\n            checkHealth();\n            // 每30秒检查一次\n            const timer = setInterval(checkHealth, 30000);\n            return ({\n                \"useSystemHealth.useEffect\": ()=>clearInterval(timer)\n            })[\"useSystemHealth.useEffect\"];\n        }\n    }[\"useSystemHealth.useEffect\"], []);\n    return {\n        isHealthy,\n        lastCheck\n    };\n}\n// 批量操作Hook\nfunction useBatchOperations() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const executeBatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useBatchOperations.useCallback[executeBatch]\": async (operations)=>{\n            setLoading(true);\n            setResults([]);\n            const batchResults = await Promise.allSettled(operations.map({\n                \"useBatchOperations.useCallback[executeBatch]\": (operation)=>operation()\n            }[\"useBatchOperations.useCallback[executeBatch]\"]));\n            const formattedResults = batchResults.map({\n                \"useBatchOperations.useCallback[executeBatch].formattedResults\": (result)=>{\n                    if (result.status === 'fulfilled') {\n                        return {\n                            success: true,\n                            data: result.value.data\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            error: (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.formatApiError)(result.reason)\n                        };\n                    }\n                }\n            }[\"useBatchOperations.useCallback[executeBatch].formattedResults\"]);\n            setResults(formattedResults);\n            setLoading(false);\n            return formattedResults;\n        }\n    }[\"useBatchOperations.useCallback[executeBatch]\"], []);\n    return {\n        loading,\n        results,\n        executeBatch\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0NBRUMsR0FFd0Q7QUFDVztBQUVwRSxhQUFhO0FBQ04sU0FBU0ssT0FDZEMsT0FBbUMsRUFDbkNDLGVBQXNCLEVBQUU7SUFFeEIsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdULCtDQUFRQSxDQUFXO0lBQzNDLE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNZLE9BQU9DLFNBQVMsR0FBR2IsK0NBQVFBLENBQWdCO0lBRWxELE1BQU1jLFlBQVlaLGtEQUFXQTt5Q0FBQztZQUM1QixJQUFJO2dCQUNGUyxXQUFXO2dCQUNYRSxTQUFTO2dCQUNULE1BQU1FLFdBQVcsTUFBTVQ7Z0JBQ3ZCRyxRQUFRTSxTQUFTUCxJQUFJO1lBQ3ZCLEVBQUUsT0FBT1EsS0FBSztnQkFDWkgsU0FBU1QsNkRBQWNBLENBQUNZO2dCQUN4QkMsUUFBUUwsS0FBSyxDQUFDLGNBQWNJO1lBQzlCLFNBQVU7Z0JBQ1JMLFdBQVc7WUFDYjtRQUNGO3dDQUFHSjtJQUVITixnREFBU0E7NEJBQUM7WUFDUmE7UUFDRjsyQkFBRztRQUFDQTtLQUFVO0lBRWQsTUFBTUksVUFBVWhCLGtEQUFXQTt1Q0FBQztZQUMxQlk7UUFDRjtzQ0FBRztRQUFDQTtLQUFVO0lBRWQsT0FBTztRQUFFTjtRQUFNRTtRQUFTRTtRQUFPTTtJQUFRO0FBQ3pDO0FBRUEsWUFBWTtBQUNMLFNBQVNDO0lBQ2QsT0FBT2QsT0FBc0JGLDhDQUFHQSxDQUFDaUIsU0FBUztBQUM1QztBQUVBLFlBQVk7QUFDTCxTQUFTQztJQUNkLE9BQU9oQixPQUFPRiw4Q0FBR0EsQ0FBQ21CLFVBQVUsQ0FBQ0MsUUFBUTtBQUN2QztBQUVBLFdBQVc7QUFDSixTQUFTQztJQUNkLE9BQU9uQixPQUFPRiw4Q0FBR0EsQ0FBQ3NCLFNBQVMsQ0FBQ0MsU0FBUztBQUN2QztBQUVBLFdBQVc7QUFDSixTQUFTQztJQUNkLE9BQU90QixPQUFPRiw4Q0FBR0EsQ0FBQ3lCLE9BQU8sQ0FBQ0MsVUFBVTtBQUN0QztBQUVBLFdBQVc7QUFDSixTQUFTQztJQUNkLE9BQU96QixPQUFPRiw4Q0FBR0EsQ0FBQzRCLFNBQVMsQ0FBQ0wsU0FBUztBQUN2QztBQUVBLFdBQVc7QUFDSixTQUFTTTtJQUNkLE9BQU8zQixPQUFPRiw4Q0FBR0EsQ0FBQ21CLFVBQVUsQ0FBQ1csWUFBWTtBQUMzQztBQUVBLFdBQVc7QUFDSixTQUFTQztJQUNkLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHcEMsK0NBQVFBLENBS3BDLEVBQUU7SUFDTixNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFFdkMsTUFBTXFDLGNBQWNuQyxrREFBV0E7OENBQUMsT0FBT29DO1lBQ3JDLE1BQU1DLGNBQWM7Z0JBQ2xCQyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Z0JBQ3ZCQyxNQUFNO2dCQUNOQyxTQUFTUDtnQkFDVFEsV0FBVyxJQUFJTDtZQUNqQjtZQUVBTDtzREFBWVcsQ0FBQUEsT0FBUTsyQkFBSUE7d0JBQU1SO3FCQUFZOztZQUMxQzVCLFdBQVc7WUFFWCxJQUFJO2dCQUNGLE1BQU1JLFdBQVcsTUFBTVosOENBQUdBLENBQUM2QyxFQUFFLENBQUNDLElBQUksQ0FBQ1g7Z0JBQ25DLE1BQU1ZLFlBQVk7b0JBQ2hCVixJQUFJLENBQUNDLEtBQUtDLEdBQUcsS0FBSyxHQUFHQyxRQUFRO29CQUM3QkMsTUFBTTtvQkFDTkMsU0FBUzlCLFNBQVNQLElBQUksQ0FBQ08sUUFBUTtvQkFDL0IrQixXQUFXLElBQUlMO2dCQUNqQjtnQkFDQUw7MERBQVlXLENBQUFBLE9BQVE7K0JBQUlBOzRCQUFNRzt5QkFBVTs7WUFDMUMsRUFBRSxPQUFPdEMsT0FBTztnQkFDZCxNQUFNdUMsZUFBZTtvQkFDbkJYLElBQUksQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLLEdBQUdDLFFBQVE7b0JBQzdCQyxNQUFNO29CQUNOQyxTQUFTO29CQUNUQyxXQUFXLElBQUlMO2dCQUNqQjtnQkFDQUw7MERBQVlXLENBQUFBLE9BQVE7K0JBQUlBOzRCQUFNSTt5QkFBYTs7WUFDN0MsU0FBVTtnQkFDUnhDLFdBQVc7WUFDYjtRQUNGOzZDQUFHLEVBQUU7SUFFTCxNQUFNeUMsZ0JBQWdCbEQsa0RBQVdBO2dEQUFDO1lBQ2hDa0MsWUFBWSxFQUFFO1FBQ2hCOytDQUFHLEVBQUU7SUFFTCxPQUFPO1FBQUVEO1FBQVV6QjtRQUFTMkI7UUFBYWU7SUFBYztBQUN6RDtBQUVBLHdCQUF3QjtBQUNqQixTQUFTQyxnQkFDZC9DLE9BQW1DLEVBQ25DZ0QsV0FBbUIsTUFBTSxVQUFVO0FBQVg7SUFFeEIsTUFBTSxDQUFDOUMsTUFBTUMsUUFBUSxHQUFHVCwrQ0FBUUEsQ0FBVztJQUMzQyxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBYztJQUUxRCxNQUFNYyxZQUFZWixrREFBV0E7a0RBQUM7WUFDNUIsSUFBSTtnQkFDRlcsU0FBUztnQkFDVCxNQUFNRSxXQUFXLE1BQU1UO2dCQUN2QkcsUUFBUU0sU0FBU1AsSUFBSTtnQkFDckJnRCxjQUFjLElBQUlmO1lBQ3BCLEVBQUUsT0FBT3pCLEtBQUs7Z0JBQ1pILFNBQVNULDZEQUFjQSxDQUFDWTtnQkFDeEJDLFFBQVFMLEtBQUssQ0FBQyx5QkFBeUJJO1lBQ3pDLFNBQVU7Z0JBQ1JMLFdBQVc7WUFDYjtRQUNGO2lEQUFHO1FBQUNMO0tBQVE7SUFFWkwsZ0RBQVNBO3FDQUFDO1lBQ1IsT0FBTztZQUNQYTtZQUVBLFFBQVE7WUFDUixNQUFNMkMsUUFBUUMsWUFBWTVDLFdBQVd3QztZQUVyQzs2Q0FBTyxJQUFNSyxjQUFjRjs7UUFDN0I7b0NBQUc7UUFBQzNDO1FBQVd3QztLQUFTO0lBRXhCLE1BQU1NLGVBQWUxRCxrREFBV0E7cURBQUM7WUFDL0JTLFdBQVc7WUFDWEc7UUFDRjtvREFBRztRQUFDQTtLQUFVO0lBRWQsT0FBTztRQUFFTjtRQUFNRTtRQUFTRTtRQUFPMkM7UUFBWUs7SUFBYTtBQUMxRDtBQUVBLGFBQWE7QUFDTixTQUFTQztJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHL0QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0UsV0FBV0MsYUFBYSxHQUFHakUsK0NBQVFBLENBQWM7SUFFeERDLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU1pRTt5REFBYztvQkFDbEIsSUFBSTt3QkFDRixNQUFNL0QsOENBQUdBLENBQUNnRSxNQUFNO3dCQUNoQkosYUFBYTtvQkFDZixFQUFFLE9BQU9uRCxPQUFPO3dCQUNkbUQsYUFBYTtvQkFDZixTQUFVO3dCQUNSRSxhQUFhLElBQUl4QjtvQkFDbkI7Z0JBQ0Y7O1lBRUEsT0FBTztZQUNQeUI7WUFFQSxXQUFXO1lBQ1gsTUFBTVQsUUFBUUMsWUFBWVEsYUFBYTtZQUV2Qzs2Q0FBTyxJQUFNUCxjQUFjRjs7UUFDN0I7b0NBQUcsRUFBRTtJQUVMLE9BQU87UUFBRUs7UUFBV0U7SUFBVTtBQUNoQztBQUVBLFdBQVc7QUFDSixTQUFTSTtJQUNkLE1BQU0sQ0FBQzFELFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUUsU0FBU0MsV0FBVyxHQUFHdEUsK0NBQVFBLENBQTBELEVBQUU7SUFFbEcsTUFBTXVFLGVBQWVyRSxrREFBV0E7d0RBQUMsT0FBT3NFO1lBQ3RDN0QsV0FBVztZQUNYMkQsV0FBVyxFQUFFO1lBRWIsTUFBTUcsZUFBZSxNQUFNQyxRQUFRQyxVQUFVLENBQzNDSCxXQUFXSSxHQUFHO2dFQUFDQyxDQUFBQSxZQUFhQTs7WUFHOUIsTUFBTUMsbUJBQW1CTCxhQUFhRyxHQUFHO2lGQUFDRyxDQUFBQTtvQkFDeEMsSUFBSUEsT0FBT0MsTUFBTSxLQUFLLGFBQWE7d0JBQ2pDLE9BQU87NEJBQUVDLFNBQVM7NEJBQU16RSxNQUFNdUUsT0FBT0csS0FBSyxDQUFDMUUsSUFBSTt3QkFBQztvQkFDbEQsT0FBTzt3QkFDTCxPQUFPOzRCQUFFeUUsU0FBUzs0QkFBT3JFLE9BQU9SLDZEQUFjQSxDQUFDMkUsT0FBT0ksTUFBTTt3QkFBRTtvQkFDaEU7Z0JBQ0Y7O1lBRUFiLFdBQVdRO1lBQ1huRSxXQUFXO1lBRVgsT0FBT21FO1FBQ1Q7dURBQUcsRUFBRTtJQUVMLE9BQU87UUFBRXBFO1FBQVMyRDtRQUFTRTtJQUFhO0FBQzFDIiwic291cmNlcyI6WyJKOlxcYXVnbWVudFxcaW5kdXN0cnktYWktcGxhdGZvcm1cXHdlYi1hcHBcXHNyY1xcaG9va3NcXHVzZUFwaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFQSeaVsOaNruiOt+WPlkhvb2tcbiAqL1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGFwaSwgRGFzaGJvYXJkRGF0YSwgZm9ybWF0QXBpRXJyb3IgfSBmcm9tICdAL3NlcnZpY2VzL2FwaSc7XG5cbi8vIOmAmueUqEFQSSBIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpPFQ+KFxuICBhcGlDYWxsOiAoKSA9PiBQcm9taXNlPHsgZGF0YTogVCB9PixcbiAgZGVwZW5kZW5jaWVzOiBhbnlbXSA9IFtdXG4pIHtcbiAgY29uc3QgW2RhdGEsIHNldERhdGFdID0gdXNlU3RhdGU8VCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICBjb25zdCBmZXRjaERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2FsbCgpO1xuICAgICAgc2V0RGF0YShyZXNwb25zZS5kYXRhKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGZvcm1hdEFwaUVycm9yKGVycikpO1xuICAgICAgY29uc29sZS5lcnJvcignQVBJIEVycm9yOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgZGVwZW5kZW5jaWVzKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoRGF0YSgpO1xuICB9LCBbZmV0Y2hEYXRhXSk7XG5cbiAgY29uc3QgcmVmZXRjaCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBmZXRjaERhdGEoKTtcbiAgfSwgW2ZldGNoRGF0YV0pO1xuXG4gIHJldHVybiB7IGRhdGEsIGxvYWRpbmcsIGVycm9yLCByZWZldGNoIH07XG59XG5cbi8vIOS7quihqOadv+aVsOaNrkhvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VEYXNoYm9hcmQoKSB7XG4gIHJldHVybiB1c2VBcGk8RGFzaGJvYXJkRGF0YT4oYXBpLmRhc2hib2FyZCk7XG59XG5cbi8vIOeUn+S6p+e6v+aVsOaNrkhvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VQcm9kdWN0aW9uTGluZXMoKSB7XG4gIHJldHVybiB1c2VBcGkoYXBpLnByb2R1Y3Rpb24uZ2V0TGluZXMpO1xufVxuXG4vLyDorr7lpIfnirbmgIFIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlRXF1aXBtZW50U3RhdHVzKCkge1xuICByZXR1cm4gdXNlQXBpKGFwaS5lcXVpcG1lbnQuZ2V0U3RhdHVzKTtcbn1cblxuLy8g6LSo6YeP5oyH5qCHSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVF1YWxpdHlNZXRyaWNzKCkge1xuICByZXR1cm4gdXNlQXBpKGFwaS5xdWFsaXR5LmdldE1ldHJpY3MpO1xufVxuXG4vLyDlupPlrZjnirbmgIFIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlSW52ZW50b3J5U3RhdHVzKCkge1xuICByZXR1cm4gdXNlQXBpKGFwaS5pbnZlbnRvcnkuZ2V0U3RhdHVzKTtcbn1cblxuLy8g55Sf5Lqn5YiG5p6QSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVByb2R1Y3Rpb25BbmFseXRpY3MoKSB7XG4gIHJldHVybiB1c2VBcGkoYXBpLnByb2R1Y3Rpb24uZ2V0QW5hbHl0aWNzKTtcbn1cblxuLy8gQUnogYrlpKlIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlQWlDaGF0KCkge1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPEFycmF5PHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHR5cGU6ICd1c2VyJyB8ICdhaSc7XG4gICAgY29udGVudDogc3RyaW5nO1xuICAgIHRpbWVzdGFtcDogRGF0ZTtcbiAgfT4+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IHNlbmRNZXNzYWdlID0gdXNlQ2FsbGJhY2soYXN5bmMgKHF1ZXJ5OiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB1c2VyTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICB0eXBlOiAndXNlcicgYXMgY29uc3QsXG4gICAgICBjb250ZW50OiBxdWVyeSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICB9O1xuXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgdXNlck1lc3NhZ2VdKTtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmFpLmNoYXQocXVlcnkpO1xuICAgICAgY29uc3QgYWlNZXNzYWdlID0ge1xuICAgICAgICBpZDogKERhdGUubm93KCkgKyAxKS50b1N0cmluZygpLFxuICAgICAgICB0eXBlOiAnYWknIGFzIGNvbnN0LFxuICAgICAgICBjb250ZW50OiByZXNwb25zZS5kYXRhLnJlc3BvbnNlLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICB9O1xuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgYWlNZXNzYWdlXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6IChEYXRlLm5vdygpICsgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogJ2FpJyBhcyBjb25zdCxcbiAgICAgICAgY29udGVudDogJ+aKseatie+8jOaIkeeOsOWcqOaXoOazleWbnuetlOaCqOeahOmXrumimOOAguivt+eojeWQjuWGjeivleOAgicsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIH07XG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBlcnJvck1lc3NhZ2VdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgY2xlYXJNZXNzYWdlcyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRNZXNzYWdlcyhbXSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4geyBtZXNzYWdlcywgbG9hZGluZywgc2VuZE1lc3NhZ2UsIGNsZWFyTWVzc2FnZXMgfTtcbn1cblxuLy8g5a6e5pe25pWw5o2uSG9va++8iOaooeaLn1dlYlNvY2tldO+8iVxuZXhwb3J0IGZ1bmN0aW9uIHVzZVJlYWxUaW1lRGF0YTxUPihcbiAgYXBpQ2FsbDogKCkgPT4gUHJvbWlzZTx7IGRhdGE6IFQgfT4sXG4gIGludGVydmFsOiBudW1iZXIgPSAzMDAwMCAvLyAzMOenkuWIt+aWsOS4gOasoVxuKSB7XG4gIGNvbnN0IFtkYXRhLCBzZXREYXRhXSA9IHVzZVN0YXRlPFQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsYXN0VXBkYXRlLCBzZXRMYXN0VXBkYXRlXSA9IHVzZVN0YXRlPERhdGUgfCBudWxsPihudWxsKTtcblxuICBjb25zdCBmZXRjaERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDYWxsKCk7XG4gICAgICBzZXREYXRhKHJlc3BvbnNlLmRhdGEpO1xuICAgICAgc2V0TGFzdFVwZGF0ZShuZXcgRGF0ZSgpKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGZvcm1hdEFwaUVycm9yKGVycikpO1xuICAgICAgY29uc29sZS5lcnJvcignUmVhbC10aW1lIGRhdGEgZXJyb3I6JywgZXJyKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbYXBpQ2FsbF0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g5Yid5aeL5Yqg6L29XG4gICAgZmV0Y2hEYXRhKCk7XG5cbiAgICAvLyDorr7nva7lrprml7blmahcbiAgICBjb25zdCB0aW1lciA9IHNldEludGVydmFsKGZldGNoRGF0YSwgaW50ZXJ2YWwpO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwodGltZXIpO1xuICB9LCBbZmV0Y2hEYXRhLCBpbnRlcnZhbF0pO1xuXG4gIGNvbnN0IGZvcmNlUmVmcmVzaCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIGZldGNoRGF0YSgpO1xuICB9LCBbZmV0Y2hEYXRhXSk7XG5cbiAgcmV0dXJuIHsgZGF0YSwgbG9hZGluZywgZXJyb3IsIGxhc3RVcGRhdGUsIGZvcmNlUmVmcmVzaCB9O1xufVxuXG4vLyDns7vnu5/lgaXlurfnirbmgIFIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlU3lzdGVtSGVhbHRoKCkge1xuICBjb25zdCBbaXNIZWFsdGh5LCBzZXRJc0hlYWx0aHldID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtsYXN0Q2hlY2ssIHNldExhc3RDaGVja10gPSB1c2VTdGF0ZTxEYXRlIHwgbnVsbD4obnVsbCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjaGVja0hlYWx0aCA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGFwaS5oZWFsdGgoKTtcbiAgICAgICAgc2V0SXNIZWFsdGh5KHRydWUpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgc2V0SXNIZWFsdGh5KGZhbHNlKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExhc3RDaGVjayhuZXcgRGF0ZSgpKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8g5Yid5aeL5qOA5p+lXG4gICAgY2hlY2tIZWFsdGgoKTtcblxuICAgIC8vIOavjzMw56eS5qOA5p+l5LiA5qyhXG4gICAgY29uc3QgdGltZXIgPSBzZXRJbnRlcnZhbChjaGVja0hlYWx0aCwgMzAwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwodGltZXIpO1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIHsgaXNIZWFsdGh5LCBsYXN0Q2hlY2sgfTtcbn1cblxuLy8g5om56YeP5pON5L2cSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUJhdGNoT3BlcmF0aW9ucygpIHtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVzdWx0cywgc2V0UmVzdWx0c10gPSB1c2VTdGF0ZTxBcnJheTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnk7IGVycm9yPzogc3RyaW5nIH0+PihbXSk7XG5cbiAgY29uc3QgZXhlY3V0ZUJhdGNoID0gdXNlQ2FsbGJhY2soYXN5bmMgKG9wZXJhdGlvbnM6IEFycmF5PCgpID0+IFByb21pc2U8YW55Pj4pID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldFJlc3VsdHMoW10pO1xuXG4gICAgY29uc3QgYmF0Y2hSZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGxTZXR0bGVkKFxuICAgICAgb3BlcmF0aW9ucy5tYXAob3BlcmF0aW9uID0+IG9wZXJhdGlvbigpKVxuICAgICk7XG5cbiAgICBjb25zdCBmb3JtYXR0ZWRSZXN1bHRzID0gYmF0Y2hSZXN1bHRzLm1hcChyZXN1bHQgPT4ge1xuICAgICAgaWYgKHJlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3VsdC52YWx1ZS5kYXRhIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGZvcm1hdEFwaUVycm9yKHJlc3VsdC5yZWFzb24pIH07XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBzZXRSZXN1bHRzKGZvcm1hdHRlZFJlc3VsdHMpO1xuICAgIHNldExvYWRpbmcoZmFsc2UpO1xuXG4gICAgcmV0dXJuIGZvcm1hdHRlZFJlc3VsdHM7XG4gIH0sIFtdKTtcblxuICByZXR1cm4geyBsb2FkaW5nLCByZXN1bHRzLCBleGVjdXRlQmF0Y2ggfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiYXBpIiwiZm9ybWF0QXBpRXJyb3IiLCJ1c2VBcGkiLCJhcGlDYWxsIiwiZGVwZW5kZW5jaWVzIiwiZGF0YSIsInNldERhdGEiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJmZXRjaERhdGEiLCJyZXNwb25zZSIsImVyciIsImNvbnNvbGUiLCJyZWZldGNoIiwidXNlRGFzaGJvYXJkIiwiZGFzaGJvYXJkIiwidXNlUHJvZHVjdGlvbkxpbmVzIiwicHJvZHVjdGlvbiIsImdldExpbmVzIiwidXNlRXF1aXBtZW50U3RhdHVzIiwiZXF1aXBtZW50IiwiZ2V0U3RhdHVzIiwidXNlUXVhbGl0eU1ldHJpY3MiLCJxdWFsaXR5IiwiZ2V0TWV0cmljcyIsInVzZUludmVudG9yeVN0YXR1cyIsImludmVudG9yeSIsInVzZVByb2R1Y3Rpb25BbmFseXRpY3MiLCJnZXRBbmFseXRpY3MiLCJ1c2VBaUNoYXQiLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwic2VuZE1lc3NhZ2UiLCJxdWVyeSIsInVzZXJNZXNzYWdlIiwiaWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJ0eXBlIiwiY29udGVudCIsInRpbWVzdGFtcCIsInByZXYiLCJhaSIsImNoYXQiLCJhaU1lc3NhZ2UiLCJlcnJvck1lc3NhZ2UiLCJjbGVhck1lc3NhZ2VzIiwidXNlUmVhbFRpbWVEYXRhIiwiaW50ZXJ2YWwiLCJsYXN0VXBkYXRlIiwic2V0TGFzdFVwZGF0ZSIsInRpbWVyIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwiZm9yY2VSZWZyZXNoIiwidXNlU3lzdGVtSGVhbHRoIiwiaXNIZWFsdGh5Iiwic2V0SXNIZWFsdGh5IiwibGFzdENoZWNrIiwic2V0TGFzdENoZWNrIiwiY2hlY2tIZWFsdGgiLCJoZWFsdGgiLCJ1c2VCYXRjaE9wZXJhdGlvbnMiLCJyZXN1bHRzIiwic2V0UmVzdWx0cyIsImV4ZWN1dGVCYXRjaCIsIm9wZXJhdGlvbnMiLCJiYXRjaFJlc3VsdHMiLCJQcm9taXNlIiwiYWxsU2V0dGxlZCIsIm1hcCIsIm9wZXJhdGlvbiIsImZvcm1hdHRlZFJlc3VsdHMiLCJyZXN1bHQiLCJzdGF0dXMiLCJzdWNjZXNzIiwidmFsdWUiLCJyZWFzb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatApiError: () => (/* binding */ formatApiError),\n/* harmony export */   isApiAvailable: () => (/* binding */ isApiAvailable)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../node_modules/axios/lib/axios.js\");\n/**\n * API服务 - 连接后端工业智能体平台API\n */ \n// API基础配置\nconst API_BASE_URL = 'http://localhost:8888';\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 可以在这里添加认证token等\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n}, (error)=>{\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\n    return Promise.reject(error);\n});\n// API接口定义\nconst api = {\n    // 基础接口\n    health: ()=>apiClient.get('/health'),\n    dashboard: ()=>apiClient.get('/dashboard'),\n    // 生产管理\n    production: {\n        getLines: ()=>apiClient.get('/production/lines'),\n        createPlan: (plan)=>apiClient.post('/production/plan', plan),\n        getAnalytics: ()=>apiClient.get('/analytics/production')\n    },\n    // 设备管理\n    equipment: {\n        getStatus: ()=>apiClient.get('/equipment'),\n        scheduleMaintenance: (task)=>apiClient.post('/maintenance/schedule', task)\n    },\n    // 质量管理\n    quality: {\n        getMetrics: ()=>apiClient.get('/quality/metrics'),\n        recordInspection: (inspection)=>apiClient.post('/quality/inspection', inspection)\n    },\n    // 库存管理\n    inventory: {\n        getStatus: ()=>apiClient.get('/inventory')\n    },\n    // AI智能助手\n    ai: {\n        chat: (query)=>apiClient.get('/ai/chat', {\n                params: {\n                    query\n                }\n            })\n    }\n};\n// 工具函数\nconst formatApiError = (error)=>{\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    }\n    if (error.message) {\n        return error.message;\n    }\n    return '网络错误，请稍后重试';\n};\nconst isApiAvailable = async ()=>{\n    try {\n        await api.health();\n        return true;\n    } catch (error) {\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?8873":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/rc-picker","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-util","vendor-chunks/@babel","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/dayjs","vendor-chunks/rc-dialog","vendor-chunks/rc-collapse","vendor-chunks/@emotion","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/rc-menu","vendor-chunks/styled-components","vendor-chunks/resize-observer-polyfill","vendor-chunks/stylis","vendor-chunks/rc-overflow","vendor-chunks/motion-utils","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/shallowequal","vendor-chunks/rc-tabs","vendor-chunks/rc-input","vendor-chunks/throttle-debounce","vendor-chunks/mime-db","vendor-chunks/rc-table","vendor-chunks/axios","vendor-chunks/rc-select","vendor-chunks/rc-tree","vendor-chunks/rc-virtual-list","vendor-chunks/follow-redirects","vendor-chunks/rc-progress","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/rc-checkbox","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/react-countup","vendor-chunks/countup.js","vendor-chunks/@antv","vendor-chunks/lodash","vendor-chunks/gl-matrix","vendor-chunks/d3-geo","vendor-chunks/d3-shape","vendor-chunks/d3-array","vendor-chunks/d3-hierarchy","vendor-chunks/d3-scale-chromatic","vendor-chunks/d3-force","vendor-chunks/tslib","vendor-chunks/fecha","vendor-chunks/d3-color","vendor-chunks/d3-format","vendor-chunks/d3-quadtree","vendor-chunks/eventemitter3","vendor-chunks/d3-dsv","vendor-chunks/pdfast","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/d3-path","vendor-chunks/d3-interpolate","vendor-chunks/d3-timer","vendor-chunks/d3-dispatch","vendor-chunks/internmap","vendor-chunks/flru","vendor-chunks/simple-swizzle","vendor-chunks/is-arrayish"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=J%3A%5Caugment%5Cindustry-ai-platform%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();