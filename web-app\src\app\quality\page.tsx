'use client';

import React, { useState } from 'react';
import { Row, Col, Card, Table, Button, Tag, Progress, Statistic, Space, Modal, Form, Input, Select, DatePicker, message, Alert } from 'antd';
import {
  SafetyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  FileSearchOutlined,
  TrophyOutlined,
  ExclamationTriangleOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Line, Pie, Column } from '@ant-design/charts';
import styled from 'styled-components';
import MainLayout from '@/components/Layout/MainLayout';
import { useQualityMetrics } from '@/hooks/useApi';
import dayjs from 'dayjs';
import CountUp from 'react-countup';

const { Option } = Select;
const { TextArea } = Input;

// 样式组件
const QualityContainer = styled.div`
  .quality-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
  
  .quality-status {
    &.pass {
      color: var(--status-success);
      background: rgba(0, 212, 170, 0.1);
      border: 1px solid rgba(0, 212, 170, 0.3);
    }
    
    &.fail {
      color: var(--status-error);
      background: rgba(255, 71, 87, 0.1);
      border: 1px solid rgba(255, 71, 87, 0.3);
    }
    
    &.pending {
      color: var(--status-warning);
      background: rgba(255, 140, 66, 0.1);
      border: 1px solid rgba(255, 140, 66, 0.3);
    }
    
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
  
  .defect-level {
    &.critical { color: var(--status-error); }
    &.major { color: var(--status-warning); }
    &.minor { color: var(--color-accent-blue); }
  }
`;

const SectionTitle = styled.h3`
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-green));
    border-radius: 2px;
  }
`;

const QualityPage: React.FC = () => {
  const [inspectionModalVisible, setInspectionModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  // 获取质量数据
  const { data: qualityData, loading: qualityLoading, refetch: refetchQuality } = useQualityMetrics();

  // 模拟质量数据
  const mockQualityMetrics = {
    defect_rate: 0.023,
    first_pass_yield: 0.977,
    customer_complaints: 2,
    quality_score: 94.5,
    total_inspections: 1250,
    passed_inspections: 1221,
    failed_inspections: 29
  };

  // 质量趋势数据
  const qualityTrendData = [
    { date: '2024-12-22', defect_rate: 2.1, quality_score: 95.2 },
    { date: '2024-12-23', defect_rate: 2.3, quality_score: 94.8 },
    { date: '2024-12-24', defect_rate: 1.9, quality_score: 95.6 },
    { date: '2024-12-25', defect_rate: 2.5, quality_score: 94.2 },
    { date: '2024-12-26', defect_rate: 2.2, quality_score: 94.9 },
    { date: '2024-12-27', defect_rate: 2.0, quality_score: 95.1 },
    { date: '2024-12-28', defect_rate: 2.3, quality_score: 94.5 }
  ];

  // 缺陷类型分布
  const defectTypeData = [
    { type: '尺寸偏差', count: 12, percentage: 41.4 },
    { type: '表面缺陷', count: 8, percentage: 27.6 },
    { type: '材料问题', count: 5, percentage: 17.2 },
    { type: '装配错误', count: 4, percentage: 13.8 }
  ];

  // 检验记录数据
  const mockInspections = [
    {
      id: 'insp_001',
      product_id: 'PROD_001',
      product_name: '发动机缸体',
      batch_number: 'B20241228001',
      inspection_type: '首件检验',
      result: 'pass',
      inspector: '张检验员',
      inspection_date: '2024-12-28 09:30',
      defects: []
    },
    {
      id: 'insp_002',
      product_id: 'PROD_002',
      product_name: '变速箱齿轮',
      batch_number: 'B20241228002',
      inspection_type: '过程检验',
      result: 'fail',
      inspector: '李检验员',
      inspection_date: '2024-12-28 10:15',
      defects: ['尺寸偏差', '表面粗糙度超标']
    },
    {
      id: 'insp_003',
      product_id: 'PROD_003',
      product_name: '制动盘',
      batch_number: 'B20241228003',
      inspection_type: '最终检验',
      result: 'pass',
      inspector: '王检验员',
      inspection_date: '2024-12-28 11:00',
      defects: []
    },
    {
      id: 'insp_004',
      product_id: 'PROD_004',
      product_name: '悬挂臂',
      batch_number: 'B20241228004',
      inspection_type: '过程检验',
      result: 'pending',
      inspector: '赵检验员',
      inspection_date: '2024-12-28 11:45',
      defects: []
    }
  ];

  // 检验记录表格列
  const inspectionColumns = [
    {
      title: '产品信息',
      key: 'product',
      render: (_, record: any) => (
        <div>
          <div style={{ color: 'var(--text-primary)', fontWeight: 500 }}>{record.product_name}</div>
          <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
            {record.product_id} | {record.batch_number}
          </div>
        </div>
      )
    },
    {
      title: '检验类型',
      dataIndex: 'inspection_type',
      key: 'inspection_type',
      render: (type: string) => {
        const colors = {
          '首件检验': 'blue',
          '过程检验': 'green',
          '最终检验': 'purple',
          '抽样检验': 'orange'
        };
        return <Tag color={colors[type as keyof typeof colors]}>{type}</Tag>;
      }
    },
    {
      title: '检验结果',
      dataIndex: 'result',
      key: 'result',
      render: (result: string) => {
        const statusConfig = {
          pass: { text: '合格', class: 'pass', icon: <CheckCircleOutlined /> },
          fail: { text: '不合格', class: 'fail', icon: <CloseCircleOutlined /> },
          pending: { text: '检验中', class: 'pending', icon: <ExclamationTriangleOutlined /> }
        };
        const config = statusConfig[result as keyof typeof statusConfig];
        return (
          <span className={`quality-status ${config.class}`}>
            {config.icon}
            {config.text}
          </span>
        );
      }
    },
    {
      title: '缺陷',
      dataIndex: 'defects',
      key: 'defects',
      render: (defects: string[]) => (
        <div>
          {defects.length === 0 ? (
            <span style={{ color: 'var(--text-muted)' }}>无</span>
          ) : (
            defects.map((defect, index) => (
              <Tag key={index} color="red" style={{ marginBottom: '2px' }}>
                {defect}
              </Tag>
            ))
          )}
        </div>
      )
    },
    {
      title: '检验员',
      dataIndex: 'inspector',
      key: 'inspector'
    },
    {
      title: '检验时间',
      dataIndex: 'inspection_date',
      key: 'inspection_date',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button size="small" icon={<FileSearchOutlined />}>
            查看详情
          </Button>
          {record.result === 'fail' && (
            <Button size="small" type="primary">
              处理缺陷
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 创建检验记录
  const handleCreateInspection = async (values: any) => {
    try {
      // 这里调用API创建检验记录
      message.success('检验记录创建成功！');
      setInspectionModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建检验记录失败');
    }
  };

  return (
    <MainLayout>
      <QualityContainer>
        <div style={{ padding: '24px' }}>
          <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SectionTitle>质量管理</SectionTitle>
            <Space>
              <Button 
                icon={<PlusOutlined />} 
                type="primary"
                onClick={() => setInspectionModalVisible(true)}
              >
                新建检验
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refetchQuality}
                loading={qualityLoading}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 质量预警 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            <Col span={24}>
              <Alert
                message="质量预警"
                description="变速箱齿轮生产线缺陷率上升，建议检查工艺参数。今日已发现2批次不合格产品。"
                type="warning"
                icon={<WarningOutlined />}
                showIcon
                closable
              />
            </Col>
          </Row>

          {/* 质量指标概览 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="质量评分"
                    value={mockQualityMetrics.quality_score}
                    precision={1}
                    prefix={<TrophyOutlined style={{ color: 'var(--color-accent-green)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-success)' }}>
                    较昨日 +0.3
                  </div>
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="缺陷率"
                    value={mockQualityMetrics.defect_rate * 100}
                    precision={2}
                    suffix="%"
                    prefix={<ExclamationTriangleOutlined style={{ color: 'var(--status-warning)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} decimals={2} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-warning)' }}>
                    目标: ≤2.0%
                  </div>
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="一次通过率"
                    value={mockQualityMetrics.first_pass_yield * 100}
                    precision={1}
                    suffix="%"
                    prefix={<CheckCircleOutlined style={{ color: 'var(--status-success)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} decimals={1} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--status-success)' }}>
                    目标: ≥98.0%
                  </div>
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="客户投诉"
                    value={mockQualityMetrics.customer_complaints}
                    prefix={<CloseCircleOutlined style={{ color: 'var(--status-error)' }} />}
                    formatter={(value) => <CountUp end={value as number} duration={2} />}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: 'var(--text-muted)' }}>
                    本月累计
                  </div>
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="quality-card" title="质量趋势" style={{ height: '400px' }}>
                  <Line
                    data={qualityTrendData}
                    xField="date"
                    yField="defect_rate"
                    smooth
                    color="var(--status-warning)"
                    point={{
                      size: 4,
                      shape: 'circle'
                    }}
                    yAxis={{
                      title: {
                        text: '缺陷率 (%)'
                      }
                    }}
                    xAxis={{
                      title: {
                        text: '日期'
                      }
                    }}
                  />
                </Card>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="quality-card" title="缺陷类型分布" style={{ height: '400px' }}>
                  <Pie
                    data={defectTypeData}
                    angleField="count"
                    colorField="type"
                    radius={0.8}
                    innerRadius={0.4}
                    label={{
                      type: 'outer',
                      content: '{name}: {percentage}%'
                    }}
                    color={['#ff4757', '#ff8c42', '#4a90e2', '#00d4aa']}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 检验统计 */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} lg={8}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="总检验次数"
                    value={mockQualityMetrics.total_inspections}
                    prefix={<FileSearchOutlined style={{ color: 'var(--color-accent-blue)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} lg={8}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="合格检验"
                    value={mockQualityMetrics.passed_inspections}
                    prefix={<CheckCircleOutlined style={{ color: 'var(--status-success)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
            
            <Col xs={24} lg={8}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 }}
              >
                <Card className="quality-card">
                  <Statistic
                    title="不合格检验"
                    value={mockQualityMetrics.failed_inspections}
                    prefix={<CloseCircleOutlined style={{ color: 'var(--status-error)' }} />}
                  />
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* 检验记录列表 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <Card className="quality-card" title="检验记录">
              <Table
                dataSource={mockInspections}
                columns={inspectionColumns}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                loading={qualityLoading}
              />
            </Card>
          </motion.div>

          {/* 创建检验记录模态框 */}
          <Modal
            title="创建检验记录"
            open={inspectionModalVisible}
            onCancel={() => {
              setInspectionModalVisible(false);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            okText="创建"
            cancelText="取消"
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreateInspection}
            >
              <Form.Item
                name="product_id"
                label="产品ID"
                rules={[{ required: true, message: '请输入产品ID' }]}
              >
                <Input placeholder="请输入产品ID" />
              </Form.Item>
              
              <Form.Item
                name="batch_number"
                label="批次号"
                rules={[{ required: true, message: '请输入批次号' }]}
              >
                <Input placeholder="请输入批次号" />
              </Form.Item>
              
              <Form.Item
                name="inspection_type"
                label="检验类型"
                rules={[{ required: true, message: '请选择检验类型' }]}
              >
                <Select placeholder="请选择检验类型">
                  <Option value="首件检验">首件检验</Option>
                  <Option value="过程检验">过程检验</Option>
                  <Option value="最终检验">最终检验</Option>
                  <Option value="抽样检验">抽样检验</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="result"
                label="检验结果"
                rules={[{ required: true, message: '请选择检验结果' }]}
              >
                <Select placeholder="请选择检验结果">
                  <Option value="pass">合格</Option>
                  <Option value="fail">不合格</Option>
                  <Option value="pending">检验中</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="defects"
                label="缺陷描述"
              >
                <TextArea rows={4} placeholder="如有缺陷，请详细描述" />
              </Form.Item>
              
              <Form.Item
                name="inspector"
                label="检验员"
                rules={[{ required: true, message: '请输入检验员姓名' }]}
              >
                <Input placeholder="请输入检验员姓名" />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </QualityContainer>
    </MainLayout>
  );
};

export default QualityPage;
