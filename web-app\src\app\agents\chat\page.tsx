'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, Avatar, Space, Typography, Divider, Tag, Spin } from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  ClearOutlined,
  <PERSON>boltOutlined,
  Bar<PERSON><PERSON>Outlined,
  ToolOutlined,
  SafetyOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { useAiChat } from '@/hooks/useApi';
import MainLayout from '@/components/Layout/MainLayout';

const { TextArea } = Input;
const { Text, Title } = Typography;

// 样式组件
const ChatContainer = styled.div`
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
`;

const ChatHeader = styled.div`
  padding: 24px;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 8px 8px 0 0;
  border-bottom: none;
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 16px;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-top: none;
  border-bottom: none;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--color-accent-gray);
    border-radius: 3px;
  }
`;

const ChatInput = styled.div`
  padding: 16px;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0 0 8px 8px;
  border-top: none;
`;

const MessageBubble = styled.div<{ isUser: boolean }>`
  display: flex;
  margin-bottom: 16px;
  justify-content: ${props => props.isUser ? 'flex-end' : 'flex-start'};
`;

const MessageContent = styled.div<{ isUser: boolean }>`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  background: ${props => props.isUser ? 'var(--color-accent-blue)' : 'var(--bg-secondary)'};
  color: ${props => props.isUser ? 'white' : 'var(--text-primary)'};
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border: 6px solid transparent;
    ${props => props.isUser ? `
      right: -12px;
      border-left-color: var(--color-accent-blue);
    ` : `
      left: -12px;
      border-right-color: var(--bg-secondary);
    `}
  }
`;

const QuickActions = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
`;

const QuickActionButton = styled(Button)`
  border-radius: 20px;
  border: 1px solid var(--border-primary);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  
  &:hover {
    border-color: var(--color-accent-blue);
    color: var(--color-accent-blue);
    background: rgba(74, 144, 226, 0.1);
  }
`;

// 快捷问题
const quickQuestions = [
  { icon: <BarChartOutlined />, text: '生产效率如何？', query: '生产效率' },
  { icon: <ToolOutlined />, text: '设备状态检查', query: '设备状态' },
  { icon: <SafetyOutlined />, text: '质量指标分析', query: '质量问题' },
  { icon: <ShoppingCartOutlined />, text: '库存预警情况', query: '库存管理' },
  { icon: <ThunderboltOutlined />, text: '系统优化建议', query: '维护建议' }
];

const AiChatPage: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { messages, loading, sendMessage, clearMessages } = useAiChat();

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!inputValue.trim() || loading) return;
    
    const message = inputValue.trim();
    setInputValue('');
    await sendMessage(message);
  };

  const handleQuickQuestion = async (query: string) => {
    if (loading) return;
    await sendMessage(query);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <MainLayout>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ color: 'var(--text-primary)', marginBottom: '24px' }}>
          <RobotOutlined style={{ marginRight: '12px', color: 'var(--color-accent-blue)' }} />
          AI智能助手
        </Title>
        
        <ChatContainer>
          <ChatHeader>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={4} style={{ color: 'var(--text-primary)', margin: 0 }}>
                  工业智能体助手
                </Title>
                <Text style={{ color: 'var(--text-secondary)' }}>
                  我可以帮您分析生产数据、优化工艺流程、预测设备故障等
                </Text>
              </div>
              <Button 
                icon={<ClearOutlined />} 
                onClick={clearMessages}
                className="metal-button"
              >
                清空对话
              </Button>
            </div>
            
            <Divider style={{ borderColor: 'var(--border-primary)', margin: '16px 0' }} />
            
            <QuickActions>
              {quickQuestions.map((item, index) => (
                <QuickActionButton
                  key={index}
                  icon={item.icon}
                  size="small"
                  onClick={() => handleQuickQuestion(item.query)}
                  disabled={loading}
                >
                  {item.text}
                </QuickActionButton>
              ))}
            </QuickActions>
          </ChatHeader>

          <ChatMessages>
            {messages.length === 0 && (
              <div style={{ 
                textAlign: 'center', 
                padding: '40px', 
                color: 'var(--text-muted)' 
              }}>
                <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>您好！我是工业智能体AI助手</div>
                <div>请选择上方的快捷问题或直接输入您的问题</div>
              </div>
            )}
            
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <MessageBubble isUser={message.type === 'user'}>
                    <Space align="start" direction={message.type === 'user' ? 'horizontal-reverse' : 'horizontal'}>
                      <Avatar 
                        icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />}
                        style={{ 
                          background: message.type === 'user' ? 'var(--color-accent-blue)' : 'var(--color-accent-green)' 
                        }}
                      />
                      <MessageContent isUser={message.type === 'user'}>
                        <div style={{ marginBottom: '4px' }}>
                          {message.content}
                        </div>
                        <div style={{ 
                          fontSize: '12px', 
                          opacity: 0.7,
                          textAlign: message.type === 'user' ? 'right' : 'left'
                        }}>
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </MessageContent>
                    </Space>
                  </MessageBubble>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {loading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                style={{ textAlign: 'center', padding: '16px' }}
              >
                <Spin size="small" />
                <Text style={{ marginLeft: '8px', color: 'var(--text-secondary)' }}>
                  AI正在思考中...
                </Text>
              </motion.div>
            )}
            
            <div ref={messagesEndRef} />
          </ChatMessages>

          <ChatInput>
            <Space.Compact style={{ width: '100%' }}>
              <TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="请输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                disabled={loading}
                style={{
                  background: 'var(--bg-secondary)',
                  border: '1px solid var(--border-primary)',
                  color: 'var(--text-primary)',
                  resize: 'none'
                }}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSend}
                loading={loading}
                disabled={!inputValue.trim()}
                style={{
                  background: 'var(--color-accent-blue)',
                  borderColor: 'var(--color-accent-blue)',
                  height: 'auto'
                }}
              >
                发送
              </Button>
            </Space.Compact>
          </ChatInput>
        </ChatContainer>
      </div>
    </MainLayout>
  );
};

export default AiChatPage;
