"""
专项算法模块
包含排产优化、预测维护、质量检测等专业算法
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from abc import ABC, abstractmethod
import random
from sklearn.ensemble import RandomForestRegressor, IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, accuracy_score
import joblib
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class ProductionOrder:
    """生产订单"""
    id: str
    product_id: str
    quantity: int
    priority: int  # 1-10, 1最高
    due_date: datetime
    estimated_duration: int  # 分钟
    required_equipment: List[str]
    dependencies: List[str] = None  # 依赖的其他订单

@dataclass
class Equipment:
    """设备"""
    id: str
    name: str
    capabilities: List[str]
    efficiency: float  # 0-1
    maintenance_schedule: List[datetime] = None
    current_status: str = "available"  # available, busy, maintenance, breakdown

@dataclass
class ScheduleItem:
    """排产项目"""
    order_id: str
    equipment_id: str
    start_time: datetime
    end_time: datetime
    setup_time: int = 0

class OptimizationAlgorithm(ABC):
    """优化算法基类"""
    
    @abstractmethod
    def optimize(self, *args, **kwargs) -> Any:
        pass

class ProductionScheduler(OptimizationAlgorithm):
    """生产排产优化器"""
    
    def __init__(self):
        self.orders = []
        self.equipment = []
        self.schedule = []
    
    def optimize(self, orders: List[ProductionOrder], equipment: List[Equipment], 
                start_time: datetime = None) -> List[ScheduleItem]:
        """
        优化生产排产
        使用遗传算法进行多目标优化
        """
        if start_time is None:
            start_time = datetime.now()
        
        self.orders = orders
        self.equipment = equipment
        
        # 使用启发式算法进行初始排产
        initial_schedule = self._greedy_schedule(start_time)
        
        # 使用遗传算法优化
        optimized_schedule = self._genetic_algorithm_optimize(initial_schedule)
        
        return optimized_schedule
    
    def _greedy_schedule(self, start_time: datetime) -> List[ScheduleItem]:
        """贪心算法初始排产"""
        schedule = []
        equipment_availability = {eq.id: start_time for eq in self.equipment}
        
        # 按优先级和交期排序
        sorted_orders = sorted(self.orders, key=lambda x: (x.priority, x.due_date))
        
        for order in sorted_orders:
            # 找到最适合的设备
            best_equipment = self._find_best_equipment(order, equipment_availability)
            
            if best_equipment:
                start = equipment_availability[best_equipment.id]
                end = start + timedelta(minutes=order.estimated_duration)
                
                schedule_item = ScheduleItem(
                    order_id=order.id,
                    equipment_id=best_equipment.id,
                    start_time=start,
                    end_time=end
                )
                
                schedule.append(schedule_item)
                equipment_availability[best_equipment.id] = end
        
        return schedule
    
    def _find_best_equipment(self, order: ProductionOrder, availability: Dict[str, datetime]) -> Optional[Equipment]:
        """找到最适合的设备"""
        suitable_equipment = []
        
        for eq in self.equipment:
            if eq.current_status == "available":
                # 检查设备能力
                if any(cap in order.required_equipment for cap in eq.capabilities):
                    suitable_equipment.append(eq)
        
        if not suitable_equipment:
            return None
        
        # 选择最早可用的设备
        return min(suitable_equipment, key=lambda eq: availability[eq.id])
    
    def _genetic_algorithm_optimize(self, initial_schedule: List[ScheduleItem]) -> List[ScheduleItem]:
        """遗传算法优化"""
        population_size = 50
        generations = 100
        mutation_rate = 0.1
        
        # 初始化种群
        population = [initial_schedule]
        for _ in range(population_size - 1):
            population.append(self._mutate_schedule(initial_schedule.copy()))
        
        for generation in range(generations):
            # 评估适应度
            fitness_scores = [self._evaluate_fitness(schedule) for schedule in population]
            
            # 选择
            selected = self._selection(population, fitness_scores)
            
            # 交叉和变异
            new_population = []
            for i in range(0, len(selected), 2):
                if i + 1 < len(selected):
                    child1, child2 = self._crossover(selected[i], selected[i + 1])
                    new_population.extend([child1, child2])
                else:
                    new_population.append(selected[i])
            
            # 变异
            for i in range(len(new_population)):
                if random.random() < mutation_rate:
                    new_population[i] = self._mutate_schedule(new_population[i])
            
            population = new_population
        
        # 返回最优解
        fitness_scores = [self._evaluate_fitness(schedule) for schedule in population]
        best_index = np.argmax(fitness_scores)
        return population[best_index]
    
    def _evaluate_fitness(self, schedule: List[ScheduleItem]) -> float:
        """评估排产方案的适应度"""
        if not schedule:
            return 0
        
        # 多目标评估
        makespan_score = self._evaluate_makespan(schedule)
        tardiness_score = self._evaluate_tardiness(schedule)
        utilization_score = self._evaluate_utilization(schedule)
        
        # 加权综合评分
        return 0.4 * makespan_score + 0.4 * tardiness_score + 0.2 * utilization_score
    
    def _evaluate_makespan(self, schedule: List[ScheduleItem]) -> float:
        """评估完工时间"""
        if not schedule:
            return 0
        
        max_end_time = max(item.end_time for item in schedule)
        min_start_time = min(item.start_time for item in schedule)
        makespan = (max_end_time - min_start_time).total_seconds() / 3600  # 小时
        
        # 越短越好，转换为0-1分数
        return max(0, 1 - makespan / 168)  # 假设最大一周
    
    def _evaluate_tardiness(self, schedule: List[ScheduleItem]) -> float:
        """评估延期情况"""
        total_tardiness = 0
        order_dict = {order.id: order for order in self.orders}
        
        for item in schedule:
            order = order_dict.get(item.order_id)
            if order and item.end_time > order.due_date:
                tardiness = (item.end_time - order.due_date).total_seconds() / 3600
                total_tardiness += tardiness * order.priority  # 考虑优先级
        
        # 越少越好
        return max(0, 1 - total_tardiness / 100)
    
    def _evaluate_utilization(self, schedule: List[ScheduleItem]) -> float:
        """评估设备利用率"""
        if not schedule:
            return 0
        
        equipment_usage = {}
        total_time = (max(item.end_time for item in schedule) - 
                     min(item.start_time for item in schedule)).total_seconds()
        
        for item in schedule:
            if item.equipment_id not in equipment_usage:
                equipment_usage[item.equipment_id] = 0
            equipment_usage[item.equipment_id] += (item.end_time - item.start_time).total_seconds()
        
        avg_utilization = sum(equipment_usage.values()) / (len(self.equipment) * total_time)
        return min(1.0, avg_utilization)
    
    def _selection(self, population: List[List[ScheduleItem]], fitness_scores: List[float]) -> List[List[ScheduleItem]]:
        """选择操作"""
        # 轮盘赌选择
        total_fitness = sum(fitness_scores)
        if total_fitness == 0:
            return random.sample(population, len(population) // 2)
        
        probabilities = [score / total_fitness for score in fitness_scores]
        selected = []
        
        for _ in range(len(population) // 2):
            r = random.random()
            cumulative = 0
            for i, prob in enumerate(probabilities):
                cumulative += prob
                if r <= cumulative:
                    selected.append(population[i].copy())
                    break
        
        return selected
    
    def _crossover(self, parent1: List[ScheduleItem], parent2: List[ScheduleItem]) -> Tuple[List[ScheduleItem], List[ScheduleItem]]:
        """交叉操作"""
        if len(parent1) <= 1 or len(parent2) <= 1:
            return parent1.copy(), parent2.copy()
        
        # 单点交叉
        crossover_point = random.randint(1, min(len(parent1), len(parent2)) - 1)
        
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        
        return child1, child2
    
    def _mutate_schedule(self, schedule: List[ScheduleItem]) -> List[ScheduleItem]:
        """变异操作"""
        if len(schedule) < 2:
            return schedule
        
        # 随机交换两个任务
        i, j = random.sample(range(len(schedule)), 2)
        schedule[i], schedule[j] = schedule[j], schedule[i]
        
        return schedule

class PredictiveMaintenanceModel:
    """预测性维护模型"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        self.is_trained = False
    
    def train(self, sensor_data: pd.DataFrame, failure_data: pd.DataFrame):
        """训练预测维护模型"""
        try:
            # 特征工程
            features = self._extract_features(sensor_data)
            
            # 准备标签
            labels = self._prepare_labels(features, failure_data)
            
            # 数据预处理
            X = self.scaler.fit_transform(features)
            y = labels
            
            # 训练模型
            self.model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.model.fit(X, y)
            
            # 训练异常检测器
            self.anomaly_detector.fit(X)
            
            self.is_trained = True
            logger.info("Predictive maintenance model trained successfully")
            
        except Exception as e:
            logger.error(f"Error training predictive maintenance model: {e}")
            raise
    
    def predict_failure(self, sensor_data: pd.DataFrame) -> Dict[str, Any]:
        """预测设备故障"""
        if not self.is_trained:
            raise ValueError("Model not trained yet")
        
        try:
            # 特征提取
            features = self._extract_features(sensor_data)
            X = self.scaler.transform(features)
            
            # 预测剩余使用寿命
            rul_prediction = self.model.predict(X)
            
            # 异常检测
            anomaly_scores = self.anomaly_detector.decision_function(X)
            is_anomaly = self.anomaly_detector.predict(X)
            
            # 风险评估
            risk_level = self._assess_risk(rul_prediction, anomaly_scores)
            
            return {
                "remaining_useful_life": float(np.mean(rul_prediction)),
                "anomaly_score": float(np.mean(anomaly_scores)),
                "is_anomaly": bool(np.any(is_anomaly == -1)),
                "risk_level": risk_level,
                "recommendations": self._generate_recommendations(risk_level, rul_prediction)
            }
            
        except Exception as e:
            logger.error(f"Error predicting failure: {e}")
            raise
    
    def _extract_features(self, sensor_data: pd.DataFrame) -> pd.DataFrame:
        """提取特征"""
        features = pd.DataFrame()
        
        # 统计特征
        features['mean_temperature'] = sensor_data.groupby('equipment_id')['temperature'].mean()
        features['std_temperature'] = sensor_data.groupby('equipment_id')['temperature'].std()
        features['max_temperature'] = sensor_data.groupby('equipment_id')['temperature'].max()
        
        features['mean_vibration'] = sensor_data.groupby('equipment_id')['vibration'].mean()
        features['std_vibration'] = sensor_data.groupby('equipment_id')['vibration'].std()
        features['max_vibration'] = sensor_data.groupby('equipment_id')['vibration'].max()
        
        features['mean_power'] = sensor_data.groupby('equipment_id')['power_consumption'].mean()
        features['std_power'] = sensor_data.groupby('equipment_id')['power_consumption'].std()
        
        # 趋势特征
        for equipment_id in sensor_data['equipment_id'].unique():
            eq_data = sensor_data[sensor_data['equipment_id'] == equipment_id].sort_values('timestamp')
            
            # 温度趋势
            temp_trend = np.polyfit(range(len(eq_data)), eq_data['temperature'], 1)[0]
            features.loc[equipment_id, 'temperature_trend'] = temp_trend
            
            # 振动趋势
            vib_trend = np.polyfit(range(len(eq_data)), eq_data['vibration'], 1)[0]
            features.loc[equipment_id, 'vibration_trend'] = vib_trend
        
        # 填充缺失值
        features = features.fillna(0)
        
        return features
    
    def _prepare_labels(self, features: pd.DataFrame, failure_data: pd.DataFrame) -> np.ndarray:
        """准备标签（剩余使用寿命）"""
        labels = []
        
        for equipment_id in features.index:
            # 查找该设备的故障记录
            equipment_failures = failure_data[failure_data['equipment_id'] == equipment_id]
            
            if len(equipment_failures) > 0:
                # 计算到下次故障的时间
                next_failure = equipment_failures['failure_time'].min()
                current_time = datetime.now()
                rul = max(0, (next_failure - current_time).total_seconds() / 3600)  # 小时
            else:
                # 没有故障记录，假设较长的剩余寿命
                rul = 1000  # 1000小时
            
            labels.append(rul)
        
        return np.array(labels)
    
    def _assess_risk(self, rul_prediction: np.ndarray, anomaly_scores: np.ndarray) -> str:
        """评估风险等级"""
        avg_rul = np.mean(rul_prediction)
        avg_anomaly = np.mean(anomaly_scores)
        
        if avg_rul < 24 or avg_anomaly < -0.5:  # 24小时内或高异常分数
            return "high"
        elif avg_rul < 72 or avg_anomaly < -0.2:  # 72小时内或中等异常分数
            return "medium"
        else:
            return "low"
    
    def _generate_recommendations(self, risk_level: str, rul_prediction: np.ndarray) -> List[str]:
        """生成维护建议"""
        recommendations = []
        
        if risk_level == "high":
            recommendations.extend([
                "立即安排维护检查",
                "准备备件和维修工具",
                "考虑停机维护",
                "通知维护团队"
            ])
        elif risk_level == "medium":
            recommendations.extend([
                "在下次计划维护时重点检查",
                "增加监控频率",
                "准备相关备件",
                "安排维护窗口"
            ])
        else:
            recommendations.extend([
                "继续正常监控",
                "按计划进行预防性维护"
            ])
        
        return recommendations

class QualityInspectionAI:
    """质量检测AI"""
    
    def __init__(self):
        self.defect_classifier = None
        self.dimension_predictor = None
        self.is_trained = False
    
    def train(self, inspection_data: pd.DataFrame):
        """训练质量检测模型"""
        try:
            # 准备特征和标签
            features = inspection_data[['measured_value', 'target_value', 'tolerance_upper', 'tolerance_lower']]
            
            # 缺陷分类标签
            defect_labels = (inspection_data['result'] != 'pass').astype(int)
            
            # 训练缺陷分类器
            from sklearn.ensemble import RandomForestClassifier
            self.defect_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
            self.defect_classifier.fit(features, defect_labels)
            
            # 训练尺寸预测器
            self.dimension_predictor = RandomForestRegressor(n_estimators=100, random_state=42)
            dimension_features = features[['target_value', 'tolerance_upper', 'tolerance_lower']]
            self.dimension_predictor.fit(dimension_features, inspection_data['measured_value'])
            
            self.is_trained = True
            logger.info("Quality inspection AI trained successfully")
            
        except Exception as e:
            logger.error(f"Error training quality inspection AI: {e}")
            raise
    
    def predict_quality(self, measurement_data: Dict[str, Any]) -> Dict[str, Any]:
        """预测质量结果"""
        if not self.is_trained:
            raise ValueError("Model not trained yet")
        
        try:
            features = np.array([[
                measurement_data['measured_value'],
                measurement_data['target_value'],
                measurement_data['tolerance_upper'],
                measurement_data['tolerance_lower']
            ]])
            
            # 预测缺陷概率
            defect_prob = self.defect_classifier.predict_proba(features)[0][1]
            
            # 预测是否合格
            is_defective = defect_prob > 0.5
            
            # 计算偏差
            deviation = abs(measurement_data['measured_value'] - measurement_data['target_value'])
            tolerance_range = measurement_data['tolerance_upper'] - measurement_data['tolerance_lower']
            deviation_ratio = deviation / (tolerance_range / 2) if tolerance_range > 0 else 0
            
            return {
                "is_defective": bool(is_defective),
                "defect_probability": float(defect_prob),
                "deviation": float(deviation),
                "deviation_ratio": float(deviation_ratio),
                "quality_score": float(1 - defect_prob),
                "recommendations": self._generate_quality_recommendations(defect_prob, deviation_ratio)
            }
            
        except Exception as e:
            logger.error(f"Error predicting quality: {e}")
            raise
    
    def _generate_quality_recommendations(self, defect_prob: float, deviation_ratio: float) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        if defect_prob > 0.8:
            recommendations.extend([
                "立即停止生产",
                "检查设备校准",
                "检查工艺参数",
                "重新检验前面的产品"
            ])
        elif defect_prob > 0.5:
            recommendations.extend([
                "增加检验频率",
                "调整工艺参数",
                "检查设备状态"
            ])
        
        if deviation_ratio > 0.8:
            recommendations.append("尺寸偏差较大，检查加工精度")
        
        return recommendations

# 算法工厂
class AlgorithmFactory:
    """算法工厂"""
    
    @staticmethod
    def create_scheduler() -> ProductionScheduler:
        """创建生产排产器"""
        return ProductionScheduler()
    
    @staticmethod
    def create_maintenance_model() -> PredictiveMaintenanceModel:
        """创建预测维护模型"""
        return PredictiveMaintenanceModel()
    
    @staticmethod
    def create_quality_ai() -> QualityInspectionAI:
        """创建质量检测AI"""
        return QualityInspectionAI()
