import { R as ReactQueryDevtools$1 } from './ReactQueryDevtools-Cn7cKi7o.js';
import { R as ReactQueryDevtoolsPanel$1 } from './ReactQueryDevtoolsPanel-D9deyZtU.js';
import 'react';
import '@tanstack/query-devtools';
import '@tanstack/react-query';

declare const ReactQueryDevtools: typeof ReactQueryDevtools$1;
declare const ReactQueryDevtoolsPanel: typeof ReactQueryDevtoolsPanel$1;

export { ReactQueryDevtools, ReactQueryDevtoolsPanel };
