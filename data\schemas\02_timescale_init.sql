-- TimescaleDB 时序数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 传感器数据表
CREATE TABLE IF NOT EXISTS sensor_data (
    time TIMESTAMPTZ NOT NULL,
    source_id TEXT NOT NULL,
    sensor_id TEXT NOT NULL,
    value DOUBLE PRECISION,
    unit TEXT,
    quality TEXT DEFAULT 'good',
    metadata JSONB,
    PRIMARY KEY (time, source_id, sensor_id)
);

-- 创建超表
SELECT create_hypertable('sensor_data', 'time', if_not_exists => TRUE);

-- 设备状态数据表
CREATE TABLE IF NOT EXISTS equipment_status (
    time TIMESTAMPTZ NOT NULL,
    equipment_id TEXT NOT NULL,
    status TEXT NOT NULL, -- running, idle, maintenance, breakdown
    utilization DOUBLE PRECISION, -- 设备利用率 0-1
    temperature DOUBLE PRECISION,
    vibration DOUBLE PRECISION,
    power_consumption DOUBLE PRECISION,
    cycle_count INTEGER,
    error_code TEXT,
    metadata JSONB,
    PRIMARY KEY (time, equipment_id)
);

SELECT create_hypertable('equipment_status', 'time', if_not_exists => TRUE);

-- 生产数据表
CREATE TABLE IF NOT EXISTS production_metrics (
    time TIMESTAMPTZ NOT NULL,
    production_line TEXT NOT NULL,
    product_id TEXT,
    quantity_produced INTEGER DEFAULT 0,
    quantity_defective INTEGER DEFAULT 0,
    cycle_time DOUBLE PRECISION, -- 秒
    throughput DOUBLE PRECISION, -- 件/小时
    oee DOUBLE PRECISION, -- 整体设备效率 0-1
    availability DOUBLE PRECISION, -- 可用性 0-1
    performance DOUBLE PRECISION, -- 性能 0-1
    quality_rate DOUBLE PRECISION, -- 质量率 0-1
    metadata JSONB,
    PRIMARY KEY (time, production_line)
);

SELECT create_hypertable('production_metrics', 'time', if_not_exists => TRUE);

-- 质量检测数据表
CREATE TABLE IF NOT EXISTS quality_measurements (
    time TIMESTAMPTZ NOT NULL,
    inspection_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    measurement_type TEXT NOT NULL, -- dimension, weight, surface, etc.
    measured_value DOUBLE PRECISION,
    target_value DOUBLE PRECISION,
    tolerance_upper DOUBLE PRECISION,
    tolerance_lower DOUBLE PRECISION,
    unit TEXT,
    result TEXT, -- pass, fail, warning
    inspector_id TEXT,
    equipment_id TEXT,
    metadata JSONB,
    PRIMARY KEY (time, inspection_id, measurement_type)
);

SELECT create_hypertable('quality_measurements', 'time', if_not_exists => TRUE);

-- 能耗数据表
CREATE TABLE IF NOT EXISTS energy_consumption (
    time TIMESTAMPTZ NOT NULL,
    meter_id TEXT NOT NULL,
    location TEXT,
    energy_type TEXT, -- electricity, gas, water, compressed_air
    consumption DOUBLE PRECISION,
    unit TEXT,
    cost DOUBLE PRECISION,
    carbon_footprint DOUBLE PRECISION,
    metadata JSONB,
    PRIMARY KEY (time, meter_id)
);

SELECT create_hypertable('energy_consumption', 'time', if_not_exists => TRUE);

-- 库存变动数据表
CREATE TABLE IF NOT EXISTS inventory_movements (
    time TIMESTAMPTZ NOT NULL,
    product_id TEXT NOT NULL,
    location TEXT NOT NULL,
    movement_type TEXT NOT NULL, -- in, out, transfer, adjustment
    quantity DOUBLE PRECISION,
    unit TEXT,
    reference_id TEXT, -- 关联的订单、生产单等
    operator_id TEXT,
    metadata JSONB,
    PRIMARY KEY (time, product_id, location, movement_type)
);

SELECT create_hypertable('inventory_movements', 'time', if_not_exists => TRUE);

-- 报警事件表
CREATE TABLE IF NOT EXISTS alarm_events (
    time TIMESTAMPTZ NOT NULL,
    alarm_id TEXT NOT NULL,
    source_id TEXT NOT NULL,
    alarm_type TEXT NOT NULL, -- equipment, quality, safety, process
    severity TEXT NOT NULL, -- critical, high, medium, low
    status TEXT NOT NULL, -- active, acknowledged, resolved
    message TEXT,
    acknowledged_by TEXT,
    acknowledged_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    metadata JSONB,
    PRIMARY KEY (time, alarm_id)
);

SELECT create_hypertable('alarm_events', 'time', if_not_exists => TRUE);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sensor_data_source_sensor ON sensor_data (source_id, sensor_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_sensor_data_quality ON sensor_data (quality, time DESC);

CREATE INDEX IF NOT EXISTS idx_equipment_status_equipment ON equipment_status (equipment_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_equipment_status_status ON equipment_status (status, time DESC);

CREATE INDEX IF NOT EXISTS idx_production_metrics_line ON production_metrics (production_line, time DESC);
CREATE INDEX IF NOT EXISTS idx_production_metrics_product ON production_metrics (product_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_quality_measurements_product ON quality_measurements (product_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_quality_measurements_result ON quality_measurements (result, time DESC);

CREATE INDEX IF NOT EXISTS idx_energy_consumption_meter ON energy_consumption (meter_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_energy_consumption_type ON energy_consumption (energy_type, time DESC);

CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON inventory_movements (product_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_location ON inventory_movements (location, time DESC);

CREATE INDEX IF NOT EXISTS idx_alarm_events_source ON alarm_events (source_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_alarm_events_status ON alarm_events (status, time DESC);
CREATE INDEX IF NOT EXISTS idx_alarm_events_severity ON alarm_events (severity, time DESC);

-- 创建数据保留策略
-- 传感器数据保留1年，之后压缩
SELECT add_retention_policy('sensor_data', INTERVAL '1 year');
SELECT add_compression_policy('sensor_data', INTERVAL '7 days');

-- 设备状态数据保留2年
SELECT add_retention_policy('equipment_status', INTERVAL '2 years');
SELECT add_compression_policy('equipment_status', INTERVAL '30 days');

-- 生产指标保留5年
SELECT add_retention_policy('production_metrics', INTERVAL '5 years');
SELECT add_compression_policy('production_metrics', INTERVAL '90 days');

-- 质量数据保留10年（法规要求）
SELECT add_compression_policy('quality_measurements', INTERVAL '180 days');

-- 能耗数据保留3年
SELECT add_retention_policy('energy_consumption', INTERVAL '3 years');
SELECT add_compression_policy('energy_consumption', INTERVAL '60 days');

-- 库存变动保留3年
SELECT add_retention_policy('inventory_movements', INTERVAL '3 years');
SELECT add_compression_policy('inventory_movements', INTERVAL '90 days');

-- 报警事件保留2年
SELECT add_retention_policy('alarm_events', INTERVAL '2 years');
SELECT add_compression_policy('alarm_events', INTERVAL '30 days');

-- 创建连续聚合视图
-- 每小时设备利用率
CREATE MATERIALIZED VIEW IF NOT EXISTS equipment_utilization_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    equipment_id,
    AVG(utilization) as avg_utilization,
    MAX(utilization) as max_utilization,
    MIN(utilization) as min_utilization,
    COUNT(*) as data_points
FROM equipment_status
GROUP BY hour, equipment_id;

-- 每日生产统计
CREATE MATERIALIZED VIEW IF NOT EXISTS production_daily_summary
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS day,
    production_line,
    SUM(quantity_produced) as total_produced,
    SUM(quantity_defective) as total_defective,
    AVG(oee) as avg_oee,
    AVG(availability) as avg_availability,
    AVG(performance) as avg_performance,
    AVG(quality_rate) as avg_quality_rate
FROM production_metrics
GROUP BY day, production_line;

-- 每小时能耗统计
CREATE MATERIALIZED VIEW IF NOT EXISTS energy_consumption_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    energy_type,
    location,
    SUM(consumption) as total_consumption,
    SUM(cost) as total_cost,
    SUM(carbon_footprint) as total_carbon_footprint
FROM energy_consumption
GROUP BY hour, energy_type, location;

-- 设置连续聚合刷新策略
SELECT add_continuous_aggregate_policy('equipment_utilization_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('production_daily_summary',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('energy_consumption_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

-- 创建实时数据视图
CREATE OR REPLACE VIEW latest_sensor_readings AS
SELECT DISTINCT ON (source_id, sensor_id)
    source_id,
    sensor_id,
    time,
    value,
    unit,
    quality
FROM sensor_data
ORDER BY source_id, sensor_id, time DESC;

CREATE OR REPLACE VIEW current_equipment_status AS
SELECT DISTINCT ON (equipment_id)
    equipment_id,
    time,
    status,
    utilization,
    temperature,
    vibration,
    power_consumption,
    cycle_count,
    error_code
FROM equipment_status
ORDER BY equipment_id, time DESC;

CREATE OR REPLACE VIEW active_alarms AS
SELECT 
    alarm_id,
    source_id,
    alarm_type,
    severity,
    message,
    time as created_at,
    acknowledged_by,
    acknowledged_at
FROM alarm_events
WHERE status = 'active'
ORDER BY severity DESC, time DESC;

-- 创建数据质量检查函数
CREATE OR REPLACE FUNCTION check_data_quality()
RETURNS TABLE(
    table_name TEXT,
    issue_type TEXT,
    issue_count BIGINT,
    check_time TIMESTAMPTZ
) AS $$
BEGIN
    -- 检查传感器数据质量
    RETURN QUERY
    SELECT 
        'sensor_data'::TEXT,
        'bad_quality'::TEXT,
        COUNT(*),
        NOW()
    FROM sensor_data 
    WHERE time >= NOW() - INTERVAL '1 hour' 
    AND quality != 'good';
    
    -- 检查设备状态数据缺失
    RETURN QUERY
    SELECT 
        'equipment_status'::TEXT,
        'missing_data'::TEXT,
        COUNT(DISTINCT equipment_id),
        NOW()
    FROM equipment_status e1
    WHERE NOT EXISTS (
        SELECT 1 FROM equipment_status e2 
        WHERE e2.equipment_id = e1.equipment_id 
        AND e2.time >= NOW() - INTERVAL '10 minutes'
    );
    
END;
$$ LANGUAGE plpgsql;
