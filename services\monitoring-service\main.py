"""
工业智能体平台 - 监控服务
实现系统监控、告警管理、性能分析等功能
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import asyncio
import asyncpg
import aioredis
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import json
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager
import psutil
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import uuid
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
db_pool = None
redis_client = None
kafka_producer = None
alert_manager = None

class AlertLevel(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertStatus(str, Enum):
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

class MetricType(str, Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool, redis_client, kafka_producer, alert_manager
    
    # 初始化数据库连接
    db_pool = await asyncpg.create_pool(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        user=os.getenv("DB_USER", "admin"),
        password=os.getenv("DB_PASSWORD", "admin123"),
        database=os.getenv("DB_NAME", "industry_platform"),
        min_size=5,
        max_size=20
    )
    
    # Redis连接
    redis_client = await aioredis.from_url(
        f"redis://{os.getenv('REDIS_HOST', 'localhost')}:{os.getenv('REDIS_PORT', '6379')}"
    )
    
    # Kafka生产者
    kafka_producer = AIOKafkaProducer(
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
    )
    await kafka_producer.start()
    
    # 初始化告警管理器
    alert_manager = AlertManager()
    
    # 初始化数据库表
    await initialize_database()
    
    # 启动监控任务
    asyncio.create_task(start_system_monitoring())
    asyncio.create_task(start_service_health_check())
    asyncio.create_task(start_alert_processor())
    
    logger.info("Monitoring service initialized")
    
    yield
    
    # 清理资源
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()
    if kafka_producer:
        await kafka_producer.stop()
    
    logger.info("Monitoring service shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="监控服务",
    description="系统监控、告警管理、性能分析",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic模型
class MetricData(BaseModel):
    name: str
    value: float
    metric_type: MetricType
    labels: Dict[str, str] = {}
    timestamp: Optional[datetime] = None

class AlertRule(BaseModel):
    name: str
    description: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    duration_seconds: int = 300  # 持续时间
    level: AlertLevel = AlertLevel.WARNING
    labels: Dict[str, str] = {}
    is_active: bool = True

class AlertCreate(BaseModel):
    rule_id: Optional[str] = None
    title: str
    description: str
    level: AlertLevel
    source: str
    labels: Dict[str, str] = {}
    annotations: Dict[str, str] = {}

class NotificationChannel(BaseModel):
    name: str
    type: str  # email, webhook, slack, dingtalk
    config: Dict[str, Any]
    is_active: bool = True

async def initialize_database():
    """初始化数据库表"""
    async with db_pool.acquire() as conn:
        # 系统指标表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS system_metrics (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                metric_name VARCHAR(100) NOT NULL,
                metric_value DECIMAL(15,6) NOT NULL,
                metric_type VARCHAR(20) NOT NULL,
                labels JSONB,
                timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 告警规则表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS alert_rules (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                rule_name VARCHAR(200) NOT NULL,
                description TEXT,
                metric_name VARCHAR(100) NOT NULL,
                condition VARCHAR(10) NOT NULL,
                threshold DECIMAL(15,6) NOT NULL,
                duration_seconds INTEGER DEFAULT 300,
                level VARCHAR(20) NOT NULL,
                labels JSONB,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 告警实例表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS alerts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                rule_id UUID REFERENCES alert_rules(id),
                title VARCHAR(200) NOT NULL,
                description TEXT,
                level VARCHAR(20) NOT NULL,
                status VARCHAR(20) DEFAULT 'active',
                source VARCHAR(100),
                labels JSONB,
                annotations JSONB,
                starts_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                ends_at TIMESTAMP WITH TIME ZONE,
                acknowledged_by UUID REFERENCES users(id),
                acknowledged_at TIMESTAMP WITH TIME ZONE,
                resolved_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 通知渠道表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS notification_channels (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                channel_name VARCHAR(100) NOT NULL,
                channel_type VARCHAR(50) NOT NULL,
                config JSONB NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 服务健康状态表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS service_health (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100) NOT NULL,
                status VARCHAR(20) NOT NULL, -- healthy, unhealthy, unknown
                response_time_ms INTEGER,
                error_message TEXT,
                last_check TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

class SystemMonitor:
    """系统监控器"""
    
    @staticmethod
    async def collect_system_metrics():
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            await SystemMonitor._save_metric("system_cpu_usage", cpu_percent, MetricType.GAUGE)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            await SystemMonitor._save_metric("system_memory_usage", memory.percent, MetricType.GAUGE)
            await SystemMonitor._save_metric("system_memory_available", memory.available, MetricType.GAUGE)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            await SystemMonitor._save_metric("system_disk_usage", disk_percent, MetricType.GAUGE)
            
            # 网络IO
            net_io = psutil.net_io_counters()
            await SystemMonitor._save_metric("system_network_bytes_sent", net_io.bytes_sent, MetricType.COUNTER)
            await SystemMonitor._save_metric("system_network_bytes_recv", net_io.bytes_recv, MetricType.COUNTER)
            
            # 进程数量
            process_count = len(psutil.pids())
            await SystemMonitor._save_metric("system_process_count", process_count, MetricType.GAUGE)
            
            logger.debug("System metrics collected")
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    @staticmethod
    async def _save_metric(name: str, value: float, metric_type: MetricType, labels: Dict[str, str] = None):
        """保存指标数据"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO system_metrics (metric_name, metric_value, metric_type, labels)
                    VALUES ($1, $2, $3, $4)
                    """,
                    name,
                    value,
                    metric_type.value,
                    json.dumps(labels) if labels else None
                )
            
            # 同时保存到Redis用于实时查询
            await redis_client.setex(f"metric:{name}", 300, value)
            
        except Exception as e:
            logger.error(f"Error saving metric {name}: {e}")

class ServiceHealthChecker:
    """服务健康检查器"""
    
    SERVICES = {
        "api-gateway": "http://localhost:8000/health",
        "auth-service": "http://localhost:8001/health",
        "llm-service": "http://localhost:8002/health",
        "agent-orchestrator": "http://localhost:8003/health",
        "production-planning": "http://localhost:8004/health",
        "maintenance-service": "http://localhost:8005/health",
        "quality-service": "http://localhost:8006/health",
        "supply-chain": "http://localhost:8007/health",
        "knowledge-service": "http://localhost:8008/health",
        "data-ingestion": "http://localhost:8009/health"
    }
    
    @staticmethod
    async def check_all_services():
        """检查所有服务健康状态"""
        for service_name, health_url in ServiceHealthChecker.SERVICES.items():
            await ServiceHealthChecker.check_service_health(service_name, health_url)
    
    @staticmethod
    async def check_service_health(service_name: str, health_url: str):
        """检查单个服务健康状态"""
        try:
            start_time = datetime.utcnow()
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(health_url)
                
            end_time = datetime.utcnow()
            response_time = int((end_time - start_time).total_seconds() * 1000)
            
            if response.status_code == 200:
                status = "healthy"
                error_message = None
            else:
                status = "unhealthy"
                error_message = f"HTTP {response.status_code}"
                
        except Exception as e:
            status = "unhealthy"
            error_message = str(e)
            response_time = None
        
        # 保存健康状态
        await ServiceHealthChecker._save_health_status(
            service_name, status, response_time, error_message
        )
        
        # 如果服务不健康，创建告警
        if status == "unhealthy":
            await alert_manager.create_alert(AlertCreate(
                title=f"服务 {service_name} 不健康",
                description=f"服务健康检查失败: {error_message}",
                level=AlertLevel.ERROR,
                source="health_checker",
                labels={"service": service_name},
                annotations={"error": error_message or "Unknown error"}
            ))
    
    @staticmethod
    async def _save_health_status(service_name: str, status: str, response_time: int = None, error_message: str = None):
        """保存服务健康状态"""
        try:
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO service_health 
                    (service_name, status, response_time_ms, error_message)
                    VALUES ($1, $2, $3, $4)
                    """,
                    service_name,
                    status,
                    response_time,
                    error_message
                )
                
        except Exception as e:
            logger.error(f"Error saving health status for {service_name}: {e}")

class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.notification_channels = []
    
    async def create_alert(self, alert_data: AlertCreate) -> str:
        """创建告警"""
        try:
            alert_id = str(uuid.uuid4())
            
            async with db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO alerts 
                    (id, rule_id, title, description, level, source, labels, annotations)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """,
                    alert_id,
                    alert_data.rule_id,
                    alert_data.title,
                    alert_data.description,
                    alert_data.level.value,
                    alert_data.source,
                    json.dumps(alert_data.labels),
                    json.dumps(alert_data.annotations)
                )
            
            # 发送通知
            await self._send_notifications(alert_data)
            
            # 发送事件
            await kafka_producer.send("monitoring_events", {
                "type": "alert_created",
                "alert_id": alert_id,
                "level": alert_data.level.value,
                "title": alert_data.title,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Created alert: {alert_id}")
            return alert_id
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _send_notifications(self, alert_data: AlertCreate):
        """发送告警通知"""
        try:
            # 获取通知渠道
            async with db_pool.acquire() as conn:
                channels = await conn.fetch(
                    "SELECT * FROM notification_channels WHERE is_active = true"
                )
            
            for channel in channels:
                await self._send_notification(channel, alert_data)
                
        except Exception as e:
            logger.error(f"Error sending notifications: {e}")
    
    async def _send_notification(self, channel: dict, alert_data: AlertCreate):
        """发送单个通知"""
        try:
            channel_type = channel['channel_type']
            config = channel['config']
            
            if channel_type == "email":
                await self._send_email_notification(config, alert_data)
            elif channel_type == "webhook":
                await self._send_webhook_notification(config, alert_data)
            # 可以添加更多通知类型
                
        except Exception as e:
            logger.error(f"Error sending notification via {channel['channel_name']}: {e}")
    
    async def _send_email_notification(self, config: dict, alert_data: AlertCreate):
        """发送邮件通知"""
        try:
            smtp_server = config.get("smtp_server")
            smtp_port = config.get("smtp_port", 587)
            username = config.get("username")
            password = config.get("password")
            to_emails = config.get("to_emails", [])
            
            if not all([smtp_server, username, password, to_emails]):
                logger.warning("Email configuration incomplete")
                return
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = username
            msg['To'] = ", ".join(to_emails)
            msg['Subject'] = f"[{alert_data.level.value.upper()}] {alert_data.title}"
            
            body = f"""
告警详情:
标题: {alert_data.title}
级别: {alert_data.level.value}
描述: {alert_data.description}
来源: {alert_data.source}
时间: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

标签: {json.dumps(alert_data.labels, ensure_ascii=False, indent=2)}
注释: {json.dumps(alert_data.annotations, ensure_ascii=False, indent=2)}
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email notification sent for alert: {alert_data.title}")
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
    
    async def _send_webhook_notification(self, config: dict, alert_data: AlertCreate):
        """发送Webhook通知"""
        try:
            webhook_url = config.get("url")
            if not webhook_url:
                return
            
            payload = {
                "title": alert_data.title,
                "description": alert_data.description,
                "level": alert_data.level.value,
                "source": alert_data.source,
                "labels": alert_data.labels,
                "annotations": alert_data.annotations,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload, timeout=10.0)
                
            if response.status_code == 200:
                logger.info(f"Webhook notification sent for alert: {alert_data.title}")
            else:
                logger.warning(f"Webhook notification failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error sending webhook notification: {e}")

# 后台任务
async def start_system_monitoring():
    """启动系统监控"""
    while True:
        try:
            await SystemMonitor.collect_system_metrics()
            await asyncio.sleep(60)  # 每分钟收集一次
        except Exception as e:
            logger.error(f"Error in system monitoring: {e}")
            await asyncio.sleep(60)

async def start_service_health_check():
    """启动服务健康检查"""
    while True:
        try:
            await ServiceHealthChecker.check_all_services()
            await asyncio.sleep(30)  # 每30秒检查一次
        except Exception as e:
            logger.error(f"Error in service health check: {e}")
            await asyncio.sleep(30)

async def start_alert_processor():
    """启动告警处理器"""
    consumer = AIOKafkaConsumer(
        "monitoring_events",
        bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        group_id="monitoring_service",
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))
    )
    
    await consumer.start()
    
    try:
        async for message in consumer:
            event_data = message.value
            await process_monitoring_event(event_data)
    except Exception as e:
        logger.error(f"Error in alert processor: {e}")
    finally:
        await consumer.stop()

async def process_monitoring_event(event_data: Dict[str, Any]):
    """处理监控事件"""
    try:
        event_type = event_data.get("type")
        
        if event_type == "metric_threshold_exceeded":
            # 处理指标阈值超出事件
            await handle_metric_threshold_event(event_data)
        elif event_type == "service_down":
            # 处理服务下线事件
            await handle_service_down_event(event_data)
        
    except Exception as e:
        logger.error(f"Error processing monitoring event: {e}")

async def handle_metric_threshold_event(event_data: Dict[str, Any]):
    """处理指标阈值事件"""
    metric_name = event_data.get("metric_name")
    current_value = event_data.get("current_value")
    threshold = event_data.get("threshold")
    
    await alert_manager.create_alert(AlertCreate(
        title=f"指标 {metric_name} 超出阈值",
        description=f"当前值 {current_value} 超出阈值 {threshold}",
        level=AlertLevel.WARNING,
        source="metric_monitor",
        labels={"metric": metric_name},
        annotations={"current_value": str(current_value), "threshold": str(threshold)}
    ))

async def handle_service_down_event(event_data: Dict[str, Any]):
    """处理服务下线事件"""
    service_name = event_data.get("service_name")
    
    await alert_manager.create_alert(AlertCreate(
        title=f"服务 {service_name} 下线",
        description=f"服务 {service_name} 无法访问",
        level=AlertLevel.CRITICAL,
        source="service_monitor",
        labels={"service": service_name}
    ))

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "monitoring-service"}

@app.get("/metrics/system")
async def get_system_metrics(hours: int = 1):
    """获取系统指标"""
    async with db_pool.acquire() as conn:
        metrics = await conn.fetch(
            """
            SELECT metric_name, metric_value, timestamp
            FROM system_metrics
            WHERE timestamp >= NOW() - INTERVAL '%s hours'
            ORDER BY timestamp DESC
            """,
            hours
        )
        
        return [dict(metric) for metric in metrics]

@app.get("/services/health")
async def get_services_health():
    """获取服务健康状态"""
    async with db_pool.acquire() as conn:
        services = await conn.fetch(
            """
            SELECT DISTINCT ON (service_name) 
                   service_name, status, response_time_ms, error_message, last_check
            FROM service_health
            ORDER BY service_name, last_check DESC
            """
        )
        
        return [dict(service) for service in services]

@app.get("/alerts")
async def get_alerts(
    status: Optional[AlertStatus] = None,
    level: Optional[AlertLevel] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取告警列表"""
    async with db_pool.acquire() as conn:
        query = "SELECT * FROM alerts WHERE 1=1"
        params = []
        param_count = 1
        
        if status:
            query += f" AND status = ${param_count}"
            params.append(status.value)
            param_count += 1
        
        if level:
            query += f" AND level = ${param_count}"
            params.append(level.value)
            param_count += 1
        
        query += f" ORDER BY created_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])
        
        alerts = await conn.fetch(query, *params)
        return [dict(alert) for alert in alerts]

@app.post("/alerts")
async def create_alert(alert_data: AlertCreate):
    """创建告警"""
    alert_id = await alert_manager.create_alert(alert_data)
    return {"message": "Alert created", "alert_id": alert_id}

@app.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str, acknowledged_by: str):
    """确认告警"""
    async with db_pool.acquire() as conn:
        await conn.execute(
            """
            UPDATE alerts 
            SET status = 'acknowledged', acknowledged_by = $1, acknowledged_at = $2
            WHERE id = $3
            """,
            acknowledged_by,
            datetime.utcnow(),
            alert_id
        )
    
    return {"message": "Alert acknowledged"}

@app.post("/notification-channels")
async def create_notification_channel(channel_data: NotificationChannel):
    """创建通知渠道"""
    channel_id = str(uuid.uuid4())
    
    async with db_pool.acquire() as conn:
        await conn.execute(
            """
            INSERT INTO notification_channels 
            (id, channel_name, channel_type, config, is_active)
            VALUES ($1, $2, $3, $4, $5)
            """,
            channel_id,
            channel_data.name,
            channel_data.type,
            json.dumps(channel_data.config),
            channel_data.is_active
        )
    
    return {"message": "Notification channel created", "channel_id": channel_id}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8010)
