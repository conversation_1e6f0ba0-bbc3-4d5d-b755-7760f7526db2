"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-upload";
exports.ids = ["vendor-chunks/rc-upload"];
exports.modules = {

/***/ "(ssr)/../node_modules/rc-upload/es/AjaxUploader.js":
/*!****************************************************!*\
  !*** ../node_modules/rc-upload/es/AjaxUploader.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/regeneratorRuntime */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/asyncToGenerator */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! classnames */ \"(ssr)/../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/../node_modules/rc-util/es/pickAttrs.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _attr_accept__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./attr-accept */ \"(ssr)/../node_modules/rc-upload/es/attr-accept.js\");\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./request */ \"(ssr)/../node_modules/rc-upload/es/request.js\");\n/* harmony import */ var _traverseFileTree__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./traverseFileTree */ \"(ssr)/../node_modules/rc-upload/es/traverseFileTree.js\");\n/* harmony import */ var _uid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./uid */ \"(ssr)/../node_modules/rc-upload/es/uid.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"prefixCls\", \"className\", \"classNames\", \"disabled\", \"id\", \"name\", \"style\", \"styles\", \"multiple\", \"accept\", \"capture\", \"children\", \"directory\", \"openFileDialogOnClick\", \"onMouseEnter\", \"onMouseLeave\", \"hasControlInside\"];\n/* eslint react/no-is-mounted:0,react/sort-comp:0,react/prop-types:0 */\n\n\n\n\n\n\n\nvar AjaxUploader = /*#__PURE__*/function (_Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(AjaxUploader, _Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(AjaxUploader);\n  function AjaxUploader() {\n    var _this;\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(this, AjaxUploader);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"state\", {\n      uid: (0,_uid__WEBPACK_IMPORTED_MODULE_19__[\"default\"])()\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"reqs\", {});\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"fileInput\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"_isMounted\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onChange\", function (e) {\n      var _this$props = _this.props,\n        accept = _this$props.accept,\n        directory = _this$props.directory;\n      var files = e.target.files;\n      var acceptedFiles = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(files).filter(function (file) {\n        return !directory || (0,_attr_accept__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(file, accept);\n      });\n      _this.uploadFiles(acceptedFiles);\n      _this.reset();\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onClick\", function (event) {\n      var el = _this.fileInput;\n      if (!el) {\n        return;\n      }\n      var target = event.target;\n      var onClick = _this.props.onClick;\n      if (target && target.tagName === 'BUTTON') {\n        var parent = el.parentNode;\n        parent.focus();\n        target.blur();\n      }\n      el.click();\n      if (onClick) {\n        onClick(event);\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onKeyDown\", function (e) {\n      if (e.key === 'Enter') {\n        _this.onClick(e);\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onDataTransferFiles\", /*#__PURE__*/function () {\n      var _ref = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_5__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().mark(function _callee(dataTransfer, existFileCallback) {\n        var _this$props2, multiple, accept, directory, items, files, acceptFiles;\n        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this$props2 = _this.props, multiple = _this$props2.multiple, accept = _this$props2.accept, directory = _this$props2.directory;\n              items = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dataTransfer.items || []);\n              files = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dataTransfer.files || []);\n              if (files.length > 0 || items.some(function (item) {\n                return item.kind === 'file';\n              })) {\n                existFileCallback === null || existFileCallback === void 0 || existFileCallback();\n              }\n              if (!directory) {\n                _context.next = 11;\n                break;\n              }\n              _context.next = 7;\n              return (0,_traverseFileTree__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(Array.prototype.slice.call(items), function (_file) {\n                return (0,_attr_accept__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(_file, _this.props.accept);\n              });\n            case 7:\n              files = _context.sent;\n              _this.uploadFiles(files);\n              _context.next = 14;\n              break;\n            case 11:\n              acceptFiles = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(files).filter(function (file) {\n                return (0,_attr_accept__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(file, accept);\n              });\n              if (multiple === false) {\n                acceptFiles = files.slice(0, 1);\n              }\n              _this.uploadFiles(acceptFiles);\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onFilePaste\", /*#__PURE__*/function () {\n      var _ref2 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_5__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().mark(function _callee2(e) {\n        var pastable, clipboardData;\n        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              pastable = _this.props.pastable;\n              if (pastable) {\n                _context2.next = 3;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 3:\n              if (!(e.type === 'paste')) {\n                _context2.next = 6;\n                break;\n              }\n              clipboardData = e.clipboardData;\n              return _context2.abrupt(\"return\", _this.onDataTransferFiles(clipboardData, function () {\n                e.preventDefault();\n              }));\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function (_x3) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onFileDragOver\", function (e) {\n      e.preventDefault();\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"onFileDrop\", /*#__PURE__*/function () {\n      var _ref3 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_5__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().mark(function _callee3(e) {\n        var dataTransfer;\n        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              e.preventDefault();\n              if (!(e.type === 'drop')) {\n                _context3.next = 4;\n                break;\n              }\n              dataTransfer = e.dataTransfer;\n              return _context3.abrupt(\"return\", _this.onDataTransferFiles(dataTransfer));\n            case 4:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function (_x4) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"uploadFiles\", function (files) {\n      var originFiles = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(files);\n      var postFiles = originFiles.map(function (file) {\n        // eslint-disable-next-line no-param-reassign\n        file.uid = (0,_uid__WEBPACK_IMPORTED_MODULE_19__[\"default\"])();\n        return _this.processFile(file, originFiles);\n      });\n\n      // Batch upload files\n      Promise.all(postFiles).then(function (fileList) {\n        var onBatchStart = _this.props.onBatchStart;\n        onBatchStart === null || onBatchStart === void 0 || onBatchStart(fileList.map(function (_ref4) {\n          var origin = _ref4.origin,\n            parsedFile = _ref4.parsedFile;\n          return {\n            file: origin,\n            parsedFile: parsedFile\n          };\n        }));\n        fileList.filter(function (file) {\n          return file.parsedFile !== null;\n        }).forEach(function (file) {\n          _this.post(file);\n        });\n      });\n    });\n    /**\n     * Process file before upload. When all the file is ready, we start upload.\n     */\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"processFile\", /*#__PURE__*/function () {\n      var _ref5 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_5__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().mark(function _callee4(file, fileList) {\n        var beforeUpload, transformedFile, action, mergedAction, data, mergedData, parsedData, parsedFile, mergedParsedFile;\n        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              beforeUpload = _this.props.beforeUpload;\n              transformedFile = file;\n              if (!beforeUpload) {\n                _context4.next = 14;\n                break;\n              }\n              _context4.prev = 3;\n              _context4.next = 6;\n              return beforeUpload(file, fileList);\n            case 6:\n              transformedFile = _context4.sent;\n              _context4.next = 12;\n              break;\n            case 9:\n              _context4.prev = 9;\n              _context4.t0 = _context4[\"catch\"](3);\n              // Rejection will also trade as false\n              transformedFile = false;\n            case 12:\n              if (!(transformedFile === false)) {\n                _context4.next = 14;\n                break;\n              }\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                parsedFile: null,\n                action: null,\n                data: null\n              });\n            case 14:\n              // Get latest action\n              action = _this.props.action;\n              if (!(typeof action === 'function')) {\n                _context4.next = 21;\n                break;\n              }\n              _context4.next = 18;\n              return action(file);\n            case 18:\n              mergedAction = _context4.sent;\n              _context4.next = 22;\n              break;\n            case 21:\n              mergedAction = action;\n            case 22:\n              // Get latest data\n              data = _this.props.data;\n              if (!(typeof data === 'function')) {\n                _context4.next = 29;\n                break;\n              }\n              _context4.next = 26;\n              return data(file);\n            case 26:\n              mergedData = _context4.sent;\n              _context4.next = 30;\n              break;\n            case 29:\n              mergedData = data;\n            case 30:\n              parsedData =\n              // string type is from legacy `transformFile`.\n              // Not sure if this will work since no related test case works with it\n              ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(transformedFile) === 'object' || typeof transformedFile === 'string') && transformedFile ? transformedFile : file;\n              if (parsedData instanceof File) {\n                parsedFile = parsedData;\n              } else {\n                parsedFile = new File([parsedData], file.name, {\n                  type: file.type\n                });\n              }\n              mergedParsedFile = parsedFile;\n              mergedParsedFile.uid = file.uid;\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                data: mergedData,\n                parsedFile: mergedParsedFile,\n                action: mergedAction\n              });\n            case 35:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[3, 9]]);\n      }));\n      return function (_x5, _x6) {\n        return _ref5.apply(this, arguments);\n      };\n    }());\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(_this), \"saveFileInput\", function (node) {\n      _this.fileInput = node;\n    });\n    return _this;\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(AjaxUploader, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._isMounted = true;\n      var pastable = this.props.pastable;\n      if (pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      this.abort();\n      document.removeEventListener('paste', this.onFilePaste);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var pastable = this.props.pastable;\n      if (pastable && !prevProps.pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      } else if (!pastable && prevProps.pastable) {\n        document.removeEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"post\",\n    value: function post(_ref6) {\n      var _this2 = this;\n      var data = _ref6.data,\n        origin = _ref6.origin,\n        action = _ref6.action,\n        parsedFile = _ref6.parsedFile;\n      if (!this._isMounted) {\n        return;\n      }\n      var _this$props3 = this.props,\n        onStart = _this$props3.onStart,\n        customRequest = _this$props3.customRequest,\n        name = _this$props3.name,\n        headers = _this$props3.headers,\n        withCredentials = _this$props3.withCredentials,\n        method = _this$props3.method;\n      var uid = origin.uid;\n      var request = customRequest || _request__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n      var requestOption = {\n        action: action,\n        filename: name,\n        data: data,\n        file: parsedFile,\n        headers: headers,\n        withCredentials: withCredentials,\n        method: method || 'post',\n        onProgress: function onProgress(e) {\n          var onProgress = _this2.props.onProgress;\n          onProgress === null || onProgress === void 0 || onProgress(e, parsedFile);\n        },\n        onSuccess: function onSuccess(ret, xhr) {\n          var onSuccess = _this2.props.onSuccess;\n          onSuccess === null || onSuccess === void 0 || onSuccess(ret, parsedFile, xhr);\n          delete _this2.reqs[uid];\n        },\n        onError: function onError(err, ret) {\n          var onError = _this2.props.onError;\n          onError === null || onError === void 0 || onError(err, ret, parsedFile);\n          delete _this2.reqs[uid];\n        }\n      };\n      onStart(origin);\n      this.reqs[uid] = request(requestOption);\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.setState({\n        uid: (0,_uid__WEBPACK_IMPORTED_MODULE_19__[\"default\"])()\n      });\n    }\n  }, {\n    key: \"abort\",\n    value: function abort(file) {\n      var reqs = this.reqs;\n      if (file) {\n        var uid = file.uid ? file.uid : file;\n        if (reqs[uid] && reqs[uid].abort) {\n          reqs[uid].abort();\n        }\n        delete reqs[uid];\n      } else {\n        Object.keys(reqs).forEach(function (uid) {\n          if (reqs[uid] && reqs[uid].abort) {\n            reqs[uid].abort();\n          }\n          delete reqs[uid];\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        Tag = _this$props4.component,\n        prefixCls = _this$props4.prefixCls,\n        className = _this$props4.className,\n        _this$props4$classNam = _this$props4.classNames,\n        classNames = _this$props4$classNam === void 0 ? {} : _this$props4$classNam,\n        disabled = _this$props4.disabled,\n        id = _this$props4.id,\n        name = _this$props4.name,\n        style = _this$props4.style,\n        _this$props4$styles = _this$props4.styles,\n        styles = _this$props4$styles === void 0 ? {} : _this$props4$styles,\n        multiple = _this$props4.multiple,\n        accept = _this$props4.accept,\n        capture = _this$props4.capture,\n        children = _this$props4.children,\n        directory = _this$props4.directory,\n        openFileDialogOnClick = _this$props4.openFileDialogOnClick,\n        onMouseEnter = _this$props4.onMouseEnter,\n        onMouseLeave = _this$props4.onMouseLeave,\n        hasControlInside = _this$props4.hasControlInside,\n        otherProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_this$props4, _excluded);\n      var cls = classnames__WEBPACK_IMPORTED_MODULE_13___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_12__[\"default\"])({}, prefixCls, true), \"\".concat(prefixCls, \"-disabled\"), disabled), className, className));\n      // because input don't have directory/webkitdirectory type declaration\n      var dirProps = directory ? {\n        directory: 'directory',\n        webkitdirectory: 'webkitdirectory'\n      } : {};\n      var events = disabled ? {} : {\n        onClick: openFileDialogOnClick ? this.onClick : function () {},\n        onKeyDown: openFileDialogOnClick ? this.onKeyDown : function () {},\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onDrop: this.onFileDrop,\n        onDragOver: this.onFileDragOver,\n        tabIndex: hasControlInside ? undefined : '0'\n      };\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_15___default().createElement(Tag, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, events, {\n        className: cls,\n        role: hasControlInside ? undefined : 'button',\n        style: style\n      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_15___default().createElement(\"input\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(otherProps, {\n        aria: true,\n        data: true\n      }), {\n        id: id\n        /**\n         * https://github.com/ant-design/ant-design/issues/50643,\n         * https://github.com/react-component/upload/pull/575#issuecomment-2320646552\n         */,\n        name: name,\n        disabled: disabled,\n        type: \"file\",\n        ref: this.saveFileInput,\n        onClick: function onClick(e) {\n          return e.stopPropagation();\n        } // https://github.com/ant-design/ant-design/issues/19948\n        ,\n        key: this.state.uid,\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          display: 'none'\n        }, styles.input),\n        className: classNames.input,\n        accept: accept\n      }, dirProps, {\n        multiple: multiple,\n        onChange: this.onChange\n      }, capture != null ? {\n        capture: capture\n      } : {})), children);\n    }\n  }]);\n  return AjaxUploader;\n}(react__WEBPACK_IMPORTED_MODULE_15__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AjaxUploader);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/AjaxUploader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/Upload.js":
/*!**********************************************!*\
  !*** ../node_modules/rc-upload/es/Upload.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _AjaxUploader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AjaxUploader */ \"(ssr)/../node_modules/rc-upload/es/AjaxUploader.js\");\n\n\n\n\n\n\n\n/* eslint react/prop-types:0 */\n\n\nfunction empty() {}\nvar Upload = /*#__PURE__*/function (_Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(Upload, _Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Upload);\n  function Upload() {\n    var _this;\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, Upload);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_this), \"uploader\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_this), \"saveUploader\", function (node) {\n      _this.uploader = node;\n    });\n    return _this;\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Upload, [{\n    key: \"abort\",\n    value: function abort(file) {\n      this.uploader.abort(file);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().createElement(_AjaxUploader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.props, {\n        ref: this.saveUploader\n      }));\n    }\n  }]);\n  return Upload;\n}(react__WEBPACK_IMPORTED_MODULE_7__.Component);\n(0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Upload, \"defaultProps\", {\n  component: 'span',\n  prefixCls: 'rc-upload',\n  data: {},\n  headers: {},\n  name: 'file',\n  multipart: false,\n  onStart: empty,\n  onError: empty,\n  onSuccess: empty,\n  multiple: false,\n  beforeUpload: null,\n  customRequest: null,\n  withCredentials: false,\n  openFileDialogOnClick: true,\n  hasControlInside: false\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Upload);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/Upload.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/attr-accept.js":
/*!***************************************************!*\
  !*** ../node_modules/rc-upload/es/attr-accept.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/../node_modules/rc-util/es/warning.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n    var fileName = file.name || '';\n    var mimeType = file.type || '';\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim();\n      // This is something like */*,*  allow all files\n      if (/^\\*(\\/\\*)?$/.test(type)) {\n        return true;\n      }\n\n      // like .jpg, .png\n      if (validType.charAt(0) === '.') {\n        var lowerFileName = fileName.toLowerCase();\n        var lowerType = validType.toLowerCase();\n        var affixList = [lowerType];\n        if (lowerType === '.jpg' || lowerType === '.jpeg') {\n          affixList = ['.jpg', '.jpeg'];\n        }\n        return affixList.some(function (affix) {\n          return lowerFileName.endsWith(affix);\n        });\n      }\n\n      // This is something like a image/* mime type\n      if (/\\/\\*$/.test(validType)) {\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      // Full match\n      if (mimeType === validType) {\n        return true;\n      }\n\n      // Invalidate type should skip\n      if (/^\\w+$/.test(validType)) {\n        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, \"Upload takes an invalidate 'accept' type '\".concat(validType, \"'.Skip for check.\"));\n        return true;\n      }\n      return false;\n    });\n  }\n  return true;\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/attr-accept.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/index.js":
/*!*********************************************!*\
  !*** ../node_modules/rc-upload/es/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Upload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Upload */ \"(ssr)/../node_modules/rc-upload/es/Upload.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Upload__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLXVwbG9hZC9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QjtBQUM5QixpRUFBZSwrQ0FBTSIsInNvdXJjZXMiOlsiSjpcXGF1Z21lbnRcXGluZHVzdHJ5LWFpLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXHJjLXVwbG9hZFxcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBVcGxvYWQgZnJvbSBcIi4vVXBsb2FkXCI7XG5leHBvcnQgZGVmYXVsdCBVcGxvYWQ7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/request.js":
/*!***********************************************!*\
  !*** ../node_modules/rc-upload/es/request.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ upload)\n/* harmony export */ });\nfunction getError(option, xhr) {\n  var msg = \"cannot \".concat(option.method, \" \").concat(option.action, \" \").concat(xhr.status, \"'\");\n  var err = new Error(msg);\n  err.status = xhr.status;\n  err.method = option.method;\n  err.url = option.action;\n  return err;\n}\nfunction getBody(xhr) {\n  var text = xhr.responseText || xhr.response;\n  if (!text) {\n    return text;\n  }\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\nfunction upload(option) {\n  // eslint-disable-next-line no-undef\n  var xhr = new XMLHttpRequest();\n  if (option.onProgress && xhr.upload) {\n    xhr.upload.onprogress = function progress(e) {\n      if (e.total > 0) {\n        e.percent = e.loaded / e.total * 100;\n      }\n      option.onProgress(e);\n    };\n  }\n\n  // eslint-disable-next-line no-undef\n  var formData = new FormData();\n  if (option.data) {\n    Object.keys(option.data).forEach(function (key) {\n      var value = option.data[key];\n      // support key-value array data\n      if (Array.isArray(value)) {\n        value.forEach(function (item) {\n          // { list: [ 11, 22 ] }\n          // formData.append('list[]', 11);\n          formData.append(\"\".concat(key, \"[]\"), item);\n        });\n        return;\n      }\n      formData.append(key, value);\n    });\n  }\n\n  // eslint-disable-next-line no-undef\n  if (option.file instanceof Blob) {\n    formData.append(option.filename, option.file, option.file.name);\n  } else {\n    formData.append(option.filename, option.file);\n  }\n  xhr.onerror = function error(e) {\n    option.onError(e);\n  };\n  xhr.onload = function onload() {\n    // allow success when 2xx status\n    // see https://github.com/react-component/upload/issues/34\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(option, xhr), getBody(xhr));\n    }\n    return option.onSuccess(getBody(xhr), xhr);\n  };\n  xhr.open(option.method, option.action, true);\n\n  // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true;\n  }\n  var headers = option.headers || {};\n\n  // when set headers['X-Requested-With'] = null , can close default XHR header\n  // see https://github.com/react-component/upload/issues/33\n  if (headers['X-Requested-With'] !== null) {\n    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n  }\n  Object.keys(headers).forEach(function (h) {\n    if (headers[h] !== null) {\n      xhr.setRequestHeader(h, headers[h]);\n    }\n  });\n  xhr.send(formData);\n  return {\n    abort: function abort() {\n      xhr.abort();\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/request.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/traverseFileTree.js":
/*!********************************************************!*\
  !*** ../node_modules/rc-upload/es/traverseFileTree.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/regeneratorRuntime */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/asyncToGenerator */ \"(ssr)/../node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n\n\n\n// https://github.com/ant-design/ant-design/issues/50080\nvar traverseFileTree = /*#__PURE__*/function () {\n  var _ref = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().mark(function _callee4(files, isAccepted) {\n    var flattenFileList, progressFileList, readDirectory, _readDirectory, readFile, _readFile, _traverseFileTree, wipIndex;\n    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _readFile = function _readFile3() {\n            _readFile = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().mark(function _callee3(item) {\n              return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().wrap(function _callee3$(_context3) {\n                while (1) switch (_context3.prev = _context3.next) {\n                  case 0:\n                    return _context3.abrupt(\"return\", new Promise(function (reslove) {\n                      item.file(function (file) {\n                        if (isAccepted(file)) {\n                          // https://github.com/ant-design/ant-design/issues/16426\n                          if (item.fullPath && !file.webkitRelativePath) {\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: true\n                              }\n                            });\n                            // eslint-disable-next-line no-param-reassign\n                            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n                            Object.defineProperties(file, {\n                              webkitRelativePath: {\n                                writable: false\n                              }\n                            });\n                          }\n                          reslove(file);\n                        } else {\n                          reslove(null);\n                        }\n                      });\n                    }));\n                  case 1:\n                  case \"end\":\n                    return _context3.stop();\n                }\n              }, _callee3);\n            }));\n            return _readFile.apply(this, arguments);\n          };\n          readFile = function _readFile2(_x4) {\n            return _readFile.apply(this, arguments);\n          };\n          _readDirectory = function _readDirectory3() {\n            _readDirectory = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().mark(function _callee2(directory) {\n              var dirReader, entries, results, n, i;\n              return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().wrap(function _callee2$(_context2) {\n                while (1) switch (_context2.prev = _context2.next) {\n                  case 0:\n                    dirReader = directory.createReader();\n                    entries = [];\n                  case 2:\n                    if (false) {}\n                    _context2.next = 5;\n                    return new Promise(function (resolve) {\n                      dirReader.readEntries(resolve, function () {\n                        return resolve([]);\n                      });\n                    });\n                  case 5:\n                    results = _context2.sent;\n                    n = results.length;\n                    if (n) {\n                      _context2.next = 9;\n                      break;\n                    }\n                    return _context2.abrupt(\"break\", 12);\n                  case 9:\n                    for (i = 0; i < n; i++) {\n                      entries.push(results[i]);\n                    }\n                    _context2.next = 2;\n                    break;\n                  case 12:\n                    return _context2.abrupt(\"return\", entries);\n                  case 13:\n                  case \"end\":\n                    return _context2.stop();\n                }\n              }, _callee2);\n            }));\n            return _readDirectory.apply(this, arguments);\n          };\n          readDirectory = function _readDirectory2(_x3) {\n            return _readDirectory.apply(this, arguments);\n          };\n          flattenFileList = [];\n          progressFileList = [];\n          files.forEach(function (file) {\n            return progressFileList.push(file.webkitGetAsEntry());\n          });\n\n          // eslint-disable-next-line @typescript-eslint/naming-convention\n          _traverseFileTree = /*#__PURE__*/function () {\n            var _ref2 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"])( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().mark(function _callee(item, path) {\n              var _file, entries;\n              return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    if (item) {\n                      _context.next = 2;\n                      break;\n                    }\n                    return _context.abrupt(\"return\");\n                  case 2:\n                    // eslint-disable-next-line no-param-reassign\n                    item.path = path || '';\n                    if (!item.isFile) {\n                      _context.next = 10;\n                      break;\n                    }\n                    _context.next = 6;\n                    return readFile(item);\n                  case 6:\n                    _file = _context.sent;\n                    if (_file) {\n                      flattenFileList.push(_file);\n                    }\n                    _context.next = 15;\n                    break;\n                  case 10:\n                    if (!item.isDirectory) {\n                      _context.next = 15;\n                      break;\n                    }\n                    _context.next = 13;\n                    return readDirectory(item);\n                  case 13:\n                    entries = _context.sent;\n                    progressFileList.push.apply(progressFileList, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(entries));\n                  case 15:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            }));\n            return function _traverseFileTree(_x5, _x6) {\n              return _ref2.apply(this, arguments);\n            };\n          }();\n          wipIndex = 0;\n        case 9:\n          if (!(wipIndex < progressFileList.length)) {\n            _context4.next = 15;\n            break;\n          }\n          _context4.next = 12;\n          return _traverseFileTree(progressFileList[wipIndex]);\n        case 12:\n          wipIndex++;\n          _context4.next = 9;\n          break;\n        case 15:\n          return _context4.abrupt(\"return\", flattenFileList);\n        case 16:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return function traverseFileTree(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (traverseFileTree);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/traverseFileTree.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/rc-upload/es/uid.js":
/*!*******************************************!*\
  !*** ../node_modules/rc-upload/es/uid.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ uid)\n/* harmony export */ });\nvar now = +new Date();\nvar index = 0;\nfunction uid() {\n  // eslint-disable-next-line no-plusplus\n  return \"rc-upload-\".concat(now, \"-\").concat(++index);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3JjLXVwbG9hZC9lcy91aWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDZTtBQUNmO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiSjpcXGF1Z21lbnRcXGluZHVzdHJ5LWFpLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXHJjLXVwbG9hZFxcZXNcXHVpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbm93ID0gK25ldyBEYXRlKCk7XG52YXIgaW5kZXggPSAwO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdWlkKCkge1xuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcGx1c3BsdXNcbiAgcmV0dXJuIFwicmMtdXBsb2FkLVwiLmNvbmNhdChub3csIFwiLVwiKS5jb25jYXQoKytpbmRleCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rc-upload/es/uid.js\n");

/***/ })

};
;